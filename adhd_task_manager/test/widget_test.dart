// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:adhd_task_manager/main.dart';

void main() {
  testWidgets('App loads successfully', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: ADHDTaskManagerApp()));

    // Wait for initial frame
    await tester.pump();

    // Verify that the app loads without crashing
    expect(find.byType(MaterialApp), findsOneWidget);

    // Wait a bit more for any immediate loading
    await tester.pump(const Duration(seconds: 1));

    // Verify that we can find some basic UI elements
    // This is a basic smoke test to ensure the app starts
    expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
  });
}
