["/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]