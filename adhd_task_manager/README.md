# إنجاز - مدير المهام لمرضى ADHD

تطبيق Flutter مصمم خصيصاً لمساعدة مرضى ADHD في إدارة مهامهم وتحسين تركيزهم وإنتاجيتهم.

## الميزات الحالية (السبرنت الأول)

### ✅ تم إنجازه
- إعداد مشروع Flutter مع البنية المعمارية المطلوبة
- تصميم 5 شاشات أساسية:
  - **لوحة القيادة**: عرض الإحصائيات والملخصات
  - **قائمة المهام**: إدارة المهام مع إمكانية الإضافة والتعديل
  - **مؤقت التركيز**: مؤقت Pomodoro للتركيز
  - **تتبع العادات**: متابعة العادات اليومية
  - **الإعدادات**: تخصيص التطبيق
- نظام تنقل سلس باستخدام BottomNavigationBar
- تصميم بصري نظيف ومهدئ مع ألوان مناسبة لمرضى ADHD
- دعم اللغة العربية

### 🎨 التصميم
- ألوان مهدئة ومريحة للعين
- واجهة مستخدم بسيطة وغير مشتتة
- تصميم متجاوب يعمل على جميع الأجهزة

## البنية المعمارية

```
lib/
├── core/
│   ├── database/
│   ├── services/
│   └── utils/
├── features/
│   ├── tasks/
│   ├── focus_timer/
│   ├── habits/
│   ├── phone_usage/
│   └── settings/
├── shared/
│   ├── widgets/
│   ├── themes/
│   └── constants/
├── screens/
└── main.dart
```

## كيفية التشغيل

1. تأكد من تثبيت Flutter على نظامك
2. انتقل إلى مجلد المشروع:
   ```bash
   cd adhd_task_manager
   ```
3. احصل على التبعيات:
   ```bash
   flutter pub get
   ```
4. شغل التطبيق:
   ```bash
   flutter run
   ```

## السبرنتات القادمة

### السبرنت الثاني (قادم)
- إعداد قاعدة البيانات المحلية (SQLite)
- تنفيذ نظام CRUD للمهام
- إضافة نظام الأولويات
- تكامل إدارة الحالة (Riverpod)

### السبرنت الثالث (قادم)
- تحسين واجهة قائمة المهام
- نظام تسجيل إكمال المهام
- إحصائيات أساسية للمهام

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **Material Design 3**: نظام التصميم
- **SQLite** (قادم): قاعدة البيانات المحلية
- **Riverpod** (قادم): إدارة الحالة

## المساهمة

هذا المشروع في مرحلة التطوير النشط. المساهمات مرحب بها!

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
