[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: armeabi-v7a", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/4o3s4613/armeabi-v7a/android_gradle_build.json due to:", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/4o3s4613/armeabi-v7a'", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/4o3s4613/armeabi-v7a'", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=21 \\\n  -DANDROID_PLATFORM=android-21 \\\n  -DANDROID_ABI=armeabi-v7a \\\n  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/cxx/Debug/4o3s4613/obj/armeabi-v7a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/cxx/Debug/4o3s4613/obj/armeabi-v7a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/4o3s4613/armeabi-v7a \\\n  -GNinja \\\n  -Wno-dev \\\n  --no-warn-unused-cli\n", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=21 \\\n  -DANDROID_PLATFORM=android-21 \\\n  -DANDROID_ABI=armeabi-v7a \\\n  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/cxx/Debug/4o3s4613/obj/armeabi-v7a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/build/app/intermediates/cxx/Debug/4o3s4613/obj/armeabi-v7a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/4o3s4613/armeabi-v7a \\\n  -GNinja \\\n  -Wno-dev \\\n  --no-warn-unused-cli\n", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/4o3s4613/armeabi-v7a/compile_commands.json.bin existed but not /Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/4o3s4613/armeabi-v7a/compile_commands.json", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]