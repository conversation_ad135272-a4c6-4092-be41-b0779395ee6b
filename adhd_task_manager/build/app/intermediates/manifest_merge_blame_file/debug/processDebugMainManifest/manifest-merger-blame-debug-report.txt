1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.adhd_task_manager"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/debug/AndroidManifest.xml:6:5-66
15-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/debug/AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:39:5-44:15
24        <intent>
24-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:41:13-72
25-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:42:13-50
27-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <permission
31-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
32        android:name="com.example.adhd_task_manager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.example.adhd_task_manager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
36
37    <application
38        android:name="android.app.Application"
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="true"
42        android:icon="@mipmap/ic_launcher"
43        android:label="adhd_task_manager" >
44        <activity
45            android:name="com.example.adhd_task_manager.MainActivity"
46            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
47            android:exported="true"
48            android:hardwareAccelerated="true"
49            android:launchMode="singleTop"
50            android:taskAffinity=""
51            android:theme="@style/LaunchTheme"
52            android:windowSoftInputMode="adjustResize" >
53
54            <!--
55                 Specifies an Android theme to apply to this Activity as soon as
56                 the Android process has started. This theme is visible to the user
57                 while the Flutter UI initializes. After that, this theme continues
58                 to determine the Window background behind the Flutter UI.
59            -->
60            <meta-data
61                android:name="io.flutter.embedding.android.NormalTheme"
62                android:resource="@style/NormalTheme" />
63
64            <intent-filter>
65                <action android:name="android.intent.action.MAIN" />
66
67                <category android:name="android.intent.category.LAUNCHER" />
68            </intent-filter>
69        </activity>
70        <!--
71             Don't delete the meta-data below.
72             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
73        -->
74        <meta-data
75            android:name="flutterEmbedding"
76            android:value="2" />
77
78        <uses-library
78-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
79            android:name="androidx.window.extensions"
79-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
80            android:required="false" />
80-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
81        <uses-library
81-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
82            android:name="androidx.window.sidecar"
82-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
83            android:required="false" />
83-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
84
85        <provider
85-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
86            android:name="androidx.startup.InitializationProvider"
86-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
87            android:authorities="com.example.adhd_task_manager.androidx-startup"
87-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
88            android:exported="false" >
88-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
89            <meta-data
89-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
90                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
90-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
91                android:value="androidx.startup" />
91-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
92            <meta-data
92-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
93                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
94                android:value="androidx.startup" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
95        </provider>
96
97        <receiver
97-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
98            android:name="androidx.profileinstaller.ProfileInstallReceiver"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
99            android:directBootAware="false"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
100            android:enabled="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
101            android:exported="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
102            android:permission="android.permission.DUMP" >
102-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
104                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
107                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
110                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
113                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
114            </intent-filter>
115        </receiver>
116    </application>
117
118</manifest>
