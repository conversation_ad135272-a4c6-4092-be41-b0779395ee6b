1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.adhd_task_manager"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/debug/AndroidManifest.xml:6:5-66
15-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/debug/AndroidManifest.xml:6:22-64
16    <!-- Permissions for notifications -->
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:3:5-66
17-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:3:22-63
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:4:5-81
18-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:4:22-78
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:5:5-68
19-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:5:22-65
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
20-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:6:5-77
20-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:6:22-74
21    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
21-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:7:5-79
21-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:7:22-76
22    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
22-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:8:5-74
22-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:8:22-71
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:59:5-64:15
31        <intent>
31-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:60:9-63:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:61:13-72
32-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:61:21-70
33
34            <data android:mimeType="text/plain" />
34-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:62:13-50
34-->/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/src/main/AndroidManifest.xml:62:19-48
35        </intent>
36    </queries>
37
38    <permission
38-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
39        android:name="com.example.adhd_task_manager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.example.adhd_task_manager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
43
44    <application
45        android:name="android.app.Application"
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="true"
49        android:icon="@mipmap/ic_launcher"
50        android:label="adhd_task_manager" >
51        <activity
52            android:name="com.example.adhd_task_manager.MainActivity"
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
54            android:exported="true"
55            android:hardwareAccelerated="true"
56            android:launchMode="singleTop"
57            android:taskAffinity=""
58            android:theme="@style/LaunchTheme"
59            android:windowSoftInputMode="adjustResize" >
60
61            <!--
62                 Specifies an Android theme to apply to this Activity as soon as
63                 the Android process has started. This theme is visible to the user
64                 while the Flutter UI initializes. After that, this theme continues
65                 to determine the Window background behind the Flutter UI.
66            -->
67            <meta-data
68                android:name="io.flutter.embedding.android.NormalTheme"
69                android:resource="@style/NormalTheme" />
70
71            <intent-filter>
72                <action android:name="android.intent.action.MAIN" />
73
74                <category android:name="android.intent.category.LAUNCHER" />
75            </intent-filter>
76        </activity>
77
78        <!-- Notification receiver -->
79        <receiver
80            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
81            android:exported="false" />
82        <receiver
83            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
84            android:exported="false" >
85            <intent-filter>
86                <action android:name="android.intent.action.BOOT_COMPLETED" />
87                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
88                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
89
90                <category android:name="android.intent.category.DEFAULT" />
91            </intent-filter>
92        </receiver>
93
94        <!--
95             Don't delete the meta-data below.
96             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
97        -->
98        <meta-data
99            android:name="flutterEmbedding"
100            android:value="2" />
101
102        <uses-library
102-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
103            android:name="androidx.window.extensions"
103-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
104            android:required="false" />
104-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
105        <uses-library
105-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
106            android:name="androidx.window.sidecar"
106-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
107            android:required="false" />
107-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
108
109        <provider
109-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
110            android:name="androidx.startup.InitializationProvider"
110-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
111            android:authorities="com.example.adhd_task_manager.androidx-startup"
111-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
112            android:exported="false" >
112-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
113            <meta-data
113-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
114                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
114-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
115                android:value="androidx.startup" />
115-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
119        </provider>
120
121        <receiver
121-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
122            android:name="androidx.profileinstaller.ProfileInstallReceiver"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
123            android:directBootAware="false"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
124            android:enabled="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
125            android:exported="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
126            android:permission="android.permission.DUMP" >
126-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
128                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
128-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
131                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
131-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
134                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
134-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
137                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
137-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
138            </intent-filter>
139        </receiver>
140    </application>
141
142</manifest>
