import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';

class HabitsScreen extends StatelessWidget {
  const HabitsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.habitsTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // TODO: عرض تحليلات العادات
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // شريط التقدم الأسبوعي
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تقدم هذا الأسبوع',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    LinearProgressIndicator(
                      value: 0.0, // TODO: حساب التقدم الفعلي
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(AppTheme.successColor),
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      '0% من العادات مكتملة',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // قائمة العادات
            Expanded(
              child: _buildEmptyState(context),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddHabitDialog(context);
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.trending_up,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            AppConstants.noHabitsMessage,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            AppConstants.addFirstHabitMessage,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () {
              _showAddHabitDialog(context);
            },
            icon: const Icon(Icons.add),
            label: const Text('إضافة عادة جديدة'),
          ),
        ],
      ),
    );
  }

  void _showAddHabitDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إضافة عادة جديدة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: 'اسم العادة',
                  border: OutlineInputBorder(),
                  hintText: 'مثال: شرب 8 أكواب ماء',
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'الهدف اليومي',
                  border: OutlineInputBorder(),
                  hintText: 'مثال: 8 مرات',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'تكرار العادة',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'daily',
                    child: Text('يومياً'),
                  ),
                  DropdownMenuItem(
                    value: 'weekly',
                    child: Text('أسبوعياً'),
                  ),
                  DropdownMenuItem(
                    value: 'custom',
                    child: Text('مخصص'),
                  ),
                ],
                onChanged: (value) {
                  // TODO: حفظ تكرار العادة
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                // TODO: حفظ العادة
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إضافة العادة بنجاح'),
                  ),
                );
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }
}
