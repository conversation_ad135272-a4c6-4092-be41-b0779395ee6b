import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.dashboardTitle),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترحيب
            Text(
              'مرحباً بك في إنجاز',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // بطاقات الإحصائيات
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: AppConstants.defaultPadding,
                mainAxisSpacing: AppConstants.defaultPadding,
                children: [
                  _buildStatCard(
                    context,
                    'المهام المكتملة',
                    '0',
                    Icons.check_circle,
                    AppTheme.successColor,
                  ),
                  _buildStatCard(
                    context,
                    'المهام المعلقة',
                    '0',
                    Icons.pending,
                    AppTheme.warningColor,
                  ),
                  _buildStatCard(
                    context,
                    'جلسات التركيز',
                    '0',
                    Icons.timer,
                    AppTheme.primaryColor,
                  ),
                  _buildStatCard(
                    context,
                    'العادات المتتبعة',
                    '0',
                    Icons.trending_up,
                    AppTheme.secondaryColor,
                  ),
                ],
              ),
            ),
            
            // قسم المهام السريعة
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'المهام العاجلة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  children: [
                    Icon(
                      Icons.task_alt,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      'لا توجد مهام عاجلة',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
