import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/providers/task_providers.dart';
import '../../core/providers/usage_tracking_providers.dart';
import '../usage_tracking/usage_reports_screen.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(taskStatisticsProvider);
    final urgentTasksAsync = ref.watch(urgentTasksProvider);

    return Scaffold(
      appBar: AppBar(title: const Text(AppConstants.dashboardTitle)),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترحيب
            Text(
              'مرحباً بك في إنجاز',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // بطاقات الإحصائيات
            Expanded(
              child: statisticsAsync.when(
                data:
                    (statistics) => GridView.count(
                      crossAxisCount: 2,
                      crossAxisSpacing: AppConstants.defaultPadding,
                      mainAxisSpacing: AppConstants.defaultPadding,
                      children: [
                        _buildStatCard(
                          context,
                          'المهام المكتملة',
                          '${statistics.completedTasks}',
                          Icons.check_circle,
                          AppTheme.successColor,
                        ),
                        _buildStatCard(
                          context,
                          'المهام المعلقة',
                          '${statistics.pendingTasks}',
                          Icons.pending,
                          AppTheme.warningColor,
                        ),
                        _buildStatCard(
                          context,
                          'جلسات التركيز',
                          '0',
                          Icons.timer,
                          AppTheme.primaryColor,
                        ),
                        _buildStatCard(
                          context,
                          'العادات المتتبعة',
                          '0',
                          Icons.trending_up,
                          AppTheme.secondaryColor,
                        ),
                      ],
                    ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error:
                    (error, stack) =>
                        Center(child: Text('خطأ في تحميل الإحصائيات: $error')),
              ),
            ),

            // قسم المهام العاجلة
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'المهام العاجلة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            urgentTasksAsync.when(
              data: (urgentTasks) {
                if (urgentTasks.isEmpty) {
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(
                        AppConstants.defaultPadding,
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.task_alt,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          Text(
                            'لا توجد مهام عاجلة',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${urgentTasks.length} مهام عاجلة',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: AppTheme.urgentColor),
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        ...urgentTasks
                            .take(3)
                            .map(
                              (task) => Padding(
                                padding: const EdgeInsets.only(bottom: 4),
                                child: Row(
                                  children: [
                                    Icon(
                                      task.isCompleted
                                          ? Icons.check_circle
                                          : Icons.circle_outlined,
                                      size: 16,
                                      color:
                                          task.isCompleted
                                              ? AppTheme.successColor
                                              : AppTheme.urgentColor,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        task.title,
                                        style: TextStyle(
                                          decoration:
                                              task.isCompleted
                                                  ? TextDecoration.lineThrough
                                                  : null,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        if (urgentTasks.length > 3)
                          Text(
                            'و ${urgentTasks.length - 3} مهام أخرى...',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                      ],
                    ),
                  ),
                );
              },
              loading:
                  () => const Card(
                    child: Padding(
                      padding: EdgeInsets.all(AppConstants.defaultPadding),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                  ),
              error:
                  (error, stack) => Card(
                    child: Padding(
                      padding: const EdgeInsets.all(
                        AppConstants.defaultPadding,
                      ),
                      child: Text('خطأ في تحميل المهام العاجلة: $error'),
                    ),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
