import 'package:flutter/material.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/models/usage_statistics.dart';

/// بطاقة نظرة عامة على الاستخدام
class UsageOverviewCard extends StatelessWidget {
  final UsageStatistics statistics;

  const UsageOverviewCard({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان مع نقاط الجودة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'نظرة عامة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _QualityScoreBadge(score: statistics.usageQualityScore),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // الإحصائيات الرئيسية
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    title: 'إجمالي الوقت',
                    value: statistics.formattedTotalTime,
                    icon: Icons.access_time,
                    color: _getTimeColor(statistics.totalHours),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _StatCard(
                    title: 'مرات الفتح',
                    value: '${statistics.totalUnlocks}',
                    icon: Icons.phone_android,
                    color: _getUnlocksColor(statistics.totalUnlocks),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // إحصائيات الإنتاجية والتشتت
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    title: 'وقت منتج',
                    value: '${statistics.productiveHours.toStringAsFixed(1)}س',
                    subtitle: '${statistics.productivePercentage.toStringAsFixed(0)}%',
                    icon: Icons.work,
                    color: AppTheme.successColor,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _StatCard(
                    title: 'وقت مشتت',
                    value: '${statistics.distractingHours.toStringAsFixed(1)}س',
                    subtitle: '${statistics.distractingPercentage.toStringAsFixed(0)}%',
                    icon: Icons.smartphone,
                    color: AppTheme.warningColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // وقت التركيز ومستوى الاستخدام
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    title: 'وقت التركيز',
                    value: '${statistics.focusHours.toStringAsFixed(1)}س',
                    subtitle: '${statistics.focusPercentage.toStringAsFixed(0)}%',
                    icon: Icons.center_focus_strong,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _UsageLevelCard(
                    level: statistics.usageLevel,
                    userType: statistics.userType,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // شريط التقدم للأوقات المختلفة
            _UsageBreakdownBar(statistics: statistics),
          ],
        ),
      ),
    );
  }

  /// تحديد لون الوقت الإجمالي
  Color _getTimeColor(double hours) {
    if (hours < 2) return AppTheme.successColor;
    if (hours < 4) return AppTheme.warningColor;
    if (hours < 6) return Colors.orange;
    return AppTheme.errorColor;
  }

  /// تحديد لون مرات الفتح
  Color _getUnlocksColor(int unlocks) {
    if (unlocks < 50) return AppTheme.successColor;
    if (unlocks < 100) return AppTheme.warningColor;
    if (unlocks < 150) return Colors.orange;
    return AppTheme.errorColor;
  }
}

/// بطاقة إحصائية فردية
class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (subtitle != null) ...[
            Text(
              subtitle!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
              ),
            ),
          ],
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// شارة نقاط الجودة
class _QualityScoreBadge extends StatelessWidget {
  final int score;

  const _QualityScoreBadge({required this.score});

  @override
  Widget build(BuildContext context) {
    final color = _getScoreColor(score);
    final label = _getScoreLabel(score);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.smallPadding,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '$score% $label',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(int score) {
    if (score >= 80) return AppTheme.successColor;
    if (score >= 60) return AppTheme.warningColor;
    if (score >= 40) return Colors.orange;
    return AppTheme.errorColor;
  }

  String _getScoreLabel(int score) {
    if (score >= 80) return 'ممتاز';
    if (score >= 60) return 'جيد';
    if (score >= 40) return 'متوسط';
    return 'ضعيف';
  }
}

/// بطاقة مستوى الاستخدام
class _UsageLevelCard extends StatelessWidget {
  final UsageLevel level;
  final UserType userType;

  const _UsageLevelCard({
    required this.level,
    required this.userType,
  });

  @override
  Widget build(BuildContext context) {
    final levelColor = _getLevelColor(level);
    final typeColor = _getTypeColor(userType);

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: levelColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: levelColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(_getLevelIcon(level), color: levelColor, size: 24),
          const SizedBox(height: 4),
          Text(
            level.displayName,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: levelColor,
            ),
          ),
          Text(
            userType.displayName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: typeColor,
            ),
          ),
          Text(
            'مستوى الاستخدام',
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getLevelColor(UsageLevel level) {
    switch (level) {
      case UsageLevel.low:
        return AppTheme.successColor;
      case UsageLevel.moderate:
        return AppTheme.warningColor;
      case UsageLevel.high:
        return Colors.orange;
      case UsageLevel.excessive:
        return AppTheme.errorColor;
    }
  }

  Color _getTypeColor(UserType type) {
    switch (type) {
      case UserType.productive:
        return AppTheme.successColor;
      case UserType.focused:
        return AppTheme.primaryColor;
      case UserType.balanced:
        return AppTheme.warningColor;
      case UserType.distracted:
        return AppTheme.errorColor;
    }
  }

  IconData _getLevelIcon(UsageLevel level) {
    switch (level) {
      case UsageLevel.low:
        return Icons.sentiment_very_satisfied;
      case UsageLevel.moderate:
        return Icons.sentiment_satisfied;
      case UsageLevel.high:
        return Icons.sentiment_neutral;
      case UsageLevel.excessive:
        return Icons.sentiment_very_dissatisfied;
    }
  }
}

/// شريط تفصيل الاستخدام
class _UsageBreakdownBar extends StatelessWidget {
  final UsageStatistics statistics;

  const _UsageBreakdownBar({required this.statistics});

  @override
  Widget build(BuildContext context) {
    final total = statistics.totalScreenTime;
    if (total == 0) {
      return const SizedBox.shrink();
    }

    final productiveRatio = statistics.productiveTime / total;
    final distractingRatio = statistics.distractingTime / total;
    final focusRatio = statistics.focusTime / total;
    final otherRatio = 1 - (productiveRatio + distractingRatio + focusRatio);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفصيل الاستخدام',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.grey[300],
          ),
          child: Row(
            children: [
              if (productiveRatio > 0)
                Expanded(
                  flex: (productiveRatio * 100).round(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppTheme.successColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        bottomLeft: Radius.circular(4),
                      ),
                    ),
                  ),
                ),
              if (focusRatio > 0)
                Expanded(
                  flex: (focusRatio * 100).round(),
                  child: Container(
                    color: AppTheme.primaryColor,
                  ),
                ),
              if (distractingRatio > 0)
                Expanded(
                  flex: (distractingRatio * 100).round(),
                  child: Container(
                    color: AppTheme.warningColor,
                  ),
                ),
              if (otherRatio > 0)
                Expanded(
                  flex: (otherRatio * 100).round(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(4),
                        bottomRight: Radius.circular(4),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _LegendItem(
              color: AppTheme.successColor,
              label: 'منتج',
              percentage: statistics.productivePercentage,
            ),
            _LegendItem(
              color: AppTheme.primaryColor,
              label: 'تركيز',
              percentage: statistics.focusPercentage,
            ),
            _LegendItem(
              color: AppTheme.warningColor,
              label: 'مشتت',
              percentage: statistics.distractingPercentage,
            ),
            _LegendItem(
              color: Colors.grey[400]!,
              label: 'أخرى',
              percentage: 100 - statistics.productivePercentage - 
                          statistics.focusPercentage - 
                          statistics.distractingPercentage,
            ),
          ],
        ),
      ],
    );
  }
}

/// عنصر وسيلة الإيضاح
class _LegendItem extends StatelessWidget {
  final Color color;
  final String label;
  final double percentage;

  const _LegendItem({
    required this.color,
    required this.label,
    required this.percentage,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label ${percentage.toStringAsFixed(0)}%',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
