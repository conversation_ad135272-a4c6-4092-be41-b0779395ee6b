import 'package:flutter/material.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/models/usage_statistics.dart';

/// رسم بياني لاستخدام الهاتف
class UsageChart extends StatelessWidget {
  final UsageStatistics statistics;

  const UsageChart({super.key, required this.statistics});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع الاستخدام',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // الرسم البياني الدائري
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  // الرسم البياني
                  Expanded(flex: 2, child: _<PERSON><PERSON><PERSON>(statistics: statistics)),

                  const SizedBox(width: AppConstants.defaultPadding),

                  // وسيلة الإيضاح
                  Expanded(
                    flex: 1,
                    child: _ChartLegend(statistics: statistics),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // الرسم البياني الشريطي للتطبيقات الأكثر استخداماً
            _TopAppsBarChart(statistics: statistics),
          ],
        ),
      ),
    );
  }
}

/// الرسم البياني الدائري
class _PieChart extends StatelessWidget {
  final UsageStatistics statistics;

  const _PieChart({required this.statistics});

  @override
  Widget build(BuildContext context) {
    if (statistics.totalScreenTime == 0) {
      return _EmptyChart();
    }

    return CustomPaint(
      size: const Size(160, 160),
      painter: _PieChartPainter(statistics: statistics),
    );
  }
}

/// رسام الرسم البياني الدائري
class _PieChartPainter extends CustomPainter {
  final UsageStatistics statistics;

  _PieChartPainter({required this.statistics});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;

    final total = statistics.totalScreenTime.toDouble();
    if (total == 0) return;

    double startAngle = -90 * (3.14159 / 180); // البدء من الأعلى

    // رسم قطاع الوقت المنتج
    if (statistics.productiveTime > 0) {
      final sweepAngle = (statistics.productiveTime / total) * 2 * 3.14159;
      final paint =
          Paint()
            ..color = AppTheme.successColor
            ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // رسم قطاع وقت التركيز
    if (statistics.focusTime > 0) {
      final sweepAngle = (statistics.focusTime / total) * 2 * 3.14159;
      final paint =
          Paint()
            ..color = AppTheme.primaryColor
            ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // رسم قطاع الوقت المشتت
    if (statistics.distractingTime > 0) {
      final sweepAngle = (statistics.distractingTime / total) * 2 * 3.14159;
      final paint =
          Paint()
            ..color = AppTheme.warningColor
            ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // رسم الوقت المتبقي
    final remainingTime =
        total -
        statistics.productiveTime -
        statistics.focusTime -
        statistics.distractingTime;
    if (remainingTime > 0) {
      final sweepAngle = (remainingTime / total) * 2 * 3.14159;
      final paint =
          Paint()
            ..color = Colors.grey[400]!
            ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );
    }

    // رسم دائرة داخلية لإنشاء تأثير الدونات
    final innerPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.6, innerPaint);

    // رسم النص في المركز
    final textPainter = TextPainter(
      text: TextSpan(
        text: statistics.formattedTotalTime,
        style: TextStyle(
          color: Colors.black,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// وسيلة إيضاح الرسم البياني
class _ChartLegend extends StatelessWidget {
  final UsageStatistics statistics;

  const _ChartLegend({required this.statistics});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (statistics.productiveTime > 0)
          _LegendItem(
            color: AppTheme.successColor,
            label: 'منتج',
            value: '${statistics.productiveHours.toStringAsFixed(1)}س',
            percentage: statistics.productivePercentage,
          ),

        if (statistics.focusTime > 0)
          _LegendItem(
            color: AppTheme.primaryColor,
            label: 'تركيز',
            value: '${statistics.focusHours.toStringAsFixed(1)}س',
            percentage: statistics.focusPercentage,
          ),

        if (statistics.distractingTime > 0)
          _LegendItem(
            color: AppTheme.warningColor,
            label: 'مشتت',
            value: '${statistics.distractingHours.toStringAsFixed(1)}س',
            percentage: statistics.distractingPercentage,
          ),

        _LegendItem(
          color: Colors.grey[400]!,
          label: 'أخرى',
          value:
              '${(statistics.totalHours - statistics.productiveHours - statistics.focusHours - statistics.distractingHours).toStringAsFixed(1)}س',
          percentage:
              100 -
              statistics.productivePercentage -
              statistics.focusPercentage -
              statistics.distractingPercentage,
        ),
      ],
    );
  }
}

/// عنصر وسيلة الإيضاح
class _LegendItem extends StatelessWidget {
  final Color color;
  final String label;
  final String value;
  final double percentage;

  const _LegendItem({
    required this.color,
    required this.label,
    required this.value,
    required this.percentage,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
                Text(
                  '$value (${percentage.toStringAsFixed(0)}%)',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// الرسم البياني الشريطي للتطبيقات الأكثر استخداماً
class _TopAppsBarChart extends StatelessWidget {
  final UsageStatistics statistics;

  const _TopAppsBarChart({required this.statistics});

  @override
  Widget build(BuildContext context) {
    final topApps = statistics.topApps.take(5).toList();

    if (topApps.isEmpty) {
      return const SizedBox.shrink();
    }

    final maxTime = topApps.first.totalTimeInForeground.toDouble();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أهم 5 تطبيقات',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.smallPadding),

        ...topApps.map((app) {
          final percentage =
              maxTime > 0 ? app.totalTimeInForeground / maxTime : 0;

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                SizedBox(
                  width: 80,
                  child: Text(
                    app.appName.length > 10
                        ? '${app.appName.substring(0, 10)}...'
                        : app.appName,
                    style: Theme.of(context).textTheme.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: percentage.toDouble(),
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              app.isDistractingApp
                                  ? AppTheme.warningColor
                                  : app.isProductiveApp
                                  ? AppTheme.successColor
                                  : AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 40,
                  child: Text(
                    app.formattedTime,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// رسم بياني فارغ
class _EmptyChart extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 160,
      height: 160,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[200],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.pie_chart_outline, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 8),
            Text(
              'لا توجد بيانات',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
