import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/providers/usage_tracking_providers.dart';
import '../../../core/models/app_usage_data.dart';

/// قائمة أهم التطبيقات المستخدمة (البيانات الحقيقية)
class RealTopAppsList extends ConsumerWidget {
  const RealTopAppsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final topAppsAsync = ref.watch(topAppsProvider);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'أهم التطبيقات المستخدمة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            topAppsAsync.when(
              data: (apps) {
                if (apps.isEmpty) {
                  return _buildEmptyState(context);
                }
                return _buildAppsList(context, apps);
              },
              loading: () => _buildLoadingState(),
              error: (error, stack) => _buildErrorState(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppsList(BuildContext context, List<AppUsageData> apps) {
    return Column(
      children: [
        // إحصائيات سريعة
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                context,
                'إجمالي الوقت',
                _formatTotalTime(apps),
                Icons.access_time,
              ),
              _buildStatItem(
                context,
                'متوسط الاستخدام',
                _formatAverageTime(apps),
                Icons.timeline,
              ),
              _buildStatItem(
                context,
                'أكثر استخداماً',
                apps.isNotEmpty ? apps.first.appName : '-',
                Icons.star,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // قائمة التطبيقات
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: apps.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final app = apps[index];
            return _buildAppTile(context, app, index + 1);
          },
        ),
      ],
    );
  }

  Widget _buildAppTile(BuildContext context, AppUsageData app, int rank) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      leading: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // ترقيم مع تمييز المراكز الأولى
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: _getRankColor(rank),
              shape: BoxShape.circle,
              boxShadow:
                  rank <= 3
                      ? [
                        BoxShadow(
                          color: _getRankColor(rank).withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                      : null,
            ),
            child: Center(
              child:
                  rank <= 3
                      ? Icon(
                        rank == 1
                            ? Icons.looks_one
                            : rank == 2
                            ? Icons.looks_two
                            : Icons.looks_3,
                        color: Colors.white,
                        size: 16,
                      )
                      : Text(
                        rank.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
            ),
          ),
          const SizedBox(width: 12),
          // أيقونة التطبيق
          Container(
            width: 45,
            height: 45,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child:
                app.icon != null
                    ? ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Image.memory(
                        app.icon!,
                        width: 45,
                        height: 45,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildDefaultIcon();
                        },
                      ),
                    )
                    : _buildDefaultIcon(),
          ),
        ],
      ),
      title: Text(
        app.appName,
        style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getCategoryColor(app.category).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              app.category,
              style: TextStyle(
                color: _getCategoryColor(app.category),
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (app.launchCount > 0)
            Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                '${app.launchCount} مرة',
                style: TextStyle(color: Colors.grey[600], fontSize: 11),
              ),
            ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            app.formattedTime,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: _getRankColor(rank),
            ),
          ),
          if (app.usageTime.inMinutes > 0)
            Text(
              _getUsageLevel(app.usageTime.inMinutes),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[500],
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDefaultIcon() {
    return Icon(Icons.android, color: Colors.grey[400], size: 28);
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            label,
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.apps_outlined, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 12),
            Text(
              'لا توجد تطبيقات مستخدمة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[400]),
            const SizedBox(height: 12),
            Text(
              'خطأ في تحميل البيانات',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.red[600]),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return const Color(0xFFFFD700); // ذهبي
      case 2:
        return const Color(0xFFC0C0C0); // فضي
      case 3:
        return const Color(0xFFCD7F32); // برونزي
      default:
        return AppTheme.primaryColor;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'التواصل الاجتماعي':
        return Colors.blue;
      case 'الترفيه':
        return Colors.purple;
      case 'الألعاب':
        return Colors.green;
      case 'الإنتاجية':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _formatTotalTime(List<AppUsageData> apps) {
    final totalMinutes = apps.fold<int>(
      0,
      (sum, app) => sum + app.usageTime.inMinutes,
    );

    if (totalMinutes < 60) {
      return '${totalMinutes}د';
    } else {
      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;
      return '${hours}س ${minutes}د';
    }
  }

  String _formatAverageTime(List<AppUsageData> apps) {
    if (apps.isEmpty) return '0د';

    final totalMinutes = apps.fold<int>(
      0,
      (sum, app) => sum + app.usageTime.inMinutes,
    );

    final averageMinutes = totalMinutes ~/ apps.length;

    if (averageMinutes < 60) {
      return '${averageMinutes}د';
    } else {
      final hours = averageMinutes ~/ 60;
      final minutes = averageMinutes % 60;
      return '${hours}س ${minutes}د';
    }
  }

  String _getUsageLevel(int minutes) {
    if (minutes < 30) return 'قليل';
    if (minutes < 120) return 'متوسط';
    return 'كثير';
  }
}
