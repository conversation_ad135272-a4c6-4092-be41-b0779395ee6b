import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/providers/usage_tracking_providers.dart';
import '../../../core/models/app_usage_data.dart';

/// قائمة التطبيقات الحقيقية المثبتة على الهاتف
class RealAppUsageList extends ConsumerWidget {
  const RealAppUsageList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final realAppsAsync = ref.watch(realAppsProvider);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.apps,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'التطبيقات المثبتة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    ref.invalidate(realAppsProvider);
                  },
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            realAppsAsync.when(
              data: (apps) {
                if (apps.isEmpty) {
                  return _buildEmptyState(context);
                }
                return _buildAppsList(context, apps);
              },
              loading: () => _buildLoadingState(),
              error: (error, stack) => _buildErrorState(context, error),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppsList(BuildContext context, List<AppUsageData> apps) {
    return Column(
      children: [
        // إحصائيات سريعة
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                context,
                'إجمالي التطبيقات',
                apps.length.toString(),
                Icons.apps,
              ),
              _buildStatItem(
                context,
                'التطبيقات المستخدمة',
                apps.where((app) => app.usageTime.inMinutes > 0).length.toString(),
                Icons.access_time,
              ),
              _buildStatItem(
                context,
                'الوقت الإجمالي',
                _formatTotalTime(apps),
                Icons.timer,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // قائمة التطبيقات
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: apps.take(20).length, // عرض أول 20 تطبيق
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final app = apps[index];
            return _buildAppTile(context, app, index + 1);
          },
        ),
        
        if (apps.length > 20)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: Text(
              'وأكثر من ${apps.length - 20} تطبيق آخر...',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }

  Widget _buildAppTile(BuildContext context, AppUsageData app, int rank) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      leading: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // ترقيم
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _getRankColor(rank),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                rank.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // أيقونة التطبيق
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: app.icon != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.memory(
                      app.icon!,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultIcon();
                      },
                    ),
                  )
                : _buildDefaultIcon(),
          ),
        ],
      ),
      title: Text(
        app.appName,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            app.category,
            style: TextStyle(
              color: _getCategoryColor(app.category),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (app.usageTime.inMinutes > 0)
            Text(
              'آخر استخدام: ${_formatLastUsed(app.lastTimeUsed)}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 11,
              ),
            ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            app.formattedTime,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: app.usageTime.inMinutes > 0 
                  ? AppTheme.primaryColor 
                  : Colors.grey[500],
            ),
          ),
          if (app.launchCount > 0)
            Text(
              '${app.launchCount} مرة',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDefaultIcon() {
    return Icon(
      Icons.android,
      color: Colors.grey[400],
      size: 24,
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.primaryColor, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.apps_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد تطبيقات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تأكد من منح الأذونات المطلوبة',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, Object error) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل التطبيقات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على تحديث للمحاولة مرة أخرى',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Color _getRankColor(int rank) {
    if (rank <= 3) return Colors.amber;
    if (rank <= 10) return AppTheme.primaryColor;
    return Colors.grey;
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'التواصل الاجتماعي':
        return Colors.blue;
      case 'الترفيه':
        return Colors.purple;
      case 'الألعاب':
        return Colors.green;
      case 'الإنتاجية':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _formatTotalTime(List<AppUsageData> apps) {
    final totalMinutes = apps.fold<int>(
      0,
      (sum, app) => sum + app.usageTime.inMinutes,
    );
    
    if (totalMinutes < 60) {
      return '${totalMinutes}د';
    } else {
      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;
      return '${hours}س ${minutes}د';
    }
  }

  String _formatLastUsed(DateTime? lastUsed) {
    if (lastUsed == null) return 'غير محدد';
    
    final now = DateTime.now();
    final difference = now.difference(lastUsed);
    
    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
