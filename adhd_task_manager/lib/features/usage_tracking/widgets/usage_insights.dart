import 'package:flutter/material.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/models/usage_statistics.dart';

/// ويدجت الرؤى والتوصيات
class UsageInsights extends StatelessWidget {
  final UsageStatistics statistics;

  const UsageInsights({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    final insights = _generateInsights();
    final recommendations = _generateRecommendations();

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الرؤى والتوصيات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // الرؤى
            if (insights.isNotEmpty) ...[
              Text(
                'الرؤى',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              ...insights.map((insight) => _InsightCard(insight: insight)),
              const SizedBox(height: AppConstants.defaultPadding),
            ],
            
            // التوصيات
            if (recommendations.isNotEmpty) ...[
              Text(
                'التوصيات',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.successColor,
                ),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              ...recommendations.map((recommendation) => 
                _RecommendationCard(recommendation: recommendation)),
            ],
          ],
        ),
      ),
    );
  }

  /// توليد الرؤى بناءً على الإحصائيات
  List<UsageInsight> _generateInsights() {
    final insights = <UsageInsight>[];

    // رؤية حول الوقت الإجمالي
    if (statistics.totalHours > 6) {
      insights.add(UsageInsight(
        type: InsightType.warning,
        title: 'استخدام مفرط للهاتف',
        description: 'قضيت ${statistics.formattedTotalTime} اليوم، وهو أكثر من المعدل الصحي المُوصى به (4 ساعات).',
        icon: Icons.warning,
      ));
    } else if (statistics.totalHours < 2) {
      insights.add(UsageInsight(
        type: InsightType.positive,
        title: 'استخدام صحي للهاتف',
        description: 'ممتاز! قضيت ${statistics.formattedTotalTime} فقط اليوم، وهو ضمن المعدل الصحي.',
        icon: Icons.check_circle,
      ));
    }

    // رؤية حول الوقت المنتج
    if (statistics.productivePercentage > 60) {
      insights.add(UsageInsight(
        type: InsightType.positive,
        title: 'تركيز عالي على التطبيقات المنتجة',
        description: '${statistics.productivePercentage.toStringAsFixed(0)}% من وقتك كان في تطبيقات منتجة.',
        icon: Icons.trending_up,
      ));
    } else if (statistics.productivePercentage < 20) {
      insights.add(UsageInsight(
        type: InsightType.warning,
        title: 'وقت منتج قليل',
        description: 'فقط ${statistics.productivePercentage.toStringAsFixed(0)}% من وقتك كان في تطبيقات منتجة.',
        icon: Icons.trending_down,
      ));
    }

    // رؤية حول التطبيقات المشتتة
    if (statistics.distractingPercentage > 50) {
      insights.add(UsageInsight(
        type: InsightType.warning,
        title: 'وقت كثير في التطبيقات المشتتة',
        description: '${statistics.distractingPercentage.toStringAsFixed(0)}% من وقتك كان في تطبيقات مشتتة.',
        icon: Icons.smartphone,
      ));
    }

    // رؤية حول وقت التركيز
    if (statistics.focusPercentage > 30) {
      insights.add(UsageInsight(
        type: InsightType.positive,
        title: 'استخدام ممتاز لأدوات التركيز',
        description: '${statistics.focusPercentage.toStringAsFixed(0)}% من وقتك كان في جلسات تركيز.',
        icon: Icons.center_focus_strong,
      ));
    } else if (statistics.focusPercentage < 10) {
      insights.add(UsageInsight(
        type: InsightType.info,
        title: 'يمكن تحسين استخدام أدوات التركيز',
        description: 'فقط ${statistics.focusPercentage.toStringAsFixed(0)}% من وقتك كان في جلسات تركيز.',
        icon: Icons.info,
      ));
    }

    // رؤية حول مرات فتح الهاتف
    if (statistics.totalUnlocks > 150) {
      insights.add(UsageInsight(
        type: InsightType.warning,
        title: 'فتح مفرط للهاتف',
        description: 'فتحت الهاتف ${statistics.totalUnlocks} مرة اليوم، وهو أكثر من المعدل الطبيعي.',
        icon: Icons.phone_android,
      ));
    }

    // رؤية حول أهم التطبيقات
    if (statistics.topApps.isNotEmpty) {
      final topApp = statistics.topApps.first;
      if (topApp.totalTimeInHours > 2) {
        insights.add(UsageInsight(
          type: InsightType.info,
          title: 'التطبيق الأكثر استخداماً',
          description: 'قضيت ${topApp.formattedTime} في ${topApp.appName} اليوم.',
          icon: Icons.apps,
        ));
      }
    }

    return insights;
  }

  /// توليد التوصيات بناءً على الإحصائيات
  List<UsageRecommendation> _generateRecommendations() {
    final recommendations = <UsageRecommendation>[];

    // توصيات للاستخدام المفرط
    if (statistics.totalHours > 6) {
      recommendations.add(UsageRecommendation(
        title: 'قلل من وقت الشاشة',
        description: 'حاول تقليل استخدام الهاتف إلى 4 ساعات يومياً أو أقل.',
        actionText: 'ضع حدود زمنية',
        priority: RecommendationPriority.high,
        icon: Icons.timer,
      ));
    }

    // توصيات للتطبيقات المشتتة
    if (statistics.distractingPercentage > 40) {
      recommendations.add(UsageRecommendation(
        title: 'قلل من التطبيقات المشتتة',
        description: 'استخدم وضع التركيز لتقليل الوقت في التطبيقات المشتتة.',
        actionText: 'فعل وضع التركيز',
        priority: RecommendationPriority.high,
        icon: Icons.block,
      ));
    }

    // توصيات لتحسين الإنتاجية
    if (statistics.productivePercentage < 30) {
      recommendations.add(UsageRecommendation(
        title: 'زد من استخدام التطبيقات المنتجة',
        description: 'حاول قضاء المزيد من الوقت في تطبيقات العمل والتعلم.',
        actionText: 'اكتشف تطبيقات منتجة',
        priority: RecommendationPriority.medium,
        icon: Icons.work,
      ));
    }

    // توصيات لاستخدام أدوات التركيز
    if (statistics.focusPercentage < 15) {
      recommendations.add(UsageRecommendation(
        title: 'استخدم مؤقت Pomodoro أكثر',
        description: 'جلسات التركيز تساعد على تحسين الإنتاجية وتقليل التشتت.',
        actionText: 'ابدأ جلسة تركيز',
        priority: RecommendationPriority.medium,
        icon: Icons.center_focus_strong,
      ));
    }

    // توصيات لتقليل مرات فتح الهاتف
    if (statistics.totalUnlocks > 100) {
      recommendations.add(UsageRecommendation(
        title: 'قلل من مرات فتح الهاتف',
        description: 'حاول تجميع المهام وتقليل عدد مرات فتح الهاتف.',
        actionText: 'ضع تذكيرات',
        priority: RecommendationPriority.medium,
        icon: Icons.notifications_off,
      ));
    }

    // توصيات إيجابية للتشجيع
    if (statistics.usageQualityScore > 80) {
      recommendations.add(UsageRecommendation(
        title: 'أحسنت! استمر على هذا المنوال',
        description: 'استخدامك للهاتف صحي ومتوازن. حافظ على هذا النمط.',
        actionText: 'شارك إنجازك',
        priority: RecommendationPriority.low,
        icon: Icons.celebration,
      ));
    }

    return recommendations;
  }
}

/// بطاقة الرؤية
class _InsightCard extends StatelessWidget {
  final UsageInsight insight;

  const _InsightCard({required this.insight});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: insight.type.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: insight.type.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            insight.icon,
            color: insight.type.color,
            size: 20,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  insight.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: insight.type.color,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  insight.description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// بطاقة التوصية
class _RecommendationCard extends StatelessWidget {
  final UsageRecommendation recommendation;

  const _RecommendationCard({required this.recommendation});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: recommendation.priority.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: recommendation.priority.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            recommendation.icon,
            color: recommendation.priority.color,
            size: 20,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  recommendation.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: recommendation.priority.color,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  recommendation.description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                TextButton(
                  onPressed: () => _handleRecommendationAction(context),
                  style: TextButton.styleFrom(
                    foregroundColor: recommendation.priority.color,
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Text(
                    recommendation.actionText,
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleRecommendationAction(BuildContext context) {
    // هنا يمكن إضافة منطق التعامل مع إجراءات التوصيات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم تنفيذ: ${recommendation.actionText}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// نموذج الرؤية
class UsageInsight {
  final InsightType type;
  final String title;
  final String description;
  final IconData icon;

  const UsageInsight({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
  });
}

/// نموذج التوصية
class UsageRecommendation {
  final String title;
  final String description;
  final String actionText;
  final RecommendationPriority priority;
  final IconData icon;

  const UsageRecommendation({
    required this.title,
    required this.description,
    required this.actionText,
    required this.priority,
    required this.icon,
  });
}

/// أنواع الرؤى
enum InsightType {
  positive(AppTheme.successColor),
  warning(AppTheme.warningColor),
  info(AppTheme.primaryColor);

  const InsightType(this.color);
  final Color color;
}

/// أولويات التوصيات
enum RecommendationPriority {
  high(AppTheme.errorColor),
  medium(AppTheme.warningColor),
  low(AppTheme.successColor);

  const RecommendationPriority(this.color);
  final Color color;
}
