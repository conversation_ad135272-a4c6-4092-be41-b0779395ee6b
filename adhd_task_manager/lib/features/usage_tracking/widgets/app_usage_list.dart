import 'package:flutter/material.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/models/app_usage.dart';

/// قائمة استخدام التطبيقات
class AppUsageList extends StatelessWidget {
  final List<AppUsage> appUsages;
  final String title;
  final int? maxItems;

  const AppUsageList({
    super.key,
    required this.appUsages,
    this.title = 'أهم التطبيقات',
    this.maxItems,
  });

  @override
  Widget build(BuildContext context) {
    if (appUsages.isEmpty) {
      return _EmptyState();
    }

    final displayedApps = maxItems != null 
        ? appUsages.take(maxItems!).toList()
        : appUsages;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (appUsages.length > (maxItems ?? appUsages.length))
                  TextButton(
                    onPressed: () => _showAllApps(context),
                    child: const Text('عرض الكل'),
                  ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // قائمة التطبيقات
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: displayedApps.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final app = displayedApps[index];
                return _AppUsageItem(
                  appUsage: app,
                  rank: index + 1,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// عرض جميع التطبيقات في صفحة منفصلة
  void _showAllApps(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _AllAppsScreen(
          appUsages: appUsages,
          title: title,
        ),
      ),
    );
  }
}

/// عنصر استخدام تطبيق واحد
class _AppUsageItem extends StatelessWidget {
  final AppUsage appUsage;
  final int rank;

  const _AppUsageItem({
    required this.appUsage,
    required this.rank,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // ترتيب التطبيق
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _getRankColor(rank),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$rank',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          // أيقونة التطبيق
          _AppIcon(appUsage: appUsage),
        ],
      ),
      title: Text(
        appUsage.appName,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Row(
        children: [
          _CategoryChip(category: appUsage.category),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            '${appUsage.launchCount} مرة فتح',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            appUsage.formattedTime,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _getTimeColor(appUsage.totalTimeInHours),
            ),
          ),
          _UsageBar(
            percentage: _calculatePercentage(appUsage),
            color: _getTimeColor(appUsage.totalTimeInHours),
          ),
        ],
      ),
      onTap: () => _showAppDetails(context),
    );
  }

  /// تحديد لون الترتيب
  Color _getRankColor(int rank) {
    if (rank <= 3) return AppTheme.primaryColor;
    if (rank <= 5) return AppTheme.warningColor;
    return Colors.grey;
  }

  /// تحديد لون الوقت
  Color _getTimeColor(double hours) {
    if (hours < 0.5) return AppTheme.successColor;
    if (hours < 1) return AppTheme.warningColor;
    if (hours < 2) return Colors.orange;
    return AppTheme.errorColor;
  }

  /// حساب النسبة المئوية للاستخدام
  double _calculatePercentage(AppUsage app) {
    // نفترض أن أقصى وقت هو 4 ساعات للحصول على نسبة مئوية
    const maxHours = 4.0;
    return (app.totalTimeInHours / maxHours * 100).clamp(0, 100);
  }

  /// عرض تفاصيل التطبيق
  void _showAppDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _AppDetailsSheet(appUsage: appUsage),
    );
  }
}

/// أيقونة التطبيق
class _AppIcon extends StatelessWidget {
  final AppUsage appUsage;

  const _AppIcon({required this.appUsage});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getCategoryColor(appUsage.category).withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        _getCategoryIcon(appUsage.category),
        color: _getCategoryColor(appUsage.category),
        size: 24,
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'social':
        return Colors.blue;
      case 'entertainment':
        return Colors.purple;
      case 'productivity':
        return AppTheme.successColor;
      case 'games':
        return Colors.orange;
      case 'education':
        return Colors.indigo;
      case 'health':
        return Colors.green;
      case 'music':
        return Colors.pink;
      case 'news':
        return Colors.red;
      case 'shopping':
        return Colors.amber;
      case 'travel':
        return Colors.teal;
      case 'finance':
        return Colors.green[800]!;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'social':
        return Icons.people;
      case 'entertainment':
        return Icons.movie;
      case 'productivity':
        return Icons.work;
      case 'games':
        return Icons.games;
      case 'education':
        return Icons.school;
      case 'health':
        return Icons.health_and_safety;
      case 'music':
        return Icons.music_note;
      case 'news':
        return Icons.newspaper;
      case 'shopping':
        return Icons.shopping_cart;
      case 'travel':
        return Icons.flight;
      case 'finance':
        return Icons.account_balance;
      default:
        return Icons.apps;
    }
  }
}

/// رقاقة التصنيف
class _CategoryChip extends StatelessWidget {
  final String category;

  const _CategoryChip({required this.category});

  @override
  Widget build(BuildContext context) {
    final categoryEnum = AppCategory.fromValue(category);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getCategoryColor(category).withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        categoryEnum.displayName,
        style: TextStyle(
          fontSize: 10,
          color: _getCategoryColor(category),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'social':
        return Colors.blue;
      case 'entertainment':
        return Colors.purple;
      case 'productivity':
        return AppTheme.successColor;
      case 'games':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

/// شريط الاستخدام
class _UsageBar extends StatelessWidget {
  final double percentage;
  final Color color;

  const _UsageBar({
    required this.percentage,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      height: 4,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: percentage / 100,
        child: Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ),
    );
  }
}

/// حالة فارغة
class _EmptyState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding * 2),
        child: Column(
          children: [
            Icon(
              Icons.apps,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'لا توجد بيانات استخدام',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'ابدأ تتبع الاستخدام لرؤية إحصائيات التطبيقات',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// شاشة جميع التطبيقات
class _AllAppsScreen extends StatelessWidget {
  final List<AppUsage> appUsages;
  final String title;

  const _AllAppsScreen({
    required this.appUsages,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: appUsages.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final app = appUsages[index];
          return _AppUsageItem(
            appUsage: app,
            rank: index + 1,
          );
        },
      ),
    );
  }
}

/// ورقة تفاصيل التطبيق
class _AppDetailsSheet extends StatelessWidget {
  final AppUsage appUsage;

  const _AppDetailsSheet({required this.appUsage});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              _AppIcon(appUsage: appUsage),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appUsage.appName,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Text(
                      appUsage.packageName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // الإحصائيات
          Row(
            children: [
              Expanded(
                child: _DetailCard(
                  title: 'إجمالي الوقت',
                  value: appUsage.formattedTime,
                  icon: Icons.access_time,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _DetailCard(
                  title: 'مرات الفتح',
                  value: '${appUsage.launchCount}',
                  icon: Icons.launch,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          Row(
            children: [
              Expanded(
                child: _DetailCard(
                  title: 'التصنيف',
                  value: AppCategory.fromValue(appUsage.category).displayName,
                  icon: Icons.category,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _DetailCard(
                  title: 'النوع',
                  value: appUsage.isDistractingApp 
                      ? 'مشتت' 
                      : appUsage.isProductiveApp 
                          ? 'منتج' 
                          : 'عادي',
                  icon: appUsage.isDistractingApp 
                      ? Icons.warning 
                      : appUsage.isProductiveApp 
                          ? Icons.check_circle 
                          : Icons.info,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// بطاقة تفصيل
class _DetailCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;

  const _DetailCard({
    required this.title,
    required this.value,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}
