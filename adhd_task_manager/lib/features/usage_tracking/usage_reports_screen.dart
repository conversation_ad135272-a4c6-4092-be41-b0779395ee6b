import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/providers/usage_tracking_providers.dart';
import '../../core/models/usage_statistics.dart';
import 'widgets/usage_overview_card.dart';
import 'widgets/app_usage_list.dart';
import 'widgets/usage_chart.dart';
import 'widgets/usage_insights.dart';

/// شاشة تقارير استخدام الهاتف
class UsageReportsScreen extends ConsumerWidget {
  const UsageReportsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = ref.watch(selectedDateProvider);
    final viewType = ref.watch(usageViewTypeProvider);
    final usageStatsAsync = ref.watch(usageStatisticsProvider);
    final weeklyStatsAsync = ref.watch(weeklyUsageStatisticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الاستخدام'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // زر تحديث البيانات
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(usageTrackingControllerProvider).refreshStatistics();
            },
          ),
          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context, ref),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التحكم في نوع العرض والتاريخ
          _buildControlBar(context, ref),

          // المحتوى الرئيسي
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                await ref
                    .read(usageTrackingControllerProvider)
                    .refreshStatistics();
              },
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // نظرة عامة على الاستخدام
                    if (viewType == UsageViewType.daily)
                      usageStatsAsync.when(
                        data: (stats) => UsageOverviewCard(statistics: stats),
                        loading: () => const _LoadingCard(),
                        error:
                            (error, stack) =>
                                _ErrorCard(error: error.toString()),
                      ),

                    if (viewType == UsageViewType.weekly)
                      weeklyStatsAsync.when(
                        data: (stats) => _WeeklyOverviewCard(statistics: stats),
                        loading: () => const _LoadingCard(),
                        error:
                            (error, stack) =>
                                _ErrorCard(error: error.toString()),
                      ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // الرسم البياني
                    if (viewType == UsageViewType.daily)
                      usageStatsAsync.when(
                        data: (stats) => UsageChart(statistics: stats),
                        loading: () => const _LoadingCard(),
                        error: (error, stack) => const SizedBox.shrink(),
                      ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // قائمة التطبيقات
                    if (viewType == UsageViewType.daily)
                      usageStatsAsync.when(
                        data: (stats) => AppUsageList(appUsages: stats.topApps),
                        loading: () => const _LoadingCard(),
                        error: (error, stack) => const SizedBox.shrink(),
                      ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // الرؤى والتوصيات
                    if (viewType == UsageViewType.daily)
                      usageStatsAsync.when(
                        data: (stats) => UsageInsights(statistics: stats),
                        loading: () => const _LoadingCard(),
                        error: (error, stack) => const SizedBox.shrink(),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),

      // زر عائم لبدء/إيقاف التتبع
      floatingActionButton: _buildTrackingFAB(context, ref),
    );
  }

  /// شريط التحكم في نوع العرض والتاريخ
  Widget _buildControlBar(BuildContext context, WidgetRef ref) {
    final selectedDate = ref.watch(selectedDateProvider);
    final viewType = ref.watch(usageViewTypeProvider);

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // أزرار نوع العرض
          Row(
            children:
                UsageViewType.values.map((type) {
                  final isSelected = viewType == type;
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: ElevatedButton(
                        onPressed: () {
                          ref.read(usageViewTypeProvider.notifier).state = type;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              isSelected ? AppTheme.primaryColor : Colors.white,
                          foregroundColor:
                              isSelected ? Colors.white : AppTheme.primaryColor,
                          elevation: isSelected ? 2 : 0,
                          side: BorderSide(
                            color: AppTheme.primaryColor,
                            width: 1,
                          ),
                        ),
                        child: Text(type.displayName),
                      ),
                    ),
                  );
                }).toList(),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          // تحديد التاريخ
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () => _navigateDate(ref, -1),
              ),
              Expanded(
                child: Center(
                  child: TextButton(
                    onPressed: () => _selectDate(context, ref),
                    child: Text(
                      _formatDateRange(selectedDate, viewType),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: () => _navigateDate(ref, 1),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// زر عائم لبدء/إيقاف التتبع
  Widget _buildTrackingFAB(BuildContext context, WidgetRef ref) {
    final isTracking = ref.watch(usageTrackingStateProvider);

    return FloatingActionButton.extended(
      onPressed: () async {
        final controller = ref.read(usageTrackingControllerProvider);

        if (isTracking) {
          await controller.stopTracking();
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم إيقاف تتبع الاستخدام')),
            );
          }
        } else {
          final success = await controller.startTracking(context: context);
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  success
                      ? 'تم بدء تتبع الاستخدام'
                      : 'فشل في بدء التتبع - تحقق من الأذونات',
                ),
              ),
            );
          }
        }
      },
      icon: Icon(isTracking ? Icons.stop : Icons.play_arrow),
      label: Text(isTracking ? 'إيقاف التتبع' : 'بدء التتبع'),
      backgroundColor: isTracking ? AppTheme.errorColor : AppTheme.primaryColor,
    );
  }

  /// التنقل بين التواريخ
  void _navigateDate(WidgetRef ref, int direction) {
    final currentDate = ref.read(selectedDateProvider);
    final viewType = ref.read(usageViewTypeProvider);

    DateTime newDate;
    switch (viewType) {
      case UsageViewType.daily:
        newDate = currentDate.add(Duration(days: direction));
        ref.read(selectedDateProvider.notifier).state = newDate;
        ref.read(usageStatisticsProvider.notifier).updateDate(newDate);
        break;
      case UsageViewType.weekly:
        if (direction > 0) {
          ref.read(weeklyUsageStatisticsProvider.notifier).nextWeek();
        } else {
          ref.read(weeklyUsageStatisticsProvider.notifier).previousWeek();
        }
        break;
      case UsageViewType.monthly:
        newDate = DateTime(currentDate.year, currentDate.month + direction, 1);
        ref.read(selectedDateProvider.notifier).state = newDate;
        break;
    }
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context, WidgetRef ref) async {
    final currentDate = ref.read(selectedDateProvider);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (selectedDate != null) {
      ref.read(selectedDateProvider.notifier).state = selectedDate;
      ref.read(usageStatisticsProvider.notifier).updateDate(selectedDate);
    }
  }

  /// تنسيق نطاق التاريخ للعرض
  String _formatDateRange(DateTime date, UsageViewType viewType) {
    switch (viewType) {
      case UsageViewType.daily:
        return '${date.day}/${date.month}/${date.year}';
      case UsageViewType.weekly:
        final weekStart = date.subtract(Duration(days: date.weekday - 1));
        final weekEnd = weekStart.add(const Duration(days: 6));
        return '${weekStart.day}/${weekStart.month} - ${weekEnd.day}/${weekEnd.month}';
      case UsageViewType.monthly:
        return '${date.month}/${date.year}';
    }
  }

  /// عرض حوار الإعدادات
  void _showSettingsDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعدادات التتبع'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.security),
                  title: const Text('إدارة الأذونات'),
                  onTap: () {
                    Navigator.pop(context);
                    // فتح إعدادات الأذونات
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.delete),
                  title: const Text('مسح البيانات القديمة'),
                  onTap: () {
                    Navigator.pop(context);
                    _showCleanupDialog(context, ref);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار تنظيف البيانات
  void _showCleanupDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مسح البيانات القديمة'),
            content: const Text(
              'هل تريد مسح بيانات الاستخدام الأقدم من 30 يوماً؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  final service = ref.read(usageTrackingServiceProvider);
                  await service.cleanupOldData();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم مسح البيانات القديمة')),
                    );
                  }
                },
                child: const Text('مسح'),
              ),
            ],
          ),
    );
  }
}

/// بطاقة التحميل
class _LoadingCard extends StatelessWidget {
  const _LoadingCard();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        height: 120,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: const Center(child: CircularProgressIndicator()),
      ),
    );
  }
}

/// بطاقة الخطأ
class _ErrorCard extends StatelessWidget {
  final String error;

  const _ErrorCard({required this.error});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            const Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              error,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// بطاقة نظرة عامة أسبوعية
class _WeeklyOverviewCard extends StatelessWidget {
  final WeeklyUsageStatistics statistics;

  const _WeeklyOverviewCard({required this.statistics});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نظرة عامة أسبوعية',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _StatItem(
                    title: 'إجمالي الوقت',
                    value: '${statistics.totalWeeklyHours.toStringAsFixed(1)}س',
                    icon: Icons.access_time,
                  ),
                ),
                Expanded(
                  child: _StatItem(
                    title: 'متوسط يومي',
                    value:
                        '${statistics.averageDailyHours.toStringAsFixed(1)}س',
                    icon: Icons.today,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: _StatItem(
                    title: 'نقاط الجودة',
                    value:
                        '${statistics.averageQualityScore.toStringAsFixed(0)}%',
                    icon: Icons.star,
                  ),
                ),
                Expanded(
                  child: _StatItem(
                    title: 'الاتجاه',
                    value: statistics.improvementTrend > 0 ? 'تحسن' : 'تراجع',
                    icon:
                        statistics.improvementTrend > 0
                            ? Icons.trending_up
                            : Icons.trending_down,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// عنصر إحصائية
class _StatItem extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;

  const _StatItem({
    required this.title,
    required this.value,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, size: 32, color: AppTheme.primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
