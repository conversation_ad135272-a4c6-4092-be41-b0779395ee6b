import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/providers/ai_providers.dart';
import '../../../core/models/adaptive_goal.dart';

/// قسم الأهداف التكيفية
class AdaptiveGoalsSection extends ConsumerWidget {
  const AdaptiveGoalsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goalsAsync = ref.watch(adaptiveGoalsProvider);
    final activeGoalsAsync = ref.watch(activeAdaptiveGoalsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(Icons.flag, color: AppTheme.successColor),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'الأهداف التكيفية',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showAllGoals(context, ref),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // الأهداف النشطة
            activeGoalsAsync.when(
              data: (activeGoals) {
                if (activeGoals.isNotEmpty) {
                  return Column(
                    children: activeGoals.take(3).map((goal) => 
                      _GoalCard(
                        goal: goal,
                        onUpdate: (value) => _updateGoalProgress(ref, goal, value),
                      ),
                    ).toList(),
                  );
                }
                return _EmptyGoalsState();
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _ErrorState(error: error.toString()),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // إحصائيات الأهداف
            goalsAsync.when(
              data: (goals) => _GoalStatistics(goals: goals),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  /// تحديث تقدم الهدف
  void _updateGoalProgress(WidgetRef ref, AdaptiveGoal goal, double value) async {
    try {
      await ref.read(adaptiveGoalsProvider.notifier).updateGoalProgress(goal.id!, value);
    } catch (e) {
      // معالجة الخطأ
    }
  }

  /// عرض جميع الأهداف
  void _showAllGoals(BuildContext context, WidgetRef ref) {
    // تنفيذ عرض جميع الأهداف
  }
}

/// بطاقة الهدف
class _GoalCard extends StatelessWidget {
  final AdaptiveGoal goal;
  final Function(double) onUpdate;

  const _GoalCard({
    required this.goal,
    required this.onUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: _getCategoryColor(goal.category).withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getCategoryColor(goal.category).withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان والفئة
          Row(
            children: [
              Icon(
                _getCategoryIcon(goal.category),
                size: 20,
                color: _getCategoryColor(goal.category),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Text(
                  goal.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: _getCategoryColor(goal.category),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  goal.category.displayName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // الوصف
          Text(
            goal.description,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // شريط التقدم
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: goal.progressPercentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getCategoryColor(goal.category),
                  ),
                  minHeight: 6,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                '${goal.progressPercentage.toStringAsFixed(0)}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getCategoryColor(goal.category),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // معلومات إضافية
          Row(
            children: [
              _InfoChip(
                icon: Icons.trending_up,
                label: '${goal.currentValue.toStringAsFixed(0)} / ${goal.targetValue.toStringAsFixed(0)} ${goal.unit}',
              ),
              const SizedBox(width: AppConstants.smallPadding),
              _InfoChip(
                icon: Icons.schedule,
                label: '${goal.daysRemaining} يوم متبقي',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// الحصول على لون الفئة
  Color _getCategoryColor(AdaptiveGoalCategory category) {
    switch (category) {
      case AdaptiveGoalCategory.productivity:
        return AppTheme.primaryColor;
      case AdaptiveGoalCategory.digitalWellbeing:
        return AppTheme.successColor;
      case AdaptiveGoalCategory.focus:
        return Colors.orange;
      case AdaptiveGoalCategory.health:
        return Colors.red;
      case AdaptiveGoalCategory.taskManagement:
        return Colors.blue;
      case AdaptiveGoalCategory.timeManagement:
        return Colors.purple;
    }
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(AdaptiveGoalCategory category) {
    switch (category) {
      case AdaptiveGoalCategory.productivity:
        return Icons.trending_up;
      case AdaptiveGoalCategory.digitalWellbeing:
        return Icons.health_and_safety;
      case AdaptiveGoalCategory.focus:
        return Icons.center_focus_strong;
      case AdaptiveGoalCategory.health:
        return Icons.favorite;
      case AdaptiveGoalCategory.taskManagement:
        return Icons.task_alt;
      case AdaptiveGoalCategory.timeManagement:
        return Icons.schedule;
    }
  }
}

/// رقاقة المعلومات
class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;

  const _InfoChip({
    required this.icon,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 6,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 10, color: Colors.grey[600]),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 9,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

/// إحصائيات الأهداف
class _GoalStatistics extends StatelessWidget {
  final List<AdaptiveGoal> goals;

  const _GoalStatistics({required this.goals});

  @override
  Widget build(BuildContext context) {
    final activeCount = goals.where((g) => g.status == AdaptiveGoalStatus.active).length;
    final completedCount = goals.where((g) => g.status == AdaptiveGoalStatus.completed).length;
    final averageProgress = goals.isNotEmpty 
        ? goals.map((g) => g.progressPercentage).reduce((a, b) => a + b) / goals.length
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _StatItem(
            label: 'نشط',
            value: '$activeCount',
            color: AppTheme.primaryColor,
          ),
          _StatItem(
            label: 'مكتمل',
            value: '$completedCount',
            color: AppTheme.successColor,
          ),
          _StatItem(
            label: 'متوسط التقدم',
            value: '${averageProgress.toStringAsFixed(0)}%',
            color: Colors.blue,
          ),
        ],
      ),
    );
  }
}

/// عنصر إحصائية
class _StatItem extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _StatItem({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}

/// حالة فارغة للأهداف
class _EmptyGoalsState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.flag_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'لا توجد أهداف نشطة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          Text(
            'سيتم إنشاء أهداف تكيفية بناءً على سلوكك',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// حالة الخطأ
class _ErrorState extends StatelessWidget {
  final String error;
  
  const _ErrorState({required this.error});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Icon(Icons.error, size: 48, color: Colors.red),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'خطأ في تحميل الأهداف',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            error,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
