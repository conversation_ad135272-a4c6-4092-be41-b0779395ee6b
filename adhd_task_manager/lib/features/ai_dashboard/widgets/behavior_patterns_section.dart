import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/providers/ai_providers.dart';
import '../../../core/models/user_behavior_pattern.dart';

/// قسم أنماط السلوك
class BehaviorPatternsSection extends ConsumerWidget {
  const BehaviorPatternsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final patternsAsync = ref.watch(userBehaviorPatternsProvider);
    final strongPatternsAsync = ref.watch(strongBehaviorPatternsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(Icons.pattern, color: AppTheme.primaryColor),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'أنماط السلوك المكتشفة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showAllPatterns(context, ref),
                  child: const Text('عرض التفاصيل'),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // الأنماط القوية
            strongPatternsAsync.when(
              data: (strongPatterns) {
                if (strongPatterns.isNotEmpty) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.trending_up,
                              size: 16,
                              color: AppTheme.successColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'أنماط قوية (${strongPatterns.length})',
                              style: TextStyle(
                                color: AppTheme.successColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      ...strongPatterns.take(2).map((pattern) => 
                        _PatternCard(pattern: pattern),
                      ),
                    ],
                  );
                }
                return const SizedBox.shrink();
              },
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
            
            // جميع الأنماط
            patternsAsync.when(
              data: (patterns) {
                final normalPatterns = patterns
                    .where((pattern) => pattern.strength != PatternStrength.strong)
                    .take(3)
                    .toList();
                
                if (patterns.isEmpty) {
                  return _EmptyPatternsState();
                }
                
                return Column(
                  children: [
                    if (normalPatterns.isNotEmpty) ...[
                      const SizedBox(height: AppConstants.smallPadding),
                      ...normalPatterns.map((pattern) => 
                        _PatternCard(pattern: pattern),
                      ),
                    ],
                    
                    // إحصائيات سريعة
                    const SizedBox(height: AppConstants.defaultPadding),
                    _PatternStatistics(patterns: patterns),
                  ],
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stack) => _ErrorState(error: error.toString()),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض جميع الأنماط
  void _showAllPatterns(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              Text(
                'تحليل أنماط السلوك',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final patternsAsync = ref.watch(userBehaviorPatternsProvider);
                    return patternsAsync.when(
                      data: (patterns) => ListView.builder(
                        controller: scrollController,
                        itemCount: patterns.length,
                        itemBuilder: (context, index) {
                          final pattern = patterns[index];
                          return _DetailedPatternCard(pattern: pattern);
                        },
                      ),
                      loading: () => const Center(child: CircularProgressIndicator()),
                      error: (error, _) => Center(child: Text('خطأ: $error')),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// بطاقة النمط
class _PatternCard extends StatelessWidget {
  final UserBehaviorPattern pattern;

  const _PatternCard({required this.pattern});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: _getStrengthColor(pattern.strength).withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStrengthColor(pattern.strength).withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          // أيقونة النوع
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getTypeColor(pattern.type).withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getTypeIcon(pattern.type),
              size: 20,
              color: _getTypeColor(pattern.type),
            ),
          ),
          
          const SizedBox(width: AppConstants.smallPadding),
          
          // المحتوى
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        pattern.type.displayName,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getStrengthColor(pattern.strength),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        pattern.strength.displayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 2),
                
                Text(
                  pattern.description ?? pattern.recommendation,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 4),
                
                Row(
                  children: [
                    Icon(
                      Icons.trending_up,
                      size: 12,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'ثقة: ${(pattern.confidence * 100).toStringAsFixed(0)}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontSize: 10,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Icon(
                      Icons.repeat,
                      size: 12,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'تكرار: ${pattern.frequency}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون قوة النمط
  Color _getStrengthColor(PatternStrength strength) {
    switch (strength) {
      case PatternStrength.strong:
        return AppTheme.successColor;
      case PatternStrength.moderate:
        return AppTheme.warningColor;
      case PatternStrength.weak:
        return Colors.orange;
      case PatternStrength.emerging:
        return Colors.grey;
    }
  }

  /// الحصول على لون نوع النمط
  Color _getTypeColor(BehaviorPatternType type) {
    switch (type) {
      case BehaviorPatternType.excessiveUsage:
        return AppTheme.errorColor;
      case BehaviorPatternType.nightUsage:
        return Colors.indigo;
      case BehaviorPatternType.frequentDistraction:
        return Colors.orange;
      case BehaviorPatternType.highProductivity:
        return AppTheme.successColor;
      case BehaviorPatternType.peakUsageTime:
        return AppTheme.primaryColor;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع النمط
  IconData _getTypeIcon(BehaviorPatternType type) {
    switch (type) {
      case BehaviorPatternType.excessiveUsage:
        return Icons.warning;
      case BehaviorPatternType.nightUsage:
        return Icons.bedtime;
      case BehaviorPatternType.frequentDistraction:
        return Icons.scatter_plot;
      case BehaviorPatternType.highProductivity:
        return Icons.trending_up;
      case BehaviorPatternType.peakUsageTime:
        return Icons.schedule;
      default:
        return Icons.insights;
    }
  }
}

/// بطاقة النمط المفصلة
class _DetailedPatternCard extends StatelessWidget {
  final UserBehaviorPattern pattern;

  const _DetailedPatternCard({required this.pattern});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getTypeIcon(pattern.type),
                  color: _getTypeColor(pattern.type),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    pattern.type.displayName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStrengthColor(pattern.strength),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    pattern.strength.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Text(
              pattern.description ?? pattern.recommendation,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Row(
              children: [
                _InfoChip(
                  icon: Icons.trending_up,
                  label: 'ثقة: ${(pattern.confidence * 100).toStringAsFixed(0)}%',
                ),
                const SizedBox(width: AppConstants.smallPadding),
                _InfoChip(
                  icon: Icons.repeat,
                  label: 'تكرار: ${pattern.frequency}',
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Text(
              'التوصية: ${pattern.recommendation}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStrengthColor(PatternStrength strength) {
    switch (strength) {
      case PatternStrength.strong:
        return AppTheme.successColor;
      case PatternStrength.moderate:
        return AppTheme.warningColor;
      case PatternStrength.weak:
        return Colors.orange;
      case PatternStrength.emerging:
        return Colors.grey;
    }
  }

  Color _getTypeColor(BehaviorPatternType type) {
    switch (type) {
      case BehaviorPatternType.excessiveUsage:
        return AppTheme.errorColor;
      case BehaviorPatternType.nightUsage:
        return Colors.indigo;
      case BehaviorPatternType.frequentDistraction:
        return Colors.orange;
      case BehaviorPatternType.highProductivity:
        return AppTheme.successColor;
      case BehaviorPatternType.peakUsageTime:
        return AppTheme.primaryColor;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(BehaviorPatternType type) {
    switch (type) {
      case BehaviorPatternType.excessiveUsage:
        return Icons.warning;
      case BehaviorPatternType.nightUsage:
        return Icons.bedtime;
      case BehaviorPatternType.frequentDistraction:
        return Icons.scatter_plot;
      case BehaviorPatternType.highProductivity:
        return Icons.trending_up;
      case BehaviorPatternType.peakUsageTime:
        return Icons.schedule;
      default:
        return Icons.insights;
    }
  }
}

/// رقاقة المعلومات
class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;

  const _InfoChip({
    required this.icon,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

/// إحصائيات الأنماط
class _PatternStatistics extends StatelessWidget {
  final List<UserBehaviorPattern> patterns;

  const _PatternStatistics({required this.patterns});

  @override
  Widget build(BuildContext context) {
    final strongCount = patterns.where((p) => p.strength == PatternStrength.strong).length;
    final moderateCount = patterns.where((p) => p.strength == PatternStrength.moderate).length;
    final averageConfidence = patterns.isNotEmpty 
        ? patterns.map((p) => p.confidence).reduce((a, b) => a + b) / patterns.length
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _StatItem(
            label: 'إجمالي',
            value: '${patterns.length}',
            color: AppTheme.primaryColor,
          ),
          _StatItem(
            label: 'قوي',
            value: '$strongCount',
            color: AppTheme.successColor,
          ),
          _StatItem(
            label: 'متوسط',
            value: '$moderateCount',
            color: AppTheme.warningColor,
          ),
          _StatItem(
            label: 'متوسط الثقة',
            value: '${(averageConfidence * 100).toStringAsFixed(0)}%',
            color: Colors.blue,
          ),
        ],
      ),
    );
  }
}

/// عنصر إحصائية
class _StatItem extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _StatItem({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}

/// حالة فارغة للأنماط
class _EmptyPatternsState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.pattern,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'لم يتم اكتشاف أنماط بعد',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          Text(
            'استمر في استخدام التطبيق لتحليل أنماط سلوكك',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// حالة الخطأ
class _ErrorState extends StatelessWidget {
  final String error;
  
  const _ErrorState({required this.error});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Icon(Icons.error, size: 48, color: Colors.red),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'خطأ في تحميل الأنماط',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            error,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
