import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/providers/ai_providers.dart';
import '../../../core/models/smart_recommendation.dart';

/// قسم التوصيات الذكية
class SmartRecommendationsSection extends ConsumerWidget {
  const SmartRecommendationsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recommendationsAsync = ref.watch(smartRecommendationsProvider);
    final highPriorityAsync = ref.watch(highPriorityRecommendationsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(Icons.lightbulb, color: AppTheme.warningColor),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'التوصيات الذكية',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showAllRecommendations(context, ref),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // التوصيات عالية الأولوية
            highPriorityAsync.when(
              data: (highPriorityRecs) {
                if (highPriorityRecs.isNotEmpty) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.errorColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.priority_high,
                              size: 16,
                              color: AppTheme.errorColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'عاجل (${highPriorityRecs.length})',
                              style: TextStyle(
                                color: AppTheme.errorColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      ...highPriorityRecs.take(2).map((rec) => 
                        _RecommendationCard(
                          recommendation: rec,
                          onAction: () => _executeRecommendation(context, ref, rec),
                          onDismiss: () => _dismissRecommendation(context, ref, rec),
                        ),
                      ),
                    ],
                  );
                }
                return const SizedBox.shrink();
              },
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
            
            // التوصيات العامة
            recommendationsAsync.when(
              data: (recommendations) {
                final normalRecs = recommendations
                    .where((rec) => rec.priority != RecommendationPriority.high && 
                                   rec.priority != RecommendationPriority.urgent)
                    .take(3)
                    .toList();
                
                if (normalRecs.isEmpty) {
                  return _EmptyRecommendationsState();
                }
                
                return Column(
                  children: normalRecs.map((rec) => 
                    _RecommendationCard(
                      recommendation: rec,
                      onAction: () => _executeRecommendation(context, ref, rec),
                      onDismiss: () => _dismissRecommendation(context, ref, rec),
                    ),
                  ).toList(),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stack) => _ErrorState(error: error.toString()),
            ),
          ],
        ),
      ),
    );
  }

  /// تنفيذ التوصية
  void _executeRecommendation(BuildContext context, WidgetRef ref, SmartRecommendation recommendation) async {
    try {
      final aiController = ref.read(aiControllerProvider);
      await aiController.executeRecommendationWithUpdate(recommendation.id!);
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تنفيذ التوصية: ${recommendation.title}')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تنفيذ التوصية: $e')),
        );
      }
    }
  }

  /// رفض التوصية
  void _dismissRecommendation(BuildContext context, WidgetRef ref, SmartRecommendation recommendation) async {
    try {
      await ref.read(smartRecommendationsProvider.notifier).markAsRead(recommendation.id!);
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم رفض التوصية')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في رفض التوصية: $e')),
        );
      }
    }
  }

  /// عرض جميع التوصيات
  void _showAllRecommendations(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              Text(
                'جميع التوصيات الذكية',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final recommendationsAsync = ref.watch(smartRecommendationsProvider);
                    return recommendationsAsync.when(
                      data: (recommendations) => ListView.builder(
                        controller: scrollController,
                        itemCount: recommendations.length,
                        itemBuilder: (context, index) {
                          final rec = recommendations[index];
                          return _RecommendationCard(
                            recommendation: rec,
                            onAction: () => _executeRecommendation(context, ref, rec),
                            onDismiss: () => _dismissRecommendation(context, ref, rec),
                          );
                        },
                      ),
                      loading: () => const Center(child: CircularProgressIndicator()),
                      error: (error, _) => Center(child: Text('خطأ: $error')),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// بطاقة التوصية
class _RecommendationCard extends StatelessWidget {
  final SmartRecommendation recommendation;
  final VoidCallback onAction;
  final VoidCallback onDismiss;

  const _RecommendationCard({
    required this.recommendation,
    required this.onAction,
    required this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: _getPriorityColor(recommendation.priority).withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getPriorityColor(recommendation.priority).withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان والأولوية
          Row(
            children: [
              Icon(
                _getTypeIcon(recommendation.type),
                size: 20,
                color: _getPriorityColor(recommendation.priority),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: _getPriorityColor(recommendation.priority),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  recommendation.priority.displayName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // الوصف
          Text(
            recommendation.description,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // الأزرار
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: onAction,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _getPriorityColor(recommendation.priority),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  child: Text(
                    recommendation.actionText,
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              TextButton(
                onPressed: onDismiss,
                child: const Text(
                  'رفض',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
          
          // العلامات
          if (recommendation.tags.isNotEmpty) ...[
            const SizedBox(height: AppConstants.smallPadding),
            Wrap(
              spacing: 4,
              children: recommendation.tags.take(3).map((tag) => 
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(fontSize: 10),
                  ),
                ),
              ).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(RecommendationPriority priority) {
    switch (priority) {
      case RecommendationPriority.urgent:
        return AppTheme.errorColor;
      case RecommendationPriority.high:
        return Colors.orange;
      case RecommendationPriority.medium:
        return AppTheme.warningColor;
      case RecommendationPriority.low:
        return Colors.blue;
    }
  }

  /// الحصول على أيقونة النوع
  IconData _getTypeIcon(RecommendationType type) {
    switch (type) {
      case RecommendationType.focusMode:
        return Icons.center_focus_strong;
      case RecommendationType.takeBreak:
        return Icons.pause_circle;
      case RecommendationType.reduceUsage:
        return Icons.trending_down;
      case RecommendationType.createTask:
        return Icons.add_task;
      case RecommendationType.improveHabits:
        return Icons.self_improvement;
      case RecommendationType.personalChallenge:
        return Icons.emoji_events;
      case RecommendationType.healthReminder:
        return Icons.health_and_safety;
      case RecommendationType.productivityTip:
        return Icons.tips_and_updates;
    }
  }
}

/// حالة فارغة للتوصيات
class _EmptyRecommendationsState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'لا توجد توصيات حالياً',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          Text(
            'استمر في استخدام التطبيق للحصول على توصيات ذكية',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// حالة الخطأ
class _ErrorState extends StatelessWidget {
  final String error;
  
  const _ErrorState({required this.error});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Icon(Icons.error, size: 48, color: Colors.red),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'خطأ في تحميل التوصيات',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            error,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
