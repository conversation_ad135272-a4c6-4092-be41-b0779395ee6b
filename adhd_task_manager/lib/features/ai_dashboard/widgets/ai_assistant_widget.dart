import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/providers/ai_providers.dart';

/// ويدجت المساعد الذكي
class AIAssistantWidget extends ConsumerStatefulWidget {
  const AIAssistantWidget({super.key});

  @override
  ConsumerState<AIAssistantWidget> createState() => _AIAssistantWidgetState();
}

class _AIAssistantWidgetState extends ConsumerState<AIAssistantWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  String _currentMessage = '';
  bool _isThinking = false;

  final List<String> _greetings = [
    'مرحباً! كيف يمكنني مساعدتك اليوم؟',
    'أهلاً بك! أنا هنا لتحسين إنتاجيتك.',
    'مرحباً! دعني أساعدك في تحقيق أهدافك.',
    'أهلاً! كيف كان أداؤك اليوم؟',
    'مرحباً! هل تريد نصائح لتحسين تركيزك؟',
  ];

  final List<String> _tips = [
    'تذكر أن تأخذ استراحة كل 25 دقيقة من العمل.',
    'حاول تقليل التنقل بين التطبيقات لتحسين تركيزك.',
    'ضع هاتفك في وضع عدم الإزعاج أثناء العمل المهم.',
    'اشرب الماء بانتظام للحفاظ على تركيزك.',
    'نظم مهامك حسب الأولوية لتحقيق أفضل النتائج.',
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setRandomGreeting();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  void _setRandomGreeting() {
    final random = Random();
    setState(() {
      _currentMessage = _greetings[random.nextInt(_greetings.length)];
    });
  }

  @override
  Widget build(BuildContext context) {
    final aiInsights = ref.watch(aiInsightsProvider);

    return Card(
      elevation: 3,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor.withOpacity(0.05),
              Colors.blue.withOpacity(0.05),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس المساعد
              Row(
                children: [
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryColor.withOpacity(0.3),
                                blurRadius: 8,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.psychology,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المساعد الذكي',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Text(
                          _isThinking ? 'يفكر...' : 'متاح للمساعدة',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _generateNewMessage,
                    color: AppTheme.primaryColor,
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // رسالة المساعد
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Text(
                        _currentMessage,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // اقتراحات سريعة
              _QuickSuggestions(
                insights: aiInsights,
                onSuggestionTap: _handleSuggestionTap,
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _performAnalysis(),
                      icon: const Icon(Icons.analytics, size: 16),
                      label: const Text('تحليل ذكي'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showTips(),
                      icon: const Icon(Icons.tips_and_updates, size: 16),
                      label: const Text('نصائح'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppTheme.primaryColor,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// توليد رسالة جديدة
  void _generateNewMessage() {
    setState(() {
      _isThinking = true;
    });

    // محاكاة التفكير
    Future.delayed(const Duration(seconds: 1), () {
      final aiInsights = ref.read(aiInsightsProvider);
      final message = _generateContextualMessage(aiInsights);
      
      setState(() {
        _currentMessage = message;
        _isThinking = false;
      });
    });
  }

  /// توليد رسالة سياقية
  String _generateContextualMessage(AIInsights insights) {
    final random = Random();
    
    // رسائل بناءً على الحالة
    if (insights.highPriorityRecommendations > 0) {
      return 'لديك ${insights.highPriorityRecommendations} توصية عاجلة. هل تريد مراجعتها؟';
    }
    
    if (insights.averageGoalProgress > 80) {
      return 'أداء ممتاز! تقدمك في الأهداف ${insights.averageGoalProgress.toStringAsFixed(0)}%. استمر!';
    }
    
    if (insights.averageGoalProgress < 30) {
      return 'تحتاج لزيادة التركيز على أهدافك. دعني أساعدك في وضع خطة.';
    }
    
    if (insights.strongPatterns > 0) {
      return 'اكتشفت ${insights.strongPatterns} نمط سلوك قوي. هل تريد معرفة التفاصيل؟';
    }
    
    // رسائل عامة
    final generalMessages = [
      'كيف يمكنني مساعدتك في تحسين إنتاجيتك اليوم؟',
      'هل تريد نصائح مخصصة بناءً على سلوكك؟',
      'دعني أساعدك في تحليل أنماط استخدامك.',
      'ما رأيك في تجربة تحدي جديد لتحسين عاداتك؟',
    ];
    
    return generalMessages[random.nextInt(generalMessages.length)];
  }

  /// معالجة النقر على الاقتراح
  void _handleSuggestionTap(String suggestion) {
    setState(() {
      _currentMessage = 'اخترت: $suggestion. دعني أساعدك في تنفيذه.';
    });
  }

  /// تنفيذ التحليل
  void _performAnalysis() async {
    setState(() {
      _isThinking = true;
      _currentMessage = 'أقوم بتحليل بياناتك...';
    });

    try {
      final aiController = ref.read(aiControllerProvider);
      await aiController.performFullAnalysis();
      
      setState(() {
        _currentMessage = 'تم التحليل بنجاح! تحقق من التوصيات الجديدة.';
        _isThinking = false;
      });
    } catch (e) {
      setState(() {
        _currentMessage = 'حدث خطأ في التحليل. حاول مرة أخرى.';
        _isThinking = false;
      });
    }
  }

  /// عرض النصائح
  void _showTips() {
    final random = Random();
    final tip = _tips[random.nextInt(_tips.length)];
    
    setState(() {
      _currentMessage = 'نصيحة: $tip';
    });
  }
}

/// اقتراحات سريعة
class _QuickSuggestions extends StatelessWidget {
  final AIInsights insights;
  final Function(String) onSuggestionTap;

  const _QuickSuggestions({
    required this.insights,
    required this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    final suggestions = _generateSuggestions();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اقتراحات سريعة:',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: suggestions.map((suggestion) => 
            GestureDetector(
              onTap: () => onSuggestionTap(suggestion),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  suggestion,
                  style: TextStyle(
                    fontSize: 11,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ).toList(),
        ),
      ],
    );
  }

  /// توليد الاقتراحات
  List<String> _generateSuggestions() {
    final suggestions = <String>[];

    if (insights.highPriorityRecommendations > 0) {
      suggestions.add('مراجعة التوصيات العاجلة');
    }

    if (insights.activeGoals > 0) {
      suggestions.add('تحديث تقدم الأهداف');
    }

    if (insights.strongPatterns > 0) {
      suggestions.add('تحليل الأنماط القوية');
    }

    // اقتراحات عامة
    suggestions.addAll([
      'تفعيل وضع التركيز',
      'أخذ استراحة',
      'مراجعة الإحصائيات',
      'إنشاء هدف جديد',
    ]);

    return suggestions.take(4).toList();
  }
}
