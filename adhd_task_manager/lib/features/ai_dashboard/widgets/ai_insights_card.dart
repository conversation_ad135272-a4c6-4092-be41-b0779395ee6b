import 'package:flutter/material.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/providers/ai_providers.dart';

/// بطاقة رؤى الذكاء الاصطناعي
class AIInsightsCard extends StatelessWidget {
  final AIInsights insights;

  const AIInsightsCard({
    super.key,
    required this.insights,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor.withOpacity(0.1),
              AppTheme.primaryColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.psychology,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'رؤى الذكاء الاصطناعي',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Text(
                          'تحليل ذكي لسلوكك وأدائك',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // شارة التقييم العام
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getAssessmentColor(insights.overallAssessment),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      insights.overallAssessment,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // الإحصائيات الرئيسية
              Row(
                children: [
                  Expanded(
                    child: _InsightMetric(
                      icon: Icons.pattern,
                      title: 'أنماط السلوك',
                      value: '${insights.totalPatterns}',
                      subtitle: '${insights.strongPatterns} قوي',
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _InsightMetric(
                      icon: Icons.lightbulb,
                      title: 'التوصيات',
                      value: '${insights.activeRecommendations}',
                      subtitle: '${insights.highPriorityRecommendations} عاجل',
                      color: AppTheme.warningColor,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _InsightMetric(
                      icon: Icons.flag,
                      title: 'الأهداف',
                      value: '${insights.activeGoals}',
                      subtitle: '${insights.completedGoals} مكتمل',
                      color: AppTheme.successColor,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // شريط التقدم العام
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'متوسط تقدم الأهداف',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${insights.averageGoalProgress.toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  LinearProgressIndicator(
                    value: insights.averageGoalProgress / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getProgressColor(insights.averageGoalProgress),
                    ),
                    minHeight: 8,
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // رؤى سريعة
              _QuickInsights(insights: insights),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على لون التقييم
  Color _getAssessmentColor(String assessment) {
    switch (assessment) {
      case 'ممتاز':
        return AppTheme.successColor;
      case 'جيد':
        return Colors.blue;
      case 'متوسط':
        return AppTheme.warningColor;
      default:
        return AppTheme.errorColor;
    }
  }

  /// الحصول على لون التقدم
  Color _getProgressColor(double progress) {
    if (progress >= 80) return AppTheme.successColor;
    if (progress >= 60) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }
}

/// مؤشر رؤية
class _InsightMetric extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final String subtitle;
  final Color color;

  const _InsightMetric({
    required this.icon,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 9,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// رؤى سريعة
class _QuickInsights extends StatelessWidget {
  final AIInsights insights;

  const _QuickInsights({required this.insights});

  @override
  Widget build(BuildContext context) {
    final quickInsights = _generateQuickInsights();

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.insights, size: 16, color: AppTheme.primaryColor),
              const SizedBox(width: 4),
              Text(
                'رؤى سريعة',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          ...quickInsights.map((insight) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Icon(
                  Icons.circle,
                  size: 6,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    insight,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// توليد رؤى سريعة
  List<String> _generateQuickInsights() {
    final insights = <String>[];

    // رؤى حول الأنماط
    if (this.insights.strongPatterns > 0) {
      insights.add('تم اكتشاف ${this.insights.strongPatterns} نمط سلوك قوي');
    }

    // رؤى حول التوصيات
    if (this.insights.highPriorityRecommendations > 0) {
      insights.add('لديك ${this.insights.highPriorityRecommendations} توصية عاجلة');
    }

    // رؤى حول الأهداف
    if (this.insights.averageGoalProgress > 80) {
      insights.add('أداء ممتاز في تحقيق الأهداف!');
    } else if (this.insights.averageGoalProgress < 30) {
      insights.add('تحتاج لزيادة التركيز على أهدافك');
    }

    // رؤى حول التقييم العام
    if (this.insights.overallAssessment == 'ممتاز') {
      insights.add('تحافظ على أداء متميز');
    } else if (this.insights.overallAssessment == 'يحتاج تحسين') {
      insights.add('فرصة جيدة لتطوير عاداتك');
    }

    // إذا لم توجد رؤى، أضف رؤية عامة
    if (insights.isEmpty) {
      insights.add('استمر في استخدام التطبيق لتحليل أفضل');
    }

    return insights.take(3).toList(); // أقصى 3 رؤى
  }
}
