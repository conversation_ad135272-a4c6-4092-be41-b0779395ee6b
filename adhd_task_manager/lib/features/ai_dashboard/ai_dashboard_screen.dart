import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/providers/ai_providers.dart';
import 'widgets/ai_insights_card.dart';
import 'widgets/smart_recommendations_section.dart';
import 'widgets/behavior_patterns_section.dart';
import 'widgets/adaptive_goals_section.dart';
import 'widgets/ai_assistant_widget.dart';

/// شاشة لوحة القيادة الذكية
class AIDashboardScreen extends ConsumerWidget {
  const AIDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final aiInsights = ref.watch(aiInsightsProvider);
    final aiController = ref.read(aiControllerProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة القيادة الذكية'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              try {
                await aiController.refreshAll();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم تحديث البيانات بنجاح')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ في التحديث: $e')),
                  );
                }
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () async {
              try {
                await aiController.performFullAnalysis();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إجراء التحليل الشامل')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ في التحليل: $e')),
                  );
                }
              }
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await aiController.refreshAll();
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رؤى الذكاء الاصطناعي
              AIInsightsCard(insights: aiInsights),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // المساعد الذكي
              const AIAssistantWidget(),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // التوصيات الذكية
              const SmartRecommendationsSection(),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // أنماط السلوك
              const BehaviorPatternsSection(),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // الأهداف التكيفية
              const AdaptiveGoalsSection(),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // إحصائيات متقدمة
              _AdvancedAnalyticsSection(),
            ],
          ),
        ),
      ),
      
      // زر عائم للتحليل السريع
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          try {
            await aiController.performFullAnalysis();
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إجراء التحليل الذكي')),
              );
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ: $e')),
              );
            }
          }
        },
        icon: const Icon(Icons.psychology),
        label: const Text('تحليل ذكي'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}

/// قسم التحليلات المتقدمة
class _AdvancedAnalyticsSection extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final aiInsights = ref.watch(aiInsightsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: AppTheme.primaryColor),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'التحليلات المتقدمة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // مؤشرات الأداء
            Row(
              children: [
                Expanded(
                  child: _MetricCard(
                    title: 'قوة الأنماط',
                    value: '${(aiInsights.patternStrengthRatio * 100).toStringAsFixed(0)}%',
                    icon: Icons.pattern,
                    color: _getColorForPercentage(aiInsights.patternStrengthRatio),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _MetricCard(
                    title: 'إكمال الأهداف',
                    value: '${(aiInsights.goalCompletionRatio * 100).toStringAsFixed(0)}%',
                    icon: Icons.flag,
                    color: _getColorForPercentage(aiInsights.goalCompletionRatio),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Row(
              children: [
                Expanded(
                  child: _MetricCard(
                    title: 'التقييم العام',
                    value: aiInsights.overallAssessment,
                    icon: Icons.assessment,
                    color: _getColorForAssessment(aiInsights.overallAssessment),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _MetricCard(
                    title: 'التوصيات النشطة',
                    value: '${aiInsights.activeRecommendations}',
                    icon: Icons.lightbulb,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // شريط التقدم العام
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'متوسط تقدم الأهداف',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                LinearProgressIndicator(
                  value: aiInsights.averageGoalProgress / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getColorForPercentage(aiInsights.averageGoalProgress / 100),
                  ),
                  minHeight: 8,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  '${aiInsights.averageGoalProgress.toStringAsFixed(1)}%',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون بناءً على النسبة المئوية
  Color _getColorForPercentage(double percentage) {
    if (percentage >= 0.8) return AppTheme.successColor;
    if (percentage >= 0.6) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }

  /// الحصول على لون بناءً على التقييم
  Color _getColorForAssessment(String assessment) {
    switch (assessment) {
      case 'ممتاز':
        return AppTheme.successColor;
      case 'جيد':
        return Colors.blue;
      case 'متوسط':
        return AppTheme.warningColor;
      default:
        return AppTheme.errorColor;
    }
  }
}

/// بطاقة مؤشر
class _MetricCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _MetricCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
