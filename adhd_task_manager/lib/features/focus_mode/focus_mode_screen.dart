import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/providers/focus_mode_providers.dart';
import '../../core/models/focus_mode.dart';
import 'widgets/focus_mode_card.dart';
import 'widgets/active_focus_mode_widget.dart';
import 'widgets/focus_mode_creation_dialog.dart';

/// شاشة إدارة أوضاع التركيز
class FocusModeScreen extends ConsumerWidget {
  const FocusModeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final focusModesAsync = ref.watch(focusModesProvider);
    final activeFocusMode = ref.watch(activeFocusModeProvider);
    final focusModeStatus = ref.watch(focusModeStatusProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('أوضاع التركيز'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(focusModesProvider.notifier).refresh();
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context, ref),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(focusModesProvider.notifier).refresh();
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // وضع التركيز النشط
              if (activeFocusMode != null)
                ActiveFocusModeWidget(
                  focusMode: activeFocusMode,
                  status: focusModeStatus,
                ),
              
              if (activeFocusMode != null)
                const SizedBox(height: AppConstants.defaultPadding),

              // اقتراح وضع التركيز
              if (activeFocusMode == null)
                _FocusModeSuggestionCard(),

              const SizedBox(height: AppConstants.defaultPadding),

              // عنوان أوضاع التركيز
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'أوضاع التركيز المتاحة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () => _showCreateFocusModeDialog(context, ref),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة وضع'),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // قائمة أوضاع التركيز
              focusModesAsync.when(
                data: (focusModes) {
                  if (focusModes.isEmpty) {
                    return _EmptyFocusModesState();
                  }

                  return ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: focusModes.length,
                    separatorBuilder: (context, index) => 
                        const SizedBox(height: AppConstants.smallPadding),
                    itemBuilder: (context, index) {
                      final focusMode = focusModes[index];
                      return FocusModeCard(
                        focusMode: focusMode,
                        isActive: activeFocusMode?.id == focusMode.id,
                        onActivate: () => _activateFocusMode(context, ref, focusMode),
                        onEdit: () => _editFocusMode(context, ref, focusMode),
                        onDelete: () => _deleteFocusMode(context, ref, focusMode),
                      );
                    },
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stack) => _ErrorState(error: error.toString()),
              ),
            ],
          ),
        ),
      ),
      
      // زر عائم لإضافة وضع تركيز جديد
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateFocusModeDialog(context, ref),
        icon: const Icon(Icons.add),
        label: const Text('وضع جديد'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  /// عرض حوار إنشاء وضع تركيز جديد
  void _showCreateFocusModeDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => FocusModeCreationDialog(
        onSave: (focusMode) async {
          try {
            await ref.read(focusModesProvider.notifier).createFocusMode(focusMode);
            if (context.mounted) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إنشاء وضع التركيز بنجاح')),
              );
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('فشل في إنشاء وضع التركيز: $e')),
              );
            }
          }
        },
      ),
    );
  }

  /// تفعيل وضع التركيز
  void _activateFocusMode(BuildContext context, WidgetRef ref, FocusMode focusMode) {
    showDialog(
      context: context,
      builder: (context) => _FocusModeActivationDialog(
        focusMode: focusMode,
        onActivate: (duration) async {
          try {
            final controller = ref.read(focusModeControllerProvider);
            final success = await controller.activateFocusModeWithNotification(
              focusMode,
              duration: duration,
            );
            
            if (context.mounted) {
              Navigator.pop(context);
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('تم تفعيل "${focusMode.name}"')),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('فشل في تفعيل وضع التركيز')),
                );
              }
            }
          } catch (e) {
            if (context.mounted) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ: $e')),
              );
            }
          }
        },
      ),
    );
  }

  /// تعديل وضع التركيز
  void _editFocusMode(BuildContext context, WidgetRef ref, FocusMode focusMode) {
    showDialog(
      context: context,
      builder: (context) => FocusModeCreationDialog(
        focusMode: focusMode,
        onSave: (updatedFocusMode) async {
          try {
            await ref.read(focusModesProvider.notifier).updateFocusMode(updatedFocusMode);
            if (context.mounted) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تحديث وضع التركيز بنجاح')),
              );
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('فشل في تحديث وضع التركيز: $e')),
              );
            }
          }
        },
      ),
    );
  }

  /// حذف وضع التركيز
  void _deleteFocusMode(BuildContext context, WidgetRef ref, FocusMode focusMode) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف وضع التركيز'),
        content: Text('هل أنت متأكد من حذف "${focusMode.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await ref.read(focusModesProvider.notifier).deleteFocusMode(focusMode.id!);
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم حذف وضع التركيز')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('فشل في حذف وضع التركيز: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الإعدادات
  void _showSettingsDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات أوضاع التركيز'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('إعدادات التنبيهات'),
              onTap: () {
                Navigator.pop(context);
                // فتح شاشة إعدادات التنبيهات
              },
            ),
            ListTile(
              leading: const Icon(Icons.apps),
              title: const Text('إدارة التطبيقات المحجوبة'),
              onTap: () {
                Navigator.pop(context);
                // فتح شاشة إدارة التطبيقات
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

/// بطاقة اقتراح وضع التركيز
class _FocusModeSuggestionCard extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final suggestionAsync = ref.watch(focusModeSuggestionProvider);

    return suggestionAsync.when(
      data: (suggestion) {
        if (suggestion == null) return const SizedBox.shrink();

        return Card(
          color: AppTheme.primaryColor.withOpacity(0.1),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.lightbulb, color: AppTheme.primaryColor),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      'اقتراح ذكي',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'بناءً على الوقت الحالي، نقترح تفعيل "${suggestion.name}"',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: () => ref.read(focusModeControllerProvider)
                      .activateFocusModeWithNotification(suggestion),
                  child: const Text('تفعيل الآن'),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}

/// حالة فارغة لأوضاع التركيز
class _EmptyFocusModesState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.center_focus_strong,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'لا توجد أوضاع تركيز',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'أنشئ وضع تركيز جديد لتحسين إنتاجيتك',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// حالة الخطأ
class _ErrorState extends StatelessWidget {
  final String error;
  
  const _ErrorState({required this.error});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'خطأ في تحميل أوضاع التركيز',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// حوار تفعيل وضع التركيز
class _FocusModeActivationDialog extends StatefulWidget {
  final FocusMode focusMode;
  final Function(int?) onActivate;

  const _FocusModeActivationDialog({
    required this.focusMode,
    required this.onActivate,
  });

  @override
  State<_FocusModeActivationDialog> createState() => _FocusModeActivationDialogState();
}

class _FocusModeActivationDialogState extends State<_FocusModeActivationDialog> {
  int? _selectedDuration;
  final List<int> _durationOptions = [15, 25, 30, 45, 60, 90, 120];

  @override
  void initState() {
    super.initState();
    _selectedDuration = widget.focusMode.duration ?? 25;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تفعيل "${widget.focusMode.name}"'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(widget.focusMode.description),
          const SizedBox(height: AppConstants.defaultPadding),
          const Text('مدة الجلسة (بالدقائق):'),
          const SizedBox(height: AppConstants.smallPadding),
          Wrap(
            spacing: 8,
            children: _durationOptions.map((duration) {
              return ChoiceChip(
                label: Text('${duration}د'),
                selected: _selectedDuration == duration,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedDuration = duration;
                    });
                  }
                },
              );
            }).toList(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () => widget.onActivate(_selectedDuration),
          child: const Text('تفعيل'),
        ),
      ],
    );
  }
}
