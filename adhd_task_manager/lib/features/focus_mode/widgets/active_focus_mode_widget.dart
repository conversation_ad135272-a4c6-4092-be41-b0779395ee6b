import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/models/focus_mode.dart';
import '../../../core/providers/focus_mode_providers.dart';

/// ويدجت وضع التركيز النشط
class ActiveFocusModeWidget extends ConsumerStatefulWidget {
  final FocusMode focusMode;
  final FocusModeStatus status;

  const ActiveFocusModeWidget({
    super.key,
    required this.focusMode,
    required this.status,
  });

  @override
  ConsumerState<ActiveFocusModeWidget> createState() => _ActiveFocusModeWidgetState();
}

class _ActiveFocusModeWidgetState extends ConsumerState<ActiveFocusModeWidget>
    with TickerProviderStateMixin {
  Timer? _timer;
  Duration? _remainingTime;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  void _startTimer() {
    _updateRemainingTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateRemainingTime();
    });
  }

  void _updateRemainingTime() {
    if (mounted) {
      setState(() {
        _remainingTime = widget.focusMode.remainingTime;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      color: AppTheme.primaryColor.withOpacity(0.05),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.primaryColor.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              // العنوان والحالة
              Row(
                children: [
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.center_focus_strong,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      );
                    },
                  ),
                  
                  const SizedBox(width: AppConstants.defaultPadding),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.focusMode.name,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Text(
                          'وضع التركيز نشط',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // زر الإيقاف
                  IconButton(
                    onPressed: () => _deactivateFocusMode(),
                    icon: const Icon(Icons.stop),
                    style: IconButton.styleFrom(
                      backgroundColor: AppTheme.errorColor.withOpacity(0.1),
                      foregroundColor: AppTheme.errorColor,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // العداد التنازلي
              if (_remainingTime != null)
                _CountdownTimer(remainingTime: _remainingTime!),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // شريط التقدم
              if (_remainingTime != null && widget.focusMode.duration != null)
                _ProgressBar(
                  totalDuration: Duration(minutes: widget.focusMode.duration!),
                  remainingTime: _remainingTime!,
                ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // معلومات الوضع
              _ActiveModeInfo(focusMode: widget.focusMode),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _extendSession(),
                      icon: const Icon(Icons.add_alarm),
                      label: const Text('تمديد الجلسة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                        foregroundColor: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: AppConstants.smallPadding),
                  
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _deactivateFocusMode(),
                      icon: const Icon(Icons.stop),
                      label: const Text('إنهاء الجلسة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.errorColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// إلغاء تفعيل وضع التركيز
  void _deactivateFocusMode() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنهاء وضع التركيز'),
        content: const Text('هل أنت متأكد من إنهاء جلسة التركيز الحالية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                final controller = ref.read(focusModeControllerProvider);
                await controller.deactivateFocusModeWithNotification();
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إنهاء جلسة التركيز')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('إنهاء'),
          ),
        ],
      ),
    );
  }

  /// تمديد الجلسة
  void _extendSession() {
    showDialog(
      context: context,
      builder: (context) => _ExtendSessionDialog(
        onExtend: (additionalMinutes) async {
          Navigator.pop(context);
          // تنفيذ تمديد الجلسة
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم تمديد الجلسة بـ $additionalMinutes دقيقة')),
          );
        },
      ),
    );
  }
}

/// العداد التنازلي
class _CountdownTimer extends StatelessWidget {
  final Duration remainingTime;

  const _CountdownTimer({required this.remainingTime});

  @override
  Widget build(BuildContext context) {
    final hours = remainingTime.inHours;
    final minutes = remainingTime.inMinutes % 60;
    final seconds = remainingTime.inSeconds % 60;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            'الوقت المتبقي',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (hours > 0) ...[
                _TimeUnit(value: hours, label: 'ساعة'),
                const Text(' : ', style: TextStyle(fontSize: 24)),
              ],
              _TimeUnit(value: minutes, label: 'دقيقة'),
              const Text(' : ', style: TextStyle(fontSize: 24)),
              _TimeUnit(value: seconds, label: 'ثانية'),
            ],
          ),
        ],
      ),
    );
  }
}

/// وحدة الوقت
class _TimeUnit extends StatelessWidget {
  final int value;
  final String label;

  const _TimeUnit({required this.value, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          value.toString().padLeft(2, '0'),
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }
}

/// شريط التقدم
class _ProgressBar extends StatelessWidget {
  final Duration totalDuration;
  final Duration remainingTime;

  const _ProgressBar({
    required this.totalDuration,
    required this.remainingTime,
  });

  @override
  Widget build(BuildContext context) {
    final progress = 1 - (remainingTime.inMilliseconds / totalDuration.inMilliseconds);
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقدم',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              '${(progress * 100).toStringAsFixed(0)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.smallPadding),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          minHeight: 8,
        ),
      ],
    );
  }
}

/// معلومات الوضع النشط
class _ActiveModeInfo extends StatelessWidget {
  final FocusMode focusMode;

  const _ActiveModeInfo({required this.focusMode});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات الوضع النشط',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: [
              _StatusChip(
                icon: Icons.block,
                label: '${focusMode.blockedApps.length} تطبيق محجوب',
                color: AppTheme.errorColor,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              _StatusChip(
                icon: Icons.notifications_off,
                label: focusMode.blockNotifications ? 'التنبيهات محجوبة' : 'التنبيهات مسموحة',
                color: focusMode.blockNotifications ? AppTheme.warningColor : AppTheme.successColor,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// رقاقة الحالة
class _StatusChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;

  const _StatusChip({
    required this.icon,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// حوار تمديد الجلسة
class _ExtendSessionDialog extends StatefulWidget {
  final Function(int) onExtend;

  const _ExtendSessionDialog({required this.onExtend});

  @override
  State<_ExtendSessionDialog> createState() => _ExtendSessionDialogState();
}

class _ExtendSessionDialogState extends State<_ExtendSessionDialog> {
  int _selectedMinutes = 15;
  final List<int> _options = [5, 10, 15, 25, 30];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تمديد الجلسة'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('كم دقيقة تريد إضافتها للجلسة؟'),
          const SizedBox(height: AppConstants.defaultPadding),
          Wrap(
            spacing: 8,
            children: _options.map((minutes) {
              return ChoiceChip(
                label: Text('${minutes}د'),
                selected: _selectedMinutes == minutes,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedMinutes = minutes;
                    });
                  }
                },
              );
            }).toList(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () => widget.onExtend(_selectedMinutes),
          child: const Text('تمديد'),
        ),
      ],
    );
  }
}
