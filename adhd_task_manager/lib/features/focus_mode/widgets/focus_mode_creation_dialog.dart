import 'package:flutter/material.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/models/focus_mode.dart';

/// حوار إنشاء/تعديل وضع التركيز
class FocusModeCreationDialog extends StatefulWidget {
  final FocusMode? focusMode; // للتعديل
  final Function(FocusMode) onSave;

  const FocusModeCreationDialog({
    super.key,
    this.focusMode,
    required this.onSave,
  });

  @override
  State<FocusModeCreationDialog> createState() => _FocusModeCreationDialogState();
}

class _FocusModeCreationDialogState extends State<FocusModeCreationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _customMessageController = TextEditingController();
  
  FocusModeType _selectedType = FocusModeType.work;
  int? _duration = 25;
  bool _blockNotifications = false;
  bool _allowCalls = true;
  bool _allowMessages = false;
  List<String> _blockedApps = [];
  List<String> _allowedApps = [];

  // قائمة التطبيقات الشائعة للاختيار منها
  final List<AppOption> _commonApps = [
    AppOption('com.facebook.katana', 'Facebook', Icons.facebook),
    AppOption('com.instagram.android', 'Instagram', Icons.camera_alt),
    AppOption('com.twitter.android', 'Twitter', Icons.alternate_email),
    AppOption('com.snapchat.android', 'Snapchat', Icons.camera),
    AppOption('com.google.android.youtube', 'YouTube', Icons.play_circle),
    AppOption('com.netflix.mediaclient', 'Netflix', Icons.movie),
    AppOption('com.whatsapp', 'WhatsApp', Icons.message),
    AppOption('com.telegram.messenger', 'Telegram', Icons.send),
    AppOption('com.king.candycrushsaga', 'Candy Crush', Icons.games),
    AppOption('com.microsoft.office.word', 'Microsoft Word', Icons.description),
    AppOption('com.google.android.apps.docs.editors.docs', 'Google Docs', Icons.edit_document),
    AppOption('com.slack', 'Slack', Icons.work),
    AppOption('com.microsoft.teams', 'Microsoft Teams', Icons.video_call),
  ];

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.focusMode != null) {
      final mode = widget.focusMode!;
      _nameController.text = mode.name;
      _descriptionController.text = mode.description;
      _customMessageController.text = mode.customMessage ?? '';
      _selectedType = mode.type;
      _duration = mode.duration;
      _blockNotifications = mode.blockNotifications;
      _allowCalls = mode.allowCalls;
      _allowMessages = mode.allowMessages;
      _blockedApps = List.from(mode.blockedApps);
      _allowedApps = List.from(mode.allowedApps);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _customMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // العنوان
            Text(
              widget.focusMode == null ? 'إنشاء وضع تركيز جديد' : 'تعديل وضع التركيز',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // المحتوى القابل للتمرير
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // المعلومات الأساسية
                      _BasicInfoSection(),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // إعدادات الوقت
                      _TimeSettingsSection(),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // إعدادات التنبيهات
                      _NotificationSettingsSection(),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // التطبيقات المحجوبة
                      _BlockedAppsSection(),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // التطبيقات المسموحة
                      _AllowedAppsSection(),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // رسالة مخصصة
                      _CustomMessageSection(),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // أزرار الحفظ والإلغاء
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveFocusMode,
                    child: const Text('حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم المعلومات الأساسية
  Widget _BasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        
        // اسم الوضع
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'اسم وضع التركيز',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال اسم الوضع';
            }
            return null;
          },
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        // الوصف
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'الوصف',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال وصف الوضع';
            }
            return null;
          },
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        // نوع الوضع
        DropdownButtonFormField<FocusModeType>(
          value: _selectedType,
          decoration: const InputDecoration(
            labelText: 'نوع الوضع',
            border: OutlineInputBorder(),
          ),
          items: FocusModeType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(type.displayName),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedType = value;
              });
            }
          },
        ),
      ],
    );
  }

  /// قسم إعدادات الوقت
  Widget _TimeSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات الوقت',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        
        Row(
          children: [
            Checkbox(
              value: _duration != null,
              onChanged: (value) {
                setState(() {
                  _duration = value == true ? 25 : null;
                });
              },
            ),
            const Text('تحديد مدة زمنية'),
          ],
        ),
        
        if (_duration != null) ...[
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _duration!.toDouble(),
                  min: 5,
                  max: 180,
                  divisions: 35,
                  label: '${_duration} دقيقة',
                  onChanged: (value) {
                    setState(() {
                      _duration = value.round();
                    });
                  },
                ),
              ),
              Text('${_duration} دقيقة'),
            ],
          ),
        ],
      ],
    );
  }

  /// قسم إعدادات التنبيهات
  Widget _NotificationSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات التنبيهات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        
        SwitchListTile(
          title: const Text('حجب التنبيهات'),
          subtitle: const Text('منع ظهور التنبيهات أثناء وضع التركيز'),
          value: _blockNotifications,
          onChanged: (value) {
            setState(() {
              _blockNotifications = value;
            });
          },
        ),
        
        SwitchListTile(
          title: const Text('السماح بالمكالمات'),
          subtitle: const Text('السماح بالمكالمات الواردة'),
          value: _allowCalls,
          onChanged: (value) {
            setState(() {
              _allowCalls = value;
            });
          },
        ),
        
        SwitchListTile(
          title: const Text('السماح بالرسائل'),
          subtitle: const Text('السماح برسائل SMS والتطبيقات'),
          value: _allowMessages,
          onChanged: (value) {
            setState(() {
              _allowMessages = value;
            });
          },
        ),
      ],
    );
  }

  /// قسم التطبيقات المحجوبة
  Widget _BlockedAppsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التطبيقات المحجوبة (${_blockedApps.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => _showAppSelectionDialog(true),
              child: const Text('إضافة'),
            ),
          ],
        ),
        
        if (_blockedApps.isNotEmpty) ...[
          const SizedBox(height: AppConstants.smallPadding),
          Wrap(
            spacing: 8,
            children: _blockedApps.map((packageName) {
              final app = _commonApps.firstWhere(
                (app) => app.packageName == packageName,
                orElse: () => AppOption(packageName, packageName, Icons.apps),
              );
              
              return Chip(
                label: Text(app.displayName),
                avatar: Icon(app.icon, size: 16),
                deleteIcon: const Icon(Icons.close, size: 16),
                onDeleted: () {
                  setState(() {
                    _blockedApps.remove(packageName);
                  });
                },
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  /// قسم التطبيقات المسموحة
  Widget _AllowedAppsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التطبيقات المسموحة (${_allowedApps.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => _showAppSelectionDialog(false),
              child: const Text('إضافة'),
            ),
          ],
        ),
        
        if (_allowedApps.isNotEmpty) ...[
          const SizedBox(height: AppConstants.smallPadding),
          Wrap(
            spacing: 8,
            children: _allowedApps.map((packageName) {
              final app = _commonApps.firstWhere(
                (app) => app.packageName == packageName,
                orElse: () => AppOption(packageName, packageName, Icons.apps),
              );
              
              return Chip(
                label: Text(app.displayName),
                avatar: Icon(app.icon, size: 16),
                deleteIcon: const Icon(Icons.close, size: 16),
                onDeleted: () {
                  setState(() {
                    _allowedApps.remove(packageName);
                  });
                },
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  /// قسم الرسالة المخصصة
  Widget _CustomMessageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رسالة مخصصة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        
        TextFormField(
          controller: _customMessageController,
          decoration: const InputDecoration(
            labelText: 'رسالة تظهر عند محاولة فتح تطبيق محجوب',
            border: OutlineInputBorder(),
            hintText: 'مثال: أنت في وضع التركيز. ركز على أهدافك!',
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  /// عرض حوار اختيار التطبيقات
  void _showAppSelectionDialog(bool isBlocked) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isBlocked ? 'اختيار التطبيقات المحجوبة' : 'اختيار التطبيقات المسموحة'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _commonApps.length,
            itemBuilder: (context, index) {
              final app = _commonApps[index];
              final isSelected = isBlocked 
                  ? _blockedApps.contains(app.packageName)
                  : _allowedApps.contains(app.packageName);
              
              return CheckboxListTile(
                title: Text(app.displayName),
                secondary: Icon(app.icon),
                value: isSelected,
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      if (isBlocked) {
                        _blockedApps.add(app.packageName);
                        _allowedApps.remove(app.packageName); // إزالة من المسموح
                      } else {
                        _allowedApps.add(app.packageName);
                        _blockedApps.remove(app.packageName); // إزالة من المحجوب
                      }
                    } else {
                      if (isBlocked) {
                        _blockedApps.remove(app.packageName);
                      } else {
                        _allowedApps.remove(app.packageName);
                      }
                    }
                  });
                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// حفظ وضع التركيز
  void _saveFocusMode() {
    if (!_formKey.currentState!.validate()) return;

    final focusMode = widget.focusMode?.copyWith(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      type: _selectedType,
      duration: _duration,
      blockNotifications: _blockNotifications,
      allowCalls: _allowCalls,
      allowMessages: _allowMessages,
      blockedApps: _blockedApps,
      allowedApps: _allowedApps,
      customMessage: _customMessageController.text.trim().isNotEmpty 
          ? _customMessageController.text.trim() 
          : null,
    ) ?? FocusMode.create(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      type: _selectedType,
      duration: _duration,
      blockNotifications: _blockNotifications,
      allowCalls: _allowCalls,
      allowMessages: _allowMessages,
      blockedApps: _blockedApps,
      allowedApps: _allowedApps,
      customMessage: _customMessageController.text.trim().isNotEmpty 
          ? _customMessageController.text.trim() 
          : null,
    );

    widget.onSave(focusMode);
  }
}

/// خيار التطبيق
class AppOption {
  final String packageName;
  final String displayName;
  final IconData icon;

  const AppOption(this.packageName, this.displayName, this.icon);
}
