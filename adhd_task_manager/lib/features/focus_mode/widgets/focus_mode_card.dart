import 'package:flutter/material.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../core/models/focus_mode.dart';

/// بطاقة وضع التركيز
class FocusModeCard extends StatelessWidget {
  final FocusMode focusMode;
  final bool isActive;
  final VoidCallback onActivate;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const FocusModeCard({
    super.key,
    required this.focusMode,
    this.isActive = false,
    required this.onActivate,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isActive ? 4 : 2,
      color: isActive ? AppTheme.primaryColor.withOpacity(0.1) : null,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان والحالة
            Row(
              children: [
                // أيقونة نوع الوضع
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getTypeColor(focusMode.type).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getTypeIcon(focusMode.type),
                    color: _getTypeColor(focusMode.type),
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: AppConstants.smallPadding),
                
                // اسم الوضع
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        focusMode.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isActive ? AppTheme.primaryColor : null,
                        ),
                      ),
                      Text(
                        focusMode.type.displayName,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getTypeColor(focusMode.type),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // شارة الحالة النشطة
                if (isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'نشط',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // الوصف
            Text(
              focusMode.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // معلومات الوضع
            _FocusModeInfo(focusMode: focusMode),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // أزرار التحكم
            Row(
              children: [
                // زر التفعيل/الإلغاء
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: isActive ? null : onActivate,
                    icon: Icon(isActive ? Icons.stop : Icons.play_arrow),
                    label: Text(isActive ? 'نشط' : 'تفعيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isActive 
                          ? Colors.grey 
                          : AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                
                const SizedBox(width: AppConstants.smallPadding),
                
                // زر التعديل
                IconButton(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey[200],
                  ),
                ),
                
                // زر الحذف
                IconButton(
                  onPressed: onDelete,
                  icon: const Icon(Icons.delete),
                  style: IconButton.styleFrom(
                    backgroundColor: AppTheme.errorColor.withOpacity(0.1),
                    foregroundColor: AppTheme.errorColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون نوع الوضع
  Color _getTypeColor(FocusModeType type) {
    switch (type) {
      case FocusModeType.work:
        return Colors.blue;
      case FocusModeType.study:
        return Colors.green;
      case FocusModeType.sleep:
        return Colors.indigo;
      case FocusModeType.meditation:
        return Colors.purple;
      case FocusModeType.exercise:
        return Colors.orange;
      case FocusModeType.custom:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع الوضع
  IconData _getTypeIcon(FocusModeType type) {
    switch (type) {
      case FocusModeType.work:
        return Icons.work;
      case FocusModeType.study:
        return Icons.school;
      case FocusModeType.sleep:
        return Icons.bedtime;
      case FocusModeType.meditation:
        return Icons.self_improvement;
      case FocusModeType.exercise:
        return Icons.fitness_center;
      case FocusModeType.custom:
        return Icons.tune;
    }
  }
}

/// معلومات وضع التركيز
class _FocusModeInfo extends StatelessWidget {
  final FocusMode focusMode;

  const _FocusModeInfo({required this.focusMode});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // المدة
          if (focusMode.duration != null)
            _InfoRow(
              icon: Icons.timer,
              label: 'المدة',
              value: '${focusMode.duration} دقيقة',
            ),
          
          // التطبيقات المحجوبة
          if (focusMode.blockedApps.isNotEmpty)
            _InfoRow(
              icon: Icons.block,
              label: 'تطبيقات محجوبة',
              value: '${focusMode.blockedApps.length} تطبيق',
            ),
          
          // التطبيقات المسموحة
          if (focusMode.allowedApps.isNotEmpty)
            _InfoRow(
              icon: Icons.check_circle,
              label: 'تطبيقات مسموحة',
              value: '${focusMode.allowedApps.length} تطبيق',
            ),
          
          // إعدادات التنبيهات
          Row(
            children: [
              _SettingChip(
                icon: Icons.notifications_off,
                label: 'حجب التنبيهات',
                isEnabled: focusMode.blockNotifications,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              _SettingChip(
                icon: Icons.phone,
                label: 'السماح بالمكالمات',
                isEnabled: focusMode.allowCalls,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// صف معلومات
class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}

/// رقاقة الإعداد
class _SettingChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isEnabled;

  const _SettingChip({
    required this.icon,
    required this.label,
    required this.isEnabled,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: isEnabled 
            ? AppTheme.successColor.withOpacity(0.2)
            : Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: isEnabled 
                ? AppTheme.successColor
                : Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isEnabled 
                  ? AppTheme.successColor
                  : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
