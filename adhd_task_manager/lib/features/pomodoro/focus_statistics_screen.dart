import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/models/focus_session.dart';
import '../../core/providers/pomodoro_providers.dart';

class FocusStatisticsScreen extends ConsumerStatefulWidget {
  const FocusStatisticsScreen({super.key});

  @override
  ConsumerState<FocusStatisticsScreen> createState() => _FocusStatisticsScreenState();
}

class _FocusStatisticsScreenState extends ConsumerState<FocusStatisticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إحصائيات التركيز'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'اليوم'),
            Tab(text: 'الأسبوع'),
            Tab(text: 'العام'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTodayTab(),
          _buildWeeklyTab(),
          _buildOverallTab(),
        ],
      ),
    );
  }

  Widget _buildTodayTab() {
    final todayStatsAsync = ref.watch(todayStatisticsProvider);
    final todaySessionsAsync = ref.watch(todaySessionsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص اليوم
          _buildTodaySummary(todayStatsAsync),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // جلسات اليوم
          _buildTodaySessions(todaySessionsAsync),
        ],
      ),
    );
  }

  Widget _buildWeeklyTab() {
    final weeklyStatsAsync = ref.watch(weeklyStatisticsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          weeklyStatsAsync.when(
            data: (stats) => Column(
              children: [
                _buildWeeklySummary(stats),
                const SizedBox(height: AppConstants.largePadding),
                _buildWeeklyChart(stats),
              ],
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Text('خطأ في تحميل إحصائيات الأسبوع: $error'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverallTab() {
    final overallStatsAsync = ref.watch(focusStatisticsProvider);
    final allSessionsAsync = ref.watch(allFocusSessionsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الإحصائيات العامة
          _buildOverallSummary(overallStatsAsync),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // توزيع أنواع الجلسات
          _buildSessionTypesChart(overallStatsAsync),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // الجلسات الأخيرة
          _buildRecentSessions(allSessionsAsync),
        ],
      ),
    );
  }

  Widget _buildTodaySummary(AsyncValue<DailyFocusStatistics> statsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص اليوم',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            statsAsync.when(
              data: (stats) => Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'جلسات العمل',
                          stats.workSessions.toString(),
                          Icons.work,
                          Colors.red,
                        ),
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      Expanded(
                        child: _buildStatCard(
                          'دقائق التركيز',
                          stats.totalFocusMinutes.toString(),
                          Icons.timer,
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'جلسات الراحة',
                          stats.breakSessions.toString(),
                          Icons.coffee,
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      Expanded(
                        child: _buildStatCard(
                          'نقاط الإنتاجية',
                          '${stats.productivityScore.toInt()}/100',
                          Icons.trending_up,
                          Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  LinearProgressIndicator(
                    value: stats.productivityScore / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      stats.productivityScore >= 80 ? Colors.green :
                      stats.productivityScore >= 60 ? Colors.orange : Colors.red,
                    ),
                  ),
                ],
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTodaySessions(AsyncValue<List<FocusSession>> sessionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'جلسات اليوم',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            sessionsAsync.when(
              data: (sessions) => sessions.isEmpty
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(AppConstants.largePadding),
                        child: Text('لا توجد جلسات اليوم'),
                      ),
                    )
                  : ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: sessions.length,
                      separatorBuilder: (context, index) => const Divider(),
                      itemBuilder: (context, index) {
                        final session = sessions[index];
                        return _buildSessionTile(session);
                      },
                    ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionTile(FocusSession session) {
    final color = _getSessionTypeColor(session.type);
    
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: Icon(
          _getSessionTypeIcon(session.type),
          color: color,
        ),
      ),
      title: Text(session.type.value),
      subtitle: Text(
        '${_formatTime(session.startTime)} - ${session.endTime != null ? _formatTime(session.endTime!) : 'جارية'}',
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${session.actualDuration ?? session.elapsedDuration.inMinutes} دقيقة',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getStatusColor(session.status).withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              session.status.value,
              style: TextStyle(
                fontSize: 10,
                color: _getStatusColor(session.status),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklySummary(WeeklyFocusStatistics stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الأسبوع',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي جلسات العمل',
                    stats.totalWorkSessions.toString(),
                    Icons.work,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي دقائق التركيز',
                    stats.totalFocusMinutes.toString(),
                    Icons.timer,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            _buildStatCard(
              'متوسط الإنتاجية اليومية',
              '${stats.averageDailyProductivity.toInt()}/100',
              Icons.trending_up,
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeeklyChart(WeeklyFocusStatistics stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإنتاجية اليومية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SizedBox(
              height: 200,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: stats.dailyStats.map((dayStats) {
                  final dayName = _getDayName(dayStats.date.weekday);
                  final height = (dayStats.productivityScore / 100 * 150).clamp(10.0, 150.0);
                  
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        dayStats.productivityScore.toInt().toString(),
                        style: const TextStyle(fontSize: 12),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 30,
                        height: height,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        dayName,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallSummary(AsyncValue<FocusStatistics> statsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإحصائيات العامة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            statsAsync.when(
              data: (stats) => Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'إجمالي الجلسات',
                          stats.totalSessions.toString(),
                          Icons.analytics,
                          Colors.purple,
                        ),
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      Expanded(
                        child: _buildStatCard(
                          'الجلسات المكتملة',
                          stats.completedSessions.toString(),
                          Icons.check_circle,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'معدل الإكمال',
                          '${(stats.completionRate * 100).toInt()}%',
                          Icons.percent,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      Expanded(
                        child: _buildStatCard(
                          'متوسط مدة الجلسة',
                          '${stats.averageSessionDuration.toInt()} دقيقة',
                          Icons.timer,
                          Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionTypesChart(AsyncValue<FocusStatistics> statsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع أنواع الجلسات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            statsAsync.when(
              data: (stats) => Column(
                children: FocusSessionType.values.map((type) {
                  final count = stats.sessionsByType[type] ?? 0;
                  final percentage = stats.totalSessions > 0 
                      ? (count / stats.totalSessions * 100).toInt()
                      : 0;
                  
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Icon(
                          _getSessionTypeIcon(type),
                          color: _getSessionTypeColor(type),
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(type.value)),
                        Text('$count ($percentage%)'),
                      ],
                    ),
                  );
                }).toList(),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentSessions(AsyncValue<List<FocusSession>> sessionsAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الجلسات الأخيرة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            sessionsAsync.when(
              data: (sessions) {
                final recentSessions = sessions.take(5).toList();
                return recentSessions.isEmpty
                    ? const Center(child: Text('لا توجد جلسات'))
                    : ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: recentSessions.length,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          return _buildSessionTile(recentSessions[index]);
                        },
                      );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSessionTypeColor(FocusSessionType type) {
    switch (type) {
      case FocusSessionType.work:
        return Colors.red;
      case FocusSessionType.shortBreak:
        return Colors.green;
      case FocusSessionType.longBreak:
        return Colors.blue;
    }
  }

  IconData _getSessionTypeIcon(FocusSessionType type) {
    switch (type) {
      case FocusSessionType.work:
        return Icons.work;
      case FocusSessionType.shortBreak:
        return Icons.coffee;
      case FocusSessionType.longBreak:
        return Icons.hotel;
    }
  }

  Color _getStatusColor(FocusSessionStatus status) {
    switch (status) {
      case FocusSessionStatus.active:
        return Colors.green;
      case FocusSessionStatus.paused:
        return Colors.orange;
      case FocusSessionStatus.completed:
        return Colors.blue;
      case FocusSessionStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String _getDayName(int weekday) {
    const days = ['الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'];
    return days[weekday - 1];
  }
}
