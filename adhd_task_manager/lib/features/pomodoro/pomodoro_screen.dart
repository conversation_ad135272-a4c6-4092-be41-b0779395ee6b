import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/models/focus_session.dart';
import '../../core/providers/pomodoro_providers.dart';

class PomodoroScreen extends ConsumerWidget {
  const PomodoroScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentSessionAsync = ref.watch(currentSessionProvider);
    final timerAsync = ref.watch(timerProvider);
    final settingsAsync = ref.watch(pomodoroSettingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مؤقت التركيز'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              _showSettingsDialog(context, ref);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // المؤقت الدائري
            _buildTimerCircle(context, ref, currentSessionAsync, timerAsync),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // معلومات الجلسة الحالية
            _buildSessionInfo(context, currentSessionAsync),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // أزرار التحكم
            _buildControlButtons(context, ref, currentSessionAsync),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // أزرار بدء جلسات جديدة
            _buildSessionTypeButtons(context, ref, currentSessionAsync),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // إحصائيات اليوم
            _buildTodayStats(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildTimerCircle(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<FocusSession?> sessionAsync,
    AsyncValue<Duration> timerAsync,
  ) {
    return Container(
      width: 280,
      height: 280,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الدائرة الخارجية
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[100],
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
          ),
          
          // مؤشر التقدم
          sessionAsync.when(
            data: (session) => timerAsync.when(
              data: (remaining) => session != null
                  ? CustomPaint(
                      size: const Size(260, 260),
                      painter: TimerProgressPainter(
                        progress: 1.0 - session.completionPercentage,
                        color: _getSessionColor(session.type),
                      ),
                    )
                  : const SizedBox(),
              loading: () => const CircularProgressIndicator(),
              error: (_, __) => const SizedBox(),
            ),
            loading: () => const CircularProgressIndicator(),
            error: (_, __) => const SizedBox(),
          ),
          
          // النص المركزي
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              timerAsync.when(
                data: (remaining) => Text(
                  _formatDuration(remaining),
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 48,
                    color: Colors.black87,
                  ),
                ),
                loading: () => const Text('--:--'),
                error: (_, __) => const Text('--:--'),
              ),
              const SizedBox(height: 8),
              sessionAsync.when(
                data: (session) => Text(
                  session?.type.value ?? 'جاهز للبدء',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: session != null ? _getSessionColor(session.type) : Colors.grey,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                loading: () => const Text('جاري التحميل...'),
                error: (_, __) => const Text('خطأ'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSessionInfo(BuildContext context, AsyncValue<FocusSession?> sessionAsync) {
    return sessionAsync.when(
      data: (session) => session != null
          ? Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'الحالة:',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getStatusColor(session.status).withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            session.status.value,
                            style: TextStyle(
                              color: _getStatusColor(session.status),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'المدة المخططة:',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Text(
                          '${session.plannedDuration} دقيقة',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'الوقت المنقضي:',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Text(
                          '${session.elapsedDuration.inMinutes} دقيقة',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            )
          : const SizedBox(),
      loading: () => const Card(
        child: Padding(
          padding: EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (_, __) => const SizedBox(),
    );
  }

  Widget _buildControlButtons(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<FocusSession?> sessionAsync,
  ) {
    return sessionAsync.when(
      data: (session) => session != null
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر الإيقاف/الاستئناف
                ElevatedButton.icon(
                  onPressed: () async {
                    final service = ref.read(pomodoroServiceProvider);
                    if (session.status.isActive) {
                      await service.pauseSession();
                    } else if (session.status.isPaused) {
                      await service.resumeSession();
                    }
                  },
                  icon: Icon(session.status.isActive ? Icons.pause : Icons.play_arrow),
                  label: Text(session.status.isActive ? 'إيقاف' : 'استئناف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: session.status.isActive ? Colors.orange : Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                // زر الإلغاء
                ElevatedButton.icon(
                  onPressed: () async {
                    final confirmed = await _showCancelConfirmation(context);
                    if (confirmed) {
                      final service = ref.read(pomodoroServiceProvider);
                      await service.cancelSession();
                    }
                  },
                  icon: const Icon(Icons.stop),
                  label: const Text('إلغاء'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            )
          : const SizedBox(),
      loading: () => const SizedBox(),
      error: (_, __) => const SizedBox(),
    );
  }

  Widget _buildSessionTypeButtons(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<FocusSession?> sessionAsync,
  ) {
    return sessionAsync.when(
      data: (session) => session == null
          ? Column(
              children: [
                Text(
                  'بدء جلسة جديدة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Row(
                  children: [
                    Expanded(
                      child: _buildSessionTypeButton(
                        context,
                        ref,
                        FocusSessionType.work,
                        Icons.work,
                        Colors.red,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: _buildSessionTypeButton(
                        context,
                        ref,
                        FocusSessionType.shortBreak,
                        Icons.coffee,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: _buildSessionTypeButton(
                        context,
                        ref,
                        FocusSessionType.longBreak,
                        Icons.hotel,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
              ],
            )
          : const SizedBox(),
      loading: () => const SizedBox(),
      error: (_, __) => const SizedBox(),
    );
  }

  Widget _buildSessionTypeButton(
    BuildContext context,
    WidgetRef ref,
    FocusSessionType type,
    IconData icon,
    Color color,
  ) {
    return ElevatedButton(
      onPressed: () async {
        final service = ref.read(pomodoroServiceProvider);
        await service.startSession(type: type);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32),
          const SizedBox(height: 4),
          Text(
            type.value,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayStats(BuildContext context, WidgetRef ref) {
    final todayStatsAsync = ref.watch(todayStatisticsProvider);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات اليوم',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            todayStatsAsync.when(
              data: (stats) => Column(
                children: [
                  _buildStatRow('جلسات العمل', stats.workSessions.toString(), Icons.work),
                  _buildStatRow('دقائق التركيز', stats.totalFocusMinutes.toString(), Icons.timer),
                  _buildStatRow('الجلسات المكتملة', stats.completedSessions.toString(), Icons.check_circle),
                  _buildStatRow('نقاط الإنتاجية', '${stats.productivityScore.toInt()}/100', Icons.trending_up),
                ],
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (_, __) => const Text('خطأ في تحميل الإحصائيات'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Color _getSessionColor(FocusSessionType type) {
    switch (type) {
      case FocusSessionType.work:
        return Colors.red;
      case FocusSessionType.shortBreak:
        return Colors.green;
      case FocusSessionType.longBreak:
        return Colors.blue;
    }
  }

  Color _getStatusColor(FocusSessionStatus status) {
    switch (status) {
      case FocusSessionStatus.active:
        return Colors.green;
      case FocusSessionStatus.paused:
        return Colors.orange;
      case FocusSessionStatus.completed:
        return Colors.blue;
      case FocusSessionStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Future<bool> _showCancelConfirmation(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الإلغاء'),
        content: const Text('هل تريد إلغاء الجلسة الحالية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showSettingsDialog(BuildContext context, WidgetRef ref) {
    // TODO: تنفيذ نافذة الإعدادات
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات Pomodoro'),
        content: const Text('سيتم تطوير إعدادات Pomodoro قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

// رسام مؤشر التقدم الدائري
class TimerProgressPainter extends CustomPainter {
  final double progress;
  final Color color;

  TimerProgressPainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;

    // رسم الدائرة الخلفية
    final backgroundPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 8
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // رسم مؤشر التقدم
    final progressPaint = Paint()
      ..color = color
      ..strokeWidth = 8
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final sweepAngle = 2 * math.pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // البدء من الأعلى
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
