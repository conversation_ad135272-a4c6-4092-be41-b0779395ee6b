import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';

class TasksScreen extends StatelessWidget {
  const TasksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.tasksTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // TODO: تنفيذ فلترة المهام
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // شريط البحث
            TextField(
              decoration: InputDecoration(
                hintText: 'البحث في المهام...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // قائمة المهام
            Expanded(
              child: _buildEmptyState(context),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddTaskDialog(context);
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.task_alt,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            AppConstants.noTasksMessage,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            AppConstants.addFirstTaskMessage,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () {
              _showAddTaskDialog(context);
            },
            icon: const Icon(Icons.add),
            label: const Text(AppConstants.addTask),
          ),
        ],
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(AppConstants.addTask),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: AppConstants.taskTitle,
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              TextField(
                decoration: const InputDecoration(
                  labelText: AppConstants.taskDescription,
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: AppConstants.priority,
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'urgent',
                    child: Text(AppConstants.urgentPriority),
                  ),
                  DropdownMenuItem(
                    value: 'important',
                    child: Text(AppConstants.importantPriority),
                  ),
                  DropdownMenuItem(
                    value: 'normal',
                    child: Text(AppConstants.normalPriority),
                  ),
                ],
                onChanged: (value) {
                  // TODO: حفظ الأولوية
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                // TODO: حفظ المهمة
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إضافة المهمة بنجاح'),
                  ),
                );
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }
}
