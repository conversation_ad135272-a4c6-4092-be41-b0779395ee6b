import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/models/task.dart';
import '../../core/models/reminder.dart';
import '../../core/providers/task_providers.dart';
import '../../core/providers/reminder_providers.dart';

class TasksScreen extends ConsumerStatefulWidget {
  const TasksScreen({super.key});

  @override
  ConsumerState<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends ConsumerState<TasksScreen> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tasksAsync = ref.watch(advancedFilteredTasksProvider);
    final searchQuery = ref.watch(searchQueryProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.tasksTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: () {
              _showSortDialog(context);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // شريط البحث
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في المهام...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          ref.read(searchQueryProvider.notifier).state = '';
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: (value) {
                ref.read(searchQueryProvider.notifier).state = value;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // شريط الفلترة والإحصائيات
            _buildFilterAndStatsBar(),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة المهام
            Expanded(
              child: tasksAsync.when(
                data: (tasks) {
                  if (tasks.isEmpty) {
                    return _buildEmptyState(context);
                  }
                  return _buildTasksList(tasks);
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red[400],
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        'حدث خطأ في تحميل المهام',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        error.toString(),
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      ElevatedButton(
                        onPressed: () {
                          ref.read(tasksNotifierProvider.notifier).loadTasks();
                        },
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddTaskDialog(context);
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildFilterAndStatsBar() {
    final statisticsAsync = ref.watch(taskStatisticsProvider);
    final selectedFilter = ref.watch(selectedPriorityFilterProvider);

    return statisticsAsync.when(
      data: (statistics) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // إحصائيات سريعة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('الإجمالي', statistics.totalTasks, AppTheme.primaryColor),
                _buildStatItem('مكتملة', statistics.completedTasks, AppTheme.successColor),
                _buildStatItem('معلقة', statistics.pendingTasks, AppTheme.warningColor),
                _buildStatItem('عاجلة', statistics.urgentTasks, AppTheme.urgentColor),
              ],
            ),

            const SizedBox(height: 12),

            // شريط الفلترة
            Column(
              children: [
                // فلاتر الأولوية
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'الكل',
                        selectedFilter == null,
                        () => ref.read(selectedPriorityFilterProvider.notifier).state = null,
                        AppTheme.primaryColor,
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'عاجل',
                        selectedFilter == TaskPriority.urgent,
                        () => ref.read(selectedPriorityFilterProvider.notifier).state = TaskPriority.urgent,
                        AppTheme.urgentColor,
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'مهم',
                        selectedFilter == TaskPriority.important,
                        () => ref.read(selectedPriorityFilterProvider.notifier).state = TaskPriority.important,
                        AppTheme.importantColor,
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'عادي',
                        selectedFilter == TaskPriority.normal,
                        () => ref.read(selectedPriorityFilterProvider.notifier).state = TaskPriority.normal,
                        AppTheme.normalColor,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                // فلاتر حالة الإكمال
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildCompletionFilterChip(),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      loading: () => const SizedBox(height: 80, child: Center(child: CircularProgressIndicator())),
      error: (error, stack) => const SizedBox(),
    );
  }

  Widget _buildStatItem(String label, int value, Color color) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap, Color color) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildCompletionFilterChip() {
    final completionFilter = ref.watch(taskCompletionFilterProvider);

    return Row(
      children: [
        _buildFilterChip(
          'الكل',
          completionFilter == null,
          () => ref.read(taskCompletionFilterProvider.notifier).state = null,
          AppTheme.primaryColor,
        ),
        const SizedBox(width: 8),
        _buildFilterChip(
          'مكتملة',
          completionFilter == TaskCompletionFilter.completed,
          () => ref.read(taskCompletionFilterProvider.notifier).state = TaskCompletionFilter.completed,
          AppTheme.successColor,
        ),
        const SizedBox(width: 8),
        _buildFilterChip(
          'معلقة',
          completionFilter == TaskCompletionFilter.pending,
          () => ref.read(taskCompletionFilterProvider.notifier).state = TaskCompletionFilter.pending,
          AppTheme.warningColor,
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.task_alt,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            AppConstants.noTasksMessage,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            AppConstants.addFirstTaskMessage,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () {
              _showAddTaskDialog(context);
            },
            icon: const Icon(Icons.add),
            label: const Text(AppConstants.addTask),
          ),
        ],
      ),
    );
  }

  Widget _buildTasksList(List<Task> tasks) {
    return ListView.builder(
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return _buildTaskCard(task);
      },
    );
  }

  Widget _buildTaskCard(Task task) {
    final priorityColor = _getPriorityColor(task.priority);

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      elevation: task.isCompleted ? 1 : 3,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: priorityColor.withOpacity(0.3),
            width: 2,
          ),
          gradient: task.isCompleted
            ? null
            : LinearGradient(
                colors: [
                  priorityColor.withOpacity(0.05),
                  Colors.white,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          leading: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: priorityColor.withOpacity(0.1),
              border: Border.all(color: priorityColor, width: 2),
            ),
            child: Checkbox(
              value: task.isCompleted,
              activeColor: priorityColor,
              checkColor: Colors.white,
              onChanged: (value) async {
                try {
                  await ref.read(tasksNotifierProvider.notifier).toggleTaskCompletion(task);

                  // إضافة تأثير بصري عند الإكمال
                  if (mounted && value == true) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            Icon(Icons.check_circle, color: Colors.white),
                            const SizedBox(width: 8),
                            Text('تم إكمال المهمة: ${task.title}'),
                          ],
                        ),
                        backgroundColor: AppTheme.successColor,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في تحديث المهمة: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
            ),
          ),
          title: Text(
            task.title,
            style: TextStyle(
              decoration: task.isCompleted ? TextDecoration.lineThrough : null,
              color: task.isCompleted ? Colors.grey : Colors.black87,
              fontWeight: task.isCompleted ? FontWeight.normal : FontWeight.w600,
              fontSize: 16,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (task.description.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  task.description,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: task.isCompleted ? Colors.grey : Colors.black54,
                    decoration: task.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildEnhancedPriorityChip(task.priority),
                  if (task.dueDate != null) ...[
                    const SizedBox(width: 8),
                    _buildDueDateChip(task.dueDate!),
                  ],
                  const Spacer(),
                  if (task.isCompleted)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppTheme.successColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 14,
                            color: AppTheme.successColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'مكتملة',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.successColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
          trailing: PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: task.isCompleted ? Colors.grey : priorityColor,
            ),
            onSelected: (value) async {
              switch (value) {
                case 'edit':
                  _showEditTaskDialog(context, task);
                  break;
                case 'delete':
                  _showDeleteConfirmation(context, task);
                  break;
                case 'duplicate':
                  _duplicateTask(task);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, color: Colors.green),
                    SizedBox(width: 8),
                    Text('نسخ'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.urgent:
        return AppTheme.urgentColor;
      case TaskPriority.important:
        return AppTheme.importantColor;
      case TaskPriority.normal:
        return AppTheme.normalColor;
    }
  }

  Widget _buildPriorityChip(TaskPriority priority) {
    Color color = _getPriorityColor(priority);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        priority.value,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEnhancedPriorityChip(TaskPriority priority) {
    Color color = _getPriorityColor(priority);
    IconData icon;

    switch (priority) {
      case TaskPriority.urgent:
        icon = Icons.priority_high;
        break;
      case TaskPriority.important:
        icon = Icons.star;
        break;
      case TaskPriority.normal:
        icon = Icons.circle;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color, width: 1.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            priority.value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDueDateChip(DateTime dueDate) {
    final color = _getDueDateColor(dueDate);
    final text = _formatDate(dueDate);
    IconData icon;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final due = DateTime(dueDate.year, dueDate.month, dueDate.day);

    if (due.isBefore(today)) {
      icon = Icons.warning;
    } else if (due.isAtSameMomentAs(today)) {
      icon = Icons.today;
    } else {
      icon = Icons.schedule;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getDueDateColor(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final due = DateTime(dueDate.year, dueDate.month, dueDate.day);

    if (due.isBefore(today)) {
      return Colors.red; // متأخر
    } else if (due.isAtSameMomentAs(today)) {
      return Colors.orange; // اليوم
    } else {
      return Colors.grey; // في المستقبل
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly.isAtSameMomentAs(today)) {
      return 'اليوم';
    } else if (dateOnly.isAtSameMomentAs(today.add(const Duration(days: 1)))) {
      return 'غداً';
    } else if (dateOnly.isBefore(today)) {
      final difference = today.difference(dateOnly).inDays;
      return 'متأخر $difference يوم';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showAddTaskDialog(BuildContext context) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    TaskPriority selectedPriority = TaskPriority.normal;
    DateTime? selectedDueDate;

    // متغيرات التذكير
    bool addReminder = false;
    DateTime? reminderDateTime;
    ReminderType reminderType = ReminderType.notification;
    ReminderFrequency reminderFrequency = ReminderFrequency.once;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text(AppConstants.addTask),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: titleController,
                      decoration: const InputDecoration(
                        labelText: AppConstants.taskTitle,
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    TextField(
                      controller: descriptionController,
                      decoration: const InputDecoration(
                        labelText: AppConstants.taskDescription,
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    DropdownButtonFormField<TaskPriority>(
                      value: selectedPriority,
                      decoration: const InputDecoration(
                        labelText: AppConstants.priority,
                        border: OutlineInputBorder(),
                      ),
                      items: TaskPriority.values.map((priority) {
                        return DropdownMenuItem(
                          value: priority,
                          child: Text(priority.value),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedPriority = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    ListTile(
                      title: const Text('تاريخ الاستحقاق'),
                      subtitle: Text(
                        selectedDueDate != null
                            ? '${selectedDueDate!.day}/${selectedDueDate!.month}/${selectedDueDate!.year}'
                            : 'لا يوجد تاريخ محدد',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(const Duration(days: 365)),
                        );
                        if (date != null) {
                          setState(() {
                            selectedDueDate = date;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),

                    // قسم التذكير
                    ExpansionTile(
                      title: const Text('إضافة تذكير'),
                      leading: const Icon(Icons.notifications),
                      children: [
                        CheckboxListTile(
                          title: const Text('تفعيل التذكير'),
                          value: addReminder,
                          onChanged: (value) {
                            setState(() {
                              addReminder = value ?? false;
                            });
                          },
                        ),
                        if (addReminder) ...[
                          ListTile(
                            title: const Text('وقت التذكير'),
                            subtitle: Text(
                              reminderDateTime != null
                                  ? '${reminderDateTime!.day}/${reminderDateTime!.month}/${reminderDateTime!.year} ${reminderDateTime!.hour}:${reminderDateTime!.minute.toString().padLeft(2, '0')}'
                                  : 'لم يتم تحديد الوقت',
                            ),
                            trailing: const Icon(Icons.access_time),
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: DateTime.now().add(const Duration(hours: 1)),
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(const Duration(days: 365)),
                              );
                              if (date != null) {
                                final time = await showTimePicker(
                                  context: context,
                                  initialTime: TimeOfDay.now(),
                                );
                                if (time != null) {
                                  setState(() {
                                    reminderDateTime = DateTime(
                                      date.year,
                                      date.month,
                                      date.day,
                                      time.hour,
                                      time.minute,
                                    );
                                  });
                                }
                              }
                            },
                          ),
                          DropdownButtonFormField<ReminderType>(
                            value: reminderType,
                            decoration: const InputDecoration(
                              labelText: 'نوع التذكير',
                              border: OutlineInputBorder(),
                            ),
                            items: ReminderType.values.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Text(type.value),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  reminderType = value;
                                });
                              }
                            },
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          DropdownButtonFormField<ReminderFrequency>(
                            value: reminderFrequency,
                            decoration: const InputDecoration(
                              labelText: 'تكرار التذكير',
                              border: OutlineInputBorder(),
                            ),
                            items: ReminderFrequency.values.map((frequency) {
                              return DropdownMenuItem(
                                value: frequency,
                                child: Text(frequency.value),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  reminderFrequency = value;
                                });
                              }
                            },
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (titleController.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى إدخال عنوان المهمة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    try {
                      // إضافة المهمة أولاً
                      await ref.read(tasksNotifierProvider.notifier).addTask(
                        title: titleController.text.trim(),
                        description: descriptionController.text.trim(),
                        priority: selectedPriority,
                        dueDate: selectedDueDate,
                      );

                      // إضافة التذكير إذا كان مفعلاً
                      if (addReminder && reminderDateTime != null) {
                        // سنحتاج لتحديث TasksNotifier لإرجاع المهمة المضافة
                        // للآن، سنعرض رسالة أن التذكير سيتم إضافته لاحقاً
                        print('سيتم إضافة التذكير للمهمة لاحقاً');
                      }

                      if (context.mounted) {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(addReminder && reminderDateTime != null
                                ? 'تم إضافة المهمة والتذكير بنجاح'
                                : 'تم إضافة المهمة بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('خطأ في إضافة المهمة: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  child: const Text('حفظ'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showEditTaskDialog(BuildContext context, Task task) {
    final titleController = TextEditingController(text: task.title);
    final descriptionController = TextEditingController(text: task.description);
    TaskPriority selectedPriority = task.priority;
    DateTime? selectedDueDate = task.dueDate;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تعديل المهمة'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: titleController,
                      decoration: const InputDecoration(
                        labelText: AppConstants.taskTitle,
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    TextField(
                      controller: descriptionController,
                      decoration: const InputDecoration(
                        labelText: AppConstants.taskDescription,
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    DropdownButtonFormField<TaskPriority>(
                      value: selectedPriority,
                      decoration: const InputDecoration(
                        labelText: AppConstants.priority,
                        border: OutlineInputBorder(),
                      ),
                      items: TaskPriority.values.map((priority) {
                        return DropdownMenuItem(
                          value: priority,
                          child: Text(priority.value),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedPriority = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    ListTile(
                      title: const Text('تاريخ الاستحقاق'),
                      subtitle: Text(
                        selectedDueDate != null
                            ? '${selectedDueDate!.day}/${selectedDueDate!.month}/${selectedDueDate!.year}'
                            : 'لا يوجد تاريخ محدد',
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (selectedDueDate != null)
                            IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  selectedDueDate = null;
                                });
                              },
                            ),
                          const Icon(Icons.calendar_today),
                        ],
                      ),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: selectedDueDate ?? DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(const Duration(days: 365)),
                        );
                        if (date != null) {
                          setState(() {
                            selectedDueDate = date;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (titleController.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى إدخال عنوان المهمة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    try {
                      final updatedTask = task.update(
                        title: titleController.text.trim(),
                        description: descriptionController.text.trim(),
                        priority: selectedPriority,
                        dueDate: selectedDueDate,
                      );

                      await ref.read(tasksNotifierProvider.notifier).updateTask(updatedTask);

                      if (context.mounted) {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم تحديث المهمة بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('خطأ في تحديث المهمة: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  child: const Text('حفظ'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من رغبتك في حذف المهمة "${task.title}"؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await ref.read(tasksNotifierProvider.notifier).deleteTask(task.id!);

                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم حذف المهمة بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في حذف المهمة: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('فلترة المهام'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('جميع المهام'),
                onTap: () {
                  ref.read(selectedPriorityFilterProvider.notifier).state = null;
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: const Text('المهام العاجلة'),
                onTap: () {
                  ref.read(selectedPriorityFilterProvider.notifier).state = TaskPriority.urgent;
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: const Text('المهام المهمة'),
                onTap: () {
                  ref.read(selectedPriorityFilterProvider.notifier).state = TaskPriority.important;
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: const Text('المهام العادية'),
                onTap: () {
                  ref.read(selectedPriorityFilterProvider.notifier).state = TaskPriority.normal;
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showSortDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('ترتيب المهام'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('تاريخ الإنشاء'),
                onTap: () {
                  ref.read(taskSortModeProvider.notifier).state = TaskSortMode.createdDate;
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: const Text('الأولوية'),
                onTap: () {
                  ref.read(taskSortModeProvider.notifier).state = TaskSortMode.priority;
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: const Text('تاريخ الاستحقاق'),
                onTap: () {
                  ref.read(taskSortModeProvider.notifier).state = TaskSortMode.dueDate;
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: const Text('الترتيب الأبجدي'),
                onTap: () {
                  ref.read(taskSortModeProvider.notifier).state = TaskSortMode.alphabetical;
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _duplicateTask(Task task) async {
    try {
      await ref.read(tasksNotifierProvider.notifier).addTask(
        title: '${task.title} (نسخة)',
        description: task.description,
        priority: task.priority,
        dueDate: task.dueDate,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ المهمة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في نسخ المهمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
