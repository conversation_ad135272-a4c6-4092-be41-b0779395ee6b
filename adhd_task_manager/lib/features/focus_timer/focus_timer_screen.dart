import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';

// Providers لحالة المؤقت البسيط
final simpleTimerRunningProvider = StateProvider<bool>((ref) => false);
final simpleTimerPausedProvider = StateProvider<bool>((ref) => false);
final simpleTimerMinutesProvider = StateProvider<int>(
  (ref) => AppConstants.defaultWorkDuration,
);
final simpleTimerSecondsProvider = StateProvider<int>((ref) => 0);
final simpleTimerSessionProvider = StateProvider<String>((ref) => 'عمل');

class FocusTimerScreen extends ConsumerWidget {
  const FocusTimerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isRunning = ref.watch(simpleTimerRunningProvider);
    final minutes = ref.watch(simpleTimerMinutesProvider);
    final seconds = ref.watch(simpleTimerSecondsProvider);
    final currentSession = ref.watch(simpleTimerSessionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.focusTimerTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              _showTimerSettings(context, ref);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // نوع الجلسة الحالية
            Text(
              'جلسة $currentSession',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.largePadding),

            // العداد الدائري
            Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppTheme.primaryColor, width: 8),
              ),
              child: Center(
                child: Text(
                  '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),

            const SizedBox(height: AppConstants.largePadding),

            // أزرار التحكم
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر البدء/الإيقاف المؤقت
                ElevatedButton.icon(
                  onPressed: () => _toggleTimer(ref),
                  icon: Icon(isRunning ? Icons.pause : Icons.play_arrow),
                  label: Text(isRunning ? 'إيقاف مؤقت' : 'بدء'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isRunning
                            ? AppTheme.warningColor
                            : AppTheme.primaryColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),

                // زر إعادة التعيين
                ElevatedButton.icon(
                  onPressed: () => _resetTimer(ref),
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة تعيين'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.errorColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.largePadding),

            // إحصائيات سريعة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  children: [
                    Text(
                      'إحصائيات اليوم',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(context, 'جلسات مكتملة', '0'),
                        _buildStatItem(context, 'وقت التركيز', '0 د'),
                        _buildStatItem(context, 'فترات الراحة', '0'),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  void _toggleTimer(WidgetRef ref) {
    final isRunning = ref.read(simpleTimerRunningProvider);
    ref.read(simpleTimerRunningProvider.notifier).state = !isRunning;
    ref.read(simpleTimerPausedProvider.notifier).state = isRunning;

    // TODO: تنفيذ منطق المؤقت الفعلي
    if (!isRunning) {
      ScaffoldMessenger.of(
        ref.context,
      ).showSnackBar(const SnackBar(content: Text('بدأ المؤقت')));
    } else {
      ScaffoldMessenger.of(
        ref.context,
      ).showSnackBar(const SnackBar(content: Text('تم إيقاف المؤقت مؤقتاً')));
    }
  }

  void _resetTimer(WidgetRef ref) {
    ref.read(simpleTimerRunningProvider.notifier).state = false;
    ref.read(simpleTimerPausedProvider.notifier).state = false;
    ref.read(simpleTimerMinutesProvider.notifier).state =
        AppConstants.defaultWorkDuration;
    ref.read(simpleTimerSecondsProvider.notifier).state = 0;

    ScaffoldMessenger.of(
      ref.context,
    ).showSnackBar(const SnackBar(content: Text('تم إعادة تعيين المؤقت')));
  }

  void _showTimerSettings(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إعدادات المؤقت'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('مدة العمل'),
                subtitle: Text('${AppConstants.defaultWorkDuration} دقيقة'),
                trailing: const Icon(Icons.edit),
              ),
              ListTile(
                title: const Text('مدة الراحة'),
                subtitle: Text('${AppConstants.defaultBreakDuration} دقائق'),
                trailing: const Icon(Icons.edit),
              ),
              ListTile(
                title: const Text('الراحة الطويلة'),
                subtitle: Text(
                  '${AppConstants.defaultLongBreakDuration} دقيقة',
                ),
                trailing: const Icon(Icons.edit),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }
}
