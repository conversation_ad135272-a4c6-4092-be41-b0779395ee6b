import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import 'notification_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _darkModeEnabled = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.settingsTitle),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // قسم التنبيهات
          _buildSectionHeader(context, 'التنبيهات والإشعارات'),
          Card(
            child: Column(
              children: [
                // تفعيل التنبيهات
                SwitchListTile(
                  title: const Text('تفعيل التنبيهات'),
                  subtitle: const Text('تلقي إشعارات للمهام والتذكيرات'),
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                ),
                SwitchListTile(
                  title: const Text('الأصوات'),
                  subtitle: const Text('تشغيل أصوات التنبيهات'),
                  value: _soundEnabled,
                  onChanged: _notificationsEnabled ? (value) {
                    setState(() {
                      _soundEnabled = value;
                    });
                  } : null,
                ),
                SwitchListTile(
                  title: const Text('الاهتزاز'),
                  subtitle: const Text('تفعيل الاهتزاز مع التنبيهات'),
                  value: _vibrationEnabled,
                  onChanged: _notificationsEnabled ? (value) {
                    setState(() {
                      _vibrationEnabled = value;
                    });
                  } : null,
                ),
                const Divider(),
                ListTile(
                  title: const Text('إعدادات التنبيهات المتقدمة'),
                  subtitle: const Text('إدارة التذكيرات والإحصائيات'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const NotificationSettingsScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // قسم المظهر
          _buildSectionHeader(context, 'المظهر'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('الوضع الداكن'),
                  subtitle: const Text('تفعيل المظهر الداكن'),
                  value: _darkModeEnabled,
                  onChanged: (value) {
                    setState(() {
                      _darkModeEnabled = value;
                    });
                    // TODO: تطبيق تغيير المظهر
                  },
                ),
                ListTile(
                  title: const Text('حجم الخط'),
                  subtitle: const Text('متوسط'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: فتح إعدادات حجم الخط
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // قسم المؤقت
          _buildSectionHeader(context, 'إعدادات المؤقت'),
          Card(
            child: Column(
              children: [
                ListTile(
                  title: const Text('مدة جلسة العمل'),
                  subtitle: Text('${AppConstants.defaultWorkDuration} دقيقة'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: فتح إعدادات مدة العمل
                  },
                ),
                ListTile(
                  title: const Text('مدة الراحة القصيرة'),
                  subtitle: Text('${AppConstants.defaultBreakDuration} دقائق'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: فتح إعدادات مدة الراحة
                  },
                ),
                ListTile(
                  title: const Text('مدة الراحة الطويلة'),
                  subtitle: Text('${AppConstants.defaultLongBreakDuration} دقيقة'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: فتح إعدادات الراحة الطويلة
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // قسم البيانات
          _buildSectionHeader(context, 'البيانات'),
          Card(
            child: Column(
              children: [
                ListTile(
                  title: const Text('نسخ احتياطي للبيانات'),
                  subtitle: const Text('حفظ بياناتك في السحابة'),
                  trailing: const Icon(Icons.cloud_upload),
                  onTap: () {
                    // TODO: تنفيذ النسخ الاحتياطي
                  },
                ),
                ListTile(
                  title: const Text('استيراد البيانات'),
                  subtitle: const Text('استعادة البيانات من نسخة احتياطية'),
                  trailing: const Icon(Icons.cloud_download),
                  onTap: () {
                    // TODO: تنفيذ استيراد البيانات
                  },
                ),
                ListTile(
                  title: const Text('مسح جميع البيانات'),
                  subtitle: const Text('حذف جميع المهام والعادات'),
                  trailing: const Icon(Icons.delete_forever),
                  onTap: () {
                    _showDeleteAllDataDialog(context);
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // قسم حول التطبيق
          _buildSectionHeader(context, 'حول التطبيق'),
          Card(
            child: Column(
              children: [
                ListTile(
                  title: const Text('الإصدار'),
                  subtitle: const Text('1.0.0'),
                  trailing: const Icon(Icons.info),
                ),
                ListTile(
                  title: const Text('تقييم التطبيق'),
                  subtitle: const Text('ساعدنا بتقييمك'),
                  trailing: const Icon(Icons.star),
                  onTap: () {
                    // TODO: فتح صفحة التقييم
                  },
                ),
                ListTile(
                  title: const Text('تواصل معنا'),
                  subtitle: const Text('أرسل ملاحظاتك واقتراحاتك'),
                  trailing: const Icon(Icons.email),
                  onTap: () {
                    // TODO: فتح نموذج التواصل
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showDeleteAllDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تحذير'),
          content: const Text(
            'هل أنت متأكد من رغبتك في حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                // TODO: تنفيذ حذف البيانات
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف جميع البيانات'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorColor,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
