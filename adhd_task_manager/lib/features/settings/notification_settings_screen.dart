import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_constants.dart';
import '../../core/providers/reminder_providers.dart';
import '../../core/services/notification_service.dart';

class NotificationSettingsScreen extends ConsumerWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationSettings = ref.watch(notificationSettingsProvider);
    final notificationPermission = ref.watch(notificationPermissionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات التنبيهات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // حالة الأذونات
            _buildPermissionSection(context, ref, notificationPermission),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // إعدادات التنبيهات
            _buildNotificationSettings(context, ref, notificationSettings),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // إحصائيات التذكيرات
            _buildReminderStatistics(context, ref),
            
            const SizedBox(height: AppConstants.largePadding),
            
            // أزرار الإجراءات
            _buildActionButtons(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionSection(BuildContext context, WidgetRef ref, AsyncValue<bool> permissionAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.security,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'حالة الأذونات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            permissionAsync.when(
              data: (hasPermission) => Row(
                children: [
                  Icon(
                    hasPermission ? Icons.check_circle : Icons.error,
                    color: hasPermission ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    hasPermission 
                        ? 'تم منح أذونات التنبيهات'
                        : 'لم يتم منح أذونات التنبيهات',
                    style: TextStyle(
                      color: hasPermission ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              loading: () => const Row(
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('جاري التحقق من الأذونات...'),
                ],
              ),
              error: (error, stack) => Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(
                    'خطأ في التحقق من الأذونات',
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
            if (permissionAsync.value == false) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              ElevatedButton.icon(
                onPressed: () async {
                  await NotificationService.instance.initialize();
                  ref.invalidate(notificationPermissionProvider);
                },
                icon: const Icon(Icons.settings),
                label: const Text('طلب الأذونات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings(BuildContext context, WidgetRef ref, NotificationSettings settings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'إعدادات التنبيهات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // تفعيل التنبيهات
            SwitchListTile(
              title: const Text('تفعيل التنبيهات'),
              subtitle: const Text('تمكين أو تعطيل جميع التنبيهات'),
              value: settings.notificationsEnabled,
              onChanged: (value) {
                ref.read(notificationSettingsProvider.notifier).updateNotificationsEnabled(value);
              },
              secondary: const Icon(Icons.notifications),
            ),
            
            const Divider(),
            
            // تفعيل الأصوات
            SwitchListTile(
              title: const Text('الأصوات'),
              subtitle: const Text('تشغيل الأصوات مع التنبيهات'),
              value: settings.soundEnabled,
              onChanged: settings.notificationsEnabled ? (value) {
                ref.read(notificationSettingsProvider.notifier).updateSoundEnabled(value);
              } : null,
              secondary: const Icon(Icons.volume_up),
            ),
            
            const Divider(),
            
            // تفعيل الاهتزاز
            SwitchListTile(
              title: const Text('الاهتزاز'),
              subtitle: const Text('تفعيل الاهتزاز مع التنبيهات'),
              value: settings.vibrationEnabled,
              onChanged: settings.notificationsEnabled ? (value) {
                ref.read(notificationSettingsProvider.notifier).updateVibrationEnabled(value);
              } : null,
              secondary: const Icon(Icons.vibration),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderStatistics(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(reminderStatisticsProvider);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات التذكيرات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            statisticsAsync.when(
              data: (statistics) => Column(
                children: [
                  _buildStatisticRow('إجمالي التذكيرات', statistics.totalReminders, Icons.notifications),
                  _buildStatisticRow('التذكيرات المفعلة', statistics.enabledReminders, Icons.notifications_active),
                  _buildStatisticRow('التذكيرات القادمة', statistics.upcomingReminders, Icons.schedule),
                  const SizedBox(height: AppConstants.defaultPadding),
                  LinearProgressIndicator(
                    value: statistics.enabledPercentage,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'نسبة التذكيرات المفعلة: ${(statistics.enabledPercentage * 100).toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                'خطأ في تحميل الإحصائيات',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticRow(String label, int value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            value.toString(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () async {
              final confirmed = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('تأكيد الحذف'),
                  content: const Text('هل تريد حذف جميع التنبيهات المجدولة؟'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text('حذف'),
                    ),
                  ],
                ),
              );
              
              if (confirmed == true) {
                await NotificationService.instance.cancelAllNotifications();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف جميع التنبيهات المجدولة'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
            icon: const Icon(Icons.clear_all),
            label: const Text('حذف جميع التنبيهات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () async {
              await NotificationService.instance.showImmediateNotification(
                title: 'اختبار التنبيه',
                body: 'هذا تنبيه تجريبي للتأكد من عمل النظام',
              );
            },
            icon: const Icon(Icons.science),
            label: const Text('اختبار التنبيه'),
          ),
        ),
      ],
    );
  }
}
