/// نموذج بيانات استخدام التطبيق
class AppUsage {
  final int? id;
  final String packageName;
  final String appName;
  final String? appIcon; // Base64 encoded icon
  final DateTime date;
  final int totalTimeInForeground; // بالميلي ثانية
  final int launchCount;
  final DateTime firstTimeStamp;
  final DateTime lastTimeStamp;
  final bool isSystemApp;
  final String category; // 'productivity', 'social', 'entertainment', etc.
  final DateTime createdAt;
  final DateTime updatedAt;

  const AppUsage({
    this.id,
    required this.packageName,
    required this.appName,
    this.appIcon,
    required this.date,
    required this.totalTimeInForeground,
    required this.launchCount,
    required this.firstTimeStamp,
    required this.lastTimeStamp,
    this.isSystemApp = false,
    this.category = 'other',
    required this.createdAt,
    required this.updatedAt,
  });

  /// Factory constructor لإنشاء استخدام تطبيق جديد
  factory AppUsage.create({
    required String packageName,
    required String appName,
    String? appIcon,
    required DateTime date,
    required int totalTimeInForeground,
    required int launchCount,
    required DateTime firstTimeStamp,
    required DateTime lastTimeStamp,
    bool isSystemApp = false,
    String category = 'other',
  }) {
    final now = DateTime.now();
    return AppUsage(
      packageName: packageName,
      appName: appName,
      appIcon: appIcon,
      date: date,
      totalTimeInForeground: totalTimeInForeground,
      launchCount: launchCount,
      firstTimeStamp: firstTimeStamp,
      lastTimeStamp: lastTimeStamp,
      isSystemApp: isSystemApp,
      category: category,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// تحويل من JSON
  factory AppUsage.fromJson(Map<String, dynamic> json) {
    return AppUsage(
      id: json['id'] as int?,
      packageName: json['packageName'] as String,
      appName: json['appName'] as String,
      appIcon: json['appIcon'] as String?,
      date: DateTime.parse(json['date'] as String),
      totalTimeInForeground: json['totalTimeInForeground'] as int,
      launchCount: json['launchCount'] as int,
      firstTimeStamp: DateTime.parse(json['firstTimeStamp'] as String),
      lastTimeStamp: DateTime.parse(json['lastTimeStamp'] as String),
      isSystemApp: json['isSystemApp'] as bool,
      category: json['category'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'packageName': packageName,
      'appName': appName,
      'appIcon': appIcon,
      'date': date.toIso8601String(),
      'totalTimeInForeground': totalTimeInForeground,
      'launchCount': launchCount,
      'firstTimeStamp': firstTimeStamp.toIso8601String(),
      'lastTimeStamp': lastTimeStamp.toIso8601String(),
      'isSystemApp': isSystemApp,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// تحويل من قاعدة البيانات
  factory AppUsage.fromDatabase(Map<String, dynamic> map) {
    return AppUsage(
      id: map['id'] as int?,
      packageName: map['packageName'] as String,
      appName: map['appName'] as String,
      appIcon: map['appIcon'] as String?,
      date: DateTime.parse(map['date'] as String),
      totalTimeInForeground: map['totalTimeInForeground'] as int,
      launchCount: map['launchCount'] as int,
      firstTimeStamp: DateTime.parse(map['firstTimeStamp'] as String),
      lastTimeStamp: DateTime.parse(map['lastTimeStamp'] as String),
      isSystemApp: (map['isSystemApp'] as int) == 1,
      category: map['category'] as String,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'packageName': packageName,
      'appName': appName,
      'appIcon': appIcon,
      'date': date.toIso8601String().split('T')[0], // تاريخ فقط
      'totalTimeInForeground': totalTimeInForeground,
      'launchCount': launchCount,
      'firstTimeStamp': firstTimeStamp.toIso8601String(),
      'lastTimeStamp': lastTimeStamp.toIso8601String(),
      'isSystemApp': isSystemApp ? 1 : 0,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديلات
  AppUsage copyWith({
    int? id,
    String? packageName,
    String? appName,
    String? appIcon,
    DateTime? date,
    int? totalTimeInForeground,
    int? launchCount,
    DateTime? firstTimeStamp,
    DateTime? lastTimeStamp,
    bool? isSystemApp,
    String? category,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppUsage(
      id: id ?? this.id,
      packageName: packageName ?? this.packageName,
      appName: appName ?? this.appName,
      appIcon: appIcon ?? this.appIcon,
      date: date ?? this.date,
      totalTimeInForeground:
          totalTimeInForeground ?? this.totalTimeInForeground,
      launchCount: launchCount ?? this.launchCount,
      firstTimeStamp: firstTimeStamp ?? this.firstTimeStamp,
      lastTimeStamp: lastTimeStamp ?? this.lastTimeStamp,
      isSystemApp: isSystemApp ?? this.isSystemApp,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحديث البيانات
  AppUsage update({
    String? appName,
    String? appIcon,
    int? totalTimeInForeground,
    int? launchCount,
    DateTime? firstTimeStamp,
    DateTime? lastTimeStamp,
    String? category,
  }) {
    return copyWith(
      appName: appName,
      appIcon: appIcon,
      totalTimeInForeground: totalTimeInForeground,
      launchCount: launchCount,
      firstTimeStamp: firstTimeStamp,
      lastTimeStamp: lastTimeStamp,
      category: category,
      updatedAt: DateTime.now(),
    );
  }

  /// الحصول على الوقت بالدقائق
  double get totalTimeInMinutes => totalTimeInForeground / (1000 * 60);

  /// الحصول على الوقت بالساعات
  double get totalTimeInHours => totalTimeInForeground / (1000 * 60 * 60);

  /// تحديد إذا كان التطبيق مشتت
  bool get isDistractingApp {
    const distractingCategories = [
      'social',
      'entertainment',
      'games',
      'video',
      'music',
    ];
    return distractingCategories.contains(category.toLowerCase());
  }

  /// تحديد إذا كان التطبيق منتج
  bool get isProductiveApp {
    const productiveCategories = [
      'productivity',
      'business',
      'education',
      'tools',
      'health',
    ];
    return productiveCategories.contains(category.toLowerCase());
  }

  /// تنسيق الوقت للعرض
  String get formattedTime {
    final hours = totalTimeInHours.floor();
    final minutes = (totalTimeInMinutes % 60).floor();

    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppUsage &&
        other.id == id &&
        other.packageName == packageName &&
        other.date == date;
  }

  @override
  int get hashCode {
    return Object.hash(id, packageName, date);
  }

  @override
  String toString() {
    return 'AppUsage(id: $id, packageName: $packageName, appName: $appName, '
        'totalTime: ${formattedTime}, launches: $launchCount)';
  }
}

/// تصنيفات التطبيقات
enum AppCategory {
  productivity('productivity', 'الإنتاجية'),
  social('social', 'التواصل الاجتماعي'),
  entertainment('entertainment', 'الترفيه'),
  games('games', 'الألعاب'),
  education('education', 'التعليم'),
  health('health', 'الصحة'),
  business('business', 'الأعمال'),
  tools('tools', 'الأدوات'),
  video('video', 'الفيديو'),
  music('music', 'الموسيقى'),
  news('news', 'الأخبار'),
  shopping('shopping', 'التسوق'),
  travel('travel', 'السفر'),
  finance('finance', 'المالية'),
  other('other', 'أخرى');

  const AppCategory(this.value, this.displayName);

  final String value;
  final String displayName;

  static AppCategory fromValue(String value) {
    return AppCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => AppCategory.other,
    );
  }
}
