/// نموذج بيانات أنماط سلوك المستخدم
class UserBehaviorPattern {
  final int? id;
  final String userId;
  final DateTime date;
  final BehaviorPatternType type;
  final Map<String, dynamic> data;
  final double confidence; // مستوى الثقة في النمط (0-1)
  final int frequency; // تكرار النمط
  final DateTime firstObserved;
  final DateTime lastObserved;
  final bool isActive;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserBehaviorPattern({
    this.id,
    required this.userId,
    required this.date,
    required this.type,
    required this.data,
    this.confidence = 0.0,
    this.frequency = 1,
    required this.firstObserved,
    required this.lastObserved,
    this.isActive = true,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Factory constructor لإنشاء نمط سلوك جديد
  factory UserBehaviorPattern.create({
    required String userId,
    required BehaviorPatternType type,
    required Map<String, dynamic> data,
    double confidence = 0.5,
    String? description,
  }) {
    final now = DateTime.now();
    return UserBehaviorPattern(
      userId: userId,
      date: now,
      type: type,
      data: data,
      confidence: confidence,
      firstObserved: now,
      lastObserved: now,
      description: description,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// أنماط سلوك شائعة

  /// نمط الاستخدام المفرط
  static UserBehaviorPattern excessiveUsage({
    required String userId,
    required String appName,
    required int dailyMinutes,
    required double averageDaily,
  }) {
    return UserBehaviorPattern.create(
      userId: userId,
      type: BehaviorPatternType.excessiveUsage,
      data: {
        'appName': appName,
        'dailyMinutes': dailyMinutes,
        'averageDaily': averageDaily,
        'excessPercentage': ((dailyMinutes - averageDaily) / averageDaily * 100),
      },
      confidence: dailyMinutes > averageDaily * 1.5 ? 0.9 : 0.6,
      description: 'استخدام مفرط لتطبيق $appName: $dailyMinutes دقيقة',
    );
  }

  /// نمط الاستخدام الليلي
  static UserBehaviorPattern nightUsage({
    required String userId,
    required int nightMinutes,
    required List<String> apps,
  }) {
    return UserBehaviorPattern.create(
      userId: userId,
      type: BehaviorPatternType.nightUsage,
      data: {
        'nightMinutes': nightMinutes,
        'apps': apps,
        'startHour': 22,
        'endHour': 6,
      },
      confidence: nightMinutes > 30 ? 0.8 : 0.4,
      description: 'استخدام ليلي للهاتف: $nightMinutes دقيقة',
    );
  }

  /// نمط التشتت المتكرر
  static UserBehaviorPattern frequentDistraction({
    required String userId,
    required int appSwitches,
    required int sessionCount,
    required List<String> distractingApps,
  }) {
    return UserBehaviorPattern.create(
      userId: userId,
      type: BehaviorPatternType.frequentDistraction,
      data: {
        'appSwitches': appSwitches,
        'sessionCount': sessionCount,
        'distractingApps': distractingApps,
        'averageSessionLength': appSwitches > 0 ? sessionCount / appSwitches : 0,
      },
      confidence: appSwitches > 50 ? 0.9 : 0.5,
      description: 'تشتت متكرر: $appSwitches تبديل تطبيق',
    );
  }

  /// نمط الإنتاجية العالية
  static UserBehaviorPattern highProductivity({
    required String userId,
    required int productiveMinutes,
    required int completedTasks,
    required int focusSessions,
  }) {
    return UserBehaviorPattern.create(
      userId: userId,
      type: BehaviorPatternType.highProductivity,
      data: {
        'productiveMinutes': productiveMinutes,
        'completedTasks': completedTasks,
        'focusSessions': focusSessions,
        'productivityScore': (productiveMinutes * 0.4 + completedTasks * 10 + focusSessions * 5),
      },
      confidence: productiveMinutes > 120 ? 0.9 : 0.6,
      description: 'إنتاجية عالية: $completedTasks مهمة مكتملة',
    );
  }

  /// نمط استخدام وقت الذروة
  static UserBehaviorPattern peakUsageTime({
    required String userId,
    required int peakHour,
    required int peakMinutes,
    required Map<int, int> hourlyUsage,
  }) {
    return UserBehaviorPattern.create(
      userId: userId,
      type: BehaviorPatternType.peakUsageTime,
      data: {
        'peakHour': peakHour,
        'peakMinutes': peakMinutes,
        'hourlyUsage': hourlyUsage,
        'peakPercentage': (peakMinutes / hourlyUsage.values.reduce((a, b) => a + b) * 100),
      },
      confidence: 0.8,
      description: 'وقت الذروة: الساعة $peakHour ($peakMinutes دقيقة)',
    );
  }

  /// تحويل من قاعدة البيانات
  factory UserBehaviorPattern.fromDatabase(Map<String, dynamic> map) {
    return UserBehaviorPattern(
      id: map['id'] as int?,
      userId: map['userId'] as String,
      date: DateTime.parse(map['date'] as String),
      type: BehaviorPatternType.fromValue(map['type'] as String),
      data: Map<String, dynamic>.from(map['data'] as Map),
      confidence: (map['confidence'] as num).toDouble(),
      frequency: map['frequency'] as int,
      firstObserved: DateTime.parse(map['firstObserved'] as String),
      lastObserved: DateTime.parse(map['lastObserved'] as String),
      isActive: (map['isActive'] as int) == 1,
      description: map['description'] as String?,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'userId': userId,
      'date': date.toIso8601String(),
      'type': type.value,
      'data': data.toString(),
      'confidence': confidence,
      'frequency': frequency,
      'firstObserved': firstObserved.toIso8601String(),
      'lastObserved': lastObserved.toIso8601String(),
      'isActive': isActive ? 1 : 0,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديلات
  UserBehaviorPattern copyWith({
    int? id,
    String? userId,
    DateTime? date,
    BehaviorPatternType? type,
    Map<String, dynamic>? data,
    double? confidence,
    int? frequency,
    DateTime? firstObserved,
    DateTime? lastObserved,
    bool? isActive,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserBehaviorPattern(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      type: type ?? this.type,
      data: data ?? this.data,
      confidence: confidence ?? this.confidence,
      frequency: frequency ?? this.frequency,
      firstObserved: firstObserved ?? this.firstObserved,
      lastObserved: lastObserved ?? this.lastObserved,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحديث النمط مع ملاحظة جديدة
  UserBehaviorPattern updateWithObservation({
    required Map<String, dynamic> newData,
    double? newConfidence,
  }) {
    final updatedFrequency = frequency + 1;
    final updatedConfidence = newConfidence ?? 
        ((confidence * frequency + (newConfidence ?? confidence)) / updatedFrequency);
    
    return copyWith(
      data: {...data, ...newData},
      confidence: updatedConfidence.clamp(0.0, 1.0),
      frequency: updatedFrequency,
      lastObserved: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// التحقق من قوة النمط
  PatternStrength get strength {
    if (confidence >= 0.8 && frequency >= 5) return PatternStrength.strong;
    if (confidence >= 0.6 && frequency >= 3) return PatternStrength.moderate;
    if (confidence >= 0.4 && frequency >= 2) return PatternStrength.weak;
    return PatternStrength.emerging;
  }

  /// الحصول على توصية بناءً على النمط
  String get recommendation {
    switch (type) {
      case BehaviorPatternType.excessiveUsage:
        final appName = data['appName'] as String;
        return 'حاول تقليل استخدام $appName وفعّل وضع التركيز';
      
      case BehaviorPatternType.nightUsage:
        return 'تجنب استخدام الهاتف قبل النوم لتحسين جودة النوم';
      
      case BehaviorPatternType.frequentDistraction:
        return 'استخدم تقنية Pomodoro وفعّل حجب التطبيقات المشتتة';
      
      case BehaviorPatternType.highProductivity:
        return 'ممتاز! حافظ على هذا الأداء وشارك إنجازاتك';
      
      case BehaviorPatternType.peakUsageTime:
        final hour = data['peakHour'] as int;
        return 'وقت ذروة استخدامك الساعة $hour - خطط مهامك المهمة في هذا الوقت';
      
      default:
        return 'استمر في مراقبة أنماط استخدامك لتحسين إنتاجيتك';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserBehaviorPattern && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserBehaviorPattern(id: $id, type: ${type.displayName}, confidence: $confidence)';
  }
}

/// أنواع أنماط السلوك
enum BehaviorPatternType {
  excessiveUsage('excessive_usage', 'استخدام مفرط'),
  nightUsage('night_usage', 'استخدام ليلي'),
  frequentDistraction('frequent_distraction', 'تشتت متكرر'),
  highProductivity('high_productivity', 'إنتاجية عالية'),
  peakUsageTime('peak_usage_time', 'وقت الذروة'),
  appAddiction('app_addiction', 'إدمان تطبيق'),
  healthyBreaks('healthy_breaks', 'استراحات صحية'),
  focusImprovement('focus_improvement', 'تحسن التركيز');

  const BehaviorPatternType(this.value, this.displayName);

  final String value;
  final String displayName;

  static BehaviorPatternType fromValue(String value) {
    return BehaviorPatternType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => BehaviorPatternType.excessiveUsage,
    );
  }
}

/// قوة النمط
enum PatternStrength {
  emerging('ناشئ'),
  weak('ضعيف'),
  moderate('متوسط'),
  strong('قوي');

  const PatternStrength(this.displayName);
  final String displayName;
}
