class Reminder {
  final int? id;
  final int taskId;
  final DateTime reminderDateTime;
  final ReminderType type;
  final ReminderFrequency frequency;
  final bool isEnabled;
  final String? customMessage;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Reminder({
    this.id,
    required this.taskId,
    required this.reminderDateTime,
    required this.type,
    this.frequency = ReminderFrequency.once,
    this.isEnabled = true,
    this.customMessage,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory constructor لإنشاء تذكير جديد
  factory Reminder.create({
    required int taskId,
    required DateTime reminderDateTime,
    required ReminderType type,
    ReminderFrequency frequency = ReminderFrequency.once,
    String? customMessage,
  }) {
    final now = DateTime.now();
    return Reminder(
      taskId: taskId,
      reminderDateTime: reminderDateTime,
      type: type,
      frequency: frequency,
      customMessage: customMessage,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Factory constructor من JSON
  factory Reminder.fromJson(Map<String, dynamic> json) {
    return Reminder(
      id: json['id'] as int?,
      taskId: json['taskId'] as int,
      reminderDateTime: DateTime.parse(json['reminderDateTime'] as String),
      type: ReminderType.fromString(json['type'] as String),
      frequency: ReminderFrequency.fromString(json['frequency'] as String),
      isEnabled: json['isEnabled'] as bool,
      customMessage: json['customMessage'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'reminderDateTime': reminderDateTime.toIso8601String(),
      'type': type.value,
      'frequency': frequency.value,
      'isEnabled': isEnabled,
      'customMessage': customMessage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Factory constructor من قاعدة البيانات
  factory Reminder.fromDatabase(Map<String, dynamic> map) {
    return Reminder(
      id: map['id'] as int?,
      taskId: map['taskId'] as int,
      reminderDateTime: DateTime.parse(map['reminderDateTime'] as String),
      type: ReminderType.fromString(map['type'] as String),
      frequency: ReminderFrequency.fromString(map['frequency'] as String),
      isEnabled: (map['isEnabled'] as int) == 1,
      customMessage: map['customMessage'] as String?,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  // تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'taskId': taskId,
      'reminderDateTime': reminderDateTime.toIso8601String(),
      'type': type.value,
      'frequency': frequency.value,
      'isEnabled': isEnabled ? 1 : 0,
      'customMessage': customMessage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // نسخ مع تعديلات
  Reminder copyWith({
    int? id,
    int? taskId,
    DateTime? reminderDateTime,
    ReminderType? type,
    ReminderFrequency? frequency,
    bool? isEnabled,
    String? customMessage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Reminder(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      reminderDateTime: reminderDateTime ?? this.reminderDateTime,
      type: type ?? this.type,
      frequency: frequency ?? this.frequency,
      isEnabled: isEnabled ?? this.isEnabled,
      customMessage: customMessage ?? this.customMessage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // تبديل حالة التفعيل
  Reminder toggleEnabled() {
    return copyWith(
      isEnabled: !isEnabled,
      updatedAt: DateTime.now(),
    );
  }

  // تحديث التذكير
  Reminder update({
    DateTime? reminderDateTime,
    ReminderType? type,
    ReminderFrequency? frequency,
    String? customMessage,
  }) {
    return copyWith(
      reminderDateTime: reminderDateTime,
      type: type,
      frequency: frequency,
      customMessage: customMessage,
      updatedAt: DateTime.now(),
    );
  }

  // حساب التذكير التالي للتذكيرات المتكررة
  DateTime? getNextReminderDateTime() {
    if (frequency == ReminderFrequency.once) {
      return null;
    }

    final now = DateTime.now();
    if (reminderDateTime.isAfter(now)) {
      return reminderDateTime;
    }

    switch (frequency) {
      case ReminderFrequency.daily:
        return reminderDateTime.add(const Duration(days: 1));
      case ReminderFrequency.weekly:
        return reminderDateTime.add(const Duration(days: 7));
      case ReminderFrequency.monthly:
        return DateTime(
          reminderDateTime.year,
          reminderDateTime.month + 1,
          reminderDateTime.day,
          reminderDateTime.hour,
          reminderDateTime.minute,
        );
      case ReminderFrequency.once:
        return null;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Reminder &&
        other.id == id &&
        other.taskId == taskId &&
        other.reminderDateTime == reminderDateTime &&
        other.type == type &&
        other.frequency == frequency;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      taskId,
      reminderDateTime,
      type,
      frequency,
    );
  }

  @override
  String toString() {
    return 'Reminder(id: $id, taskId: $taskId, type: $type, frequency: $frequency)';
  }
}

// تعداد أنواع التذكيرات
enum ReminderType {
  notification('إشعار'),
  sound('صوتي'),
  vibration('اهتزاز');

  const ReminderType(this.value);

  final String value;

  static ReminderType fromString(String value) {
    switch (value) {
      case 'إشعار':
        return ReminderType.notification;
      case 'صوتي':
        return ReminderType.sound;
      case 'اهتزاز':
        return ReminderType.vibration;
      default:
        return ReminderType.notification;
    }
  }
}

// تعداد تكرار التذكيرات
enum ReminderFrequency {
  once('مرة واحدة'),
  daily('يومياً'),
  weekly('أسبوعياً'),
  monthly('شهرياً');

  const ReminderFrequency(this.value);

  final String value;

  static ReminderFrequency fromString(String value) {
    switch (value) {
      case 'مرة واحدة':
        return ReminderFrequency.once;
      case 'يومياً':
        return ReminderFrequency.daily;
      case 'أسبوعياً':
        return ReminderFrequency.weekly;
      case 'شهرياً':
        return ReminderFrequency.monthly;
      default:
        return ReminderFrequency.once;
    }
  }

  // الحصول على المدة بالأيام
  int get daysInterval {
    switch (this) {
      case ReminderFrequency.once:
        return 0;
      case ReminderFrequency.daily:
        return 1;
      case ReminderFrequency.weekly:
        return 7;
      case ReminderFrequency.monthly:
        return 30; // تقريبي
    }
  }
}
