/// نموذج بيانات التنبيهات الذكية
class SmartNotification {
  final int? id;
  final String title;
  final String message;
  final SmartNotificationType type;
  final SmartNotificationPriority priority;
  final DateTime scheduledTime;
  final bool isDelivered;
  final bool isRead;
  final Map<String, dynamic>? data; // بيانات إضافية
  final String? actionType; // نوع الإجراء المطلوب
  final Map<String, dynamic>? actionData; // بيانات الإجراء
  final DateTime createdAt;
  final DateTime? deliveredAt;
  final DateTime? readAt;

  const SmartNotification({
    this.id,
    required this.title,
    required this.message,
    required this.type,
    this.priority = SmartNotificationPriority.normal,
    required this.scheduledTime,
    this.isDelivered = false,
    this.isRead = false,
    this.data,
    this.actionType,
    this.actionData,
    required this.createdAt,
    this.deliveredAt,
    this.readAt,
  });

  /// Factory constructor لإنشاء تنبيه ذكي جديد
  factory SmartNotification.create({
    required String title,
    required String message,
    required SmartNotificationType type,
    SmartNotificationPriority priority = SmartNotificationPriority.normal,
    required DateTime scheduledTime,
    Map<String, dynamic>? data,
    String? actionType,
    Map<String, dynamic>? actionData,
  }) {
    return SmartNotification(
      title: title,
      message: message,
      type: type,
      priority: priority,
      scheduledTime: scheduledTime,
      data: data,
      actionType: actionType,
      actionData: actionData,
      createdAt: DateTime.now(),
    );
  }

  /// تنبيهات ذكية مُعرفة مسبقاً

  /// تنبيه استراحة ذكي
  static SmartNotification breakReminder({
    required Duration workDuration,
    required int sessionsCompleted,
  }) {
    String message;
    if (workDuration.inMinutes >= 90) {
      message = 'لقد عملت لمدة ${workDuration.inMinutes} دقيقة. حان وقت استراحة طويلة!';
    } else if (workDuration.inMinutes >= 25) {
      message = 'أحسنت! خذ استراحة قصيرة لمدة 5 دقائق.';
    } else {
      message = 'وقت الاستراحة! امنح عقلك قسطاً من الراحة.';
    }

    return SmartNotification.create(
      title: 'وقت الاستراحة 🧘',
      message: message,
      type: SmartNotificationType.breakReminder,
      priority: SmartNotificationPriority.high,
      scheduledTime: DateTime.now(),
      data: {
        'workDuration': workDuration.inMinutes,
        'sessionsCompleted': sessionsCompleted,
      },
      actionType: 'start_break',
    );
  }

  /// تنبيه العودة للعمل
  static SmartNotification workReminder({
    required Duration breakDuration,
  }) {
    return SmartNotification.create(
      title: 'العودة للعمل 💪',
      message: 'انتهت استراحتك. حان وقت العودة للتركيز!',
      type: SmartNotificationType.workReminder,
      priority: SmartNotificationPriority.high,
      scheduledTime: DateTime.now(),
      data: {
        'breakDuration': breakDuration.inMinutes,
      },
      actionType: 'start_work',
    );
  }

  /// تنبيه الاستخدام المفرط
  static SmartNotification excessiveUsageWarning({
    required String appName,
    required Duration usageTime,
    required double dailyLimit,
  }) {
    return SmartNotification.create(
      title: 'تحذير: استخدام مفرط ⚠️',
      message: 'لقد قضيت ${usageTime.inMinutes} دقيقة في $appName اليوم. هذا أكثر من الحد المُوصى به.',
      type: SmartNotificationType.usageWarning,
      priority: SmartNotificationPriority.high,
      scheduledTime: DateTime.now(),
      data: {
        'appName': appName,
        'usageMinutes': usageTime.inMinutes,
        'dailyLimit': dailyLimit,
      },
      actionType: 'enable_focus_mode',
    );
  }

  /// تنبيه إنجاز الهدف
  static SmartNotification goalAchievement({
    required String goalName,
    required String goalType,
    required double progress,
  }) {
    return SmartNotification.create(
      title: 'تهانينا! 🎉',
      message: 'لقد حققت هدف "$goalName" بنسبة ${progress.toStringAsFixed(0)}%!',
      type: SmartNotificationType.goalAchievement,
      priority: SmartNotificationPriority.normal,
      scheduledTime: DateTime.now(),
      data: {
        'goalName': goalName,
        'goalType': goalType,
        'progress': progress,
      },
      actionType: 'view_achievements',
    );
  }

  /// تنبيه تذكير المهمة
  static SmartNotification taskReminder({
    required String taskTitle,
    required DateTime dueDate,
    required String priority,
  }) {
    final timeUntilDue = dueDate.difference(DateTime.now());
    String message;
    
    if (timeUntilDue.inHours < 1) {
      message = 'مهمة "$taskTitle" مستحقة خلال ${timeUntilDue.inMinutes} دقيقة!';
    } else if (timeUntilDue.inHours < 24) {
      message = 'مهمة "$taskTitle" مستحقة خلال ${timeUntilDue.inHours} ساعة.';
    } else {
      message = 'تذكير: مهمة "$taskTitle" مستحقة قريباً.';
    }

    return SmartNotification.create(
      title: 'تذكير مهمة 📋',
      message: message,
      type: SmartNotificationType.taskReminder,
      priority: priority == 'urgent' 
          ? SmartNotificationPriority.urgent 
          : SmartNotificationPriority.normal,
      scheduledTime: DateTime.now(),
      data: {
        'taskTitle': taskTitle,
        'dueDate': dueDate.toIso8601String(),
        'priority': priority,
      },
      actionType: 'view_task',
    );
  }

  /// تنبيه اقتراح وضع التركيز
  static SmartNotification focusModesuggestion({
    required String reason,
    required String suggestedMode,
  }) {
    return SmartNotification.create(
      title: 'اقتراح وضع التركيز 🎯',
      message: 'بناءً على نشاطك، نقترح تفعيل "$suggestedMode". $reason',
      type: SmartNotificationType.focusSuggestion,
      priority: SmartNotificationPriority.normal,
      scheduledTime: DateTime.now(),
      data: {
        'reason': reason,
        'suggestedMode': suggestedMode,
      },
      actionType: 'enable_focus_mode',
      actionData: {
        'mode': suggestedMode,
      },
    );
  }

  /// تحويل من قاعدة البيانات
  factory SmartNotification.fromDatabase(Map<String, dynamic> map) {
    return SmartNotification(
      id: map['id'] as int?,
      title: map['title'] as String,
      message: map['message'] as String,
      type: SmartNotificationType.fromValue(map['type'] as String),
      priority: SmartNotificationPriority.fromValue(map['priority'] as String),
      scheduledTime: DateTime.parse(map['scheduledTime'] as String),
      isDelivered: (map['isDelivered'] as int) == 1,
      isRead: (map['isRead'] as int) == 1,
      data: map['data'] != null ? Map<String, dynamic>.from(map['data'] as Map) : null,
      actionType: map['actionType'] as String?,
      actionData: map['actionData'] != null ? Map<String, dynamic>.from(map['actionData'] as Map) : null,
      createdAt: DateTime.parse(map['createdAt'] as String),
      deliveredAt: map['deliveredAt'] != null ? DateTime.parse(map['deliveredAt'] as String) : null,
      readAt: map['readAt'] != null ? DateTime.parse(map['readAt'] as String) : null,
    );
  }

  /// تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.value,
      'priority': priority.value,
      'scheduledTime': scheduledTime.toIso8601String(),
      'isDelivered': isDelivered ? 1 : 0,
      'isRead': isRead ? 1 : 0,
      'data': data?.toString(),
      'actionType': actionType,
      'actionData': actionData?.toString(),
      'createdAt': createdAt.toIso8601String(),
      'deliveredAt': deliveredAt?.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
    };
  }

  /// نسخ مع تعديلات
  SmartNotification copyWith({
    int? id,
    String? title,
    String? message,
    SmartNotificationType? type,
    SmartNotificationPriority? priority,
    DateTime? scheduledTime,
    bool? isDelivered,
    bool? isRead,
    Map<String, dynamic>? data,
    String? actionType,
    Map<String, dynamic>? actionData,
    DateTime? createdAt,
    DateTime? deliveredAt,
    DateTime? readAt,
  }) {
    return SmartNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      isDelivered: isDelivered ?? this.isDelivered,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      actionType: actionType ?? this.actionType,
      actionData: actionData ?? this.actionData,
      createdAt: createdAt ?? this.createdAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
    );
  }

  /// تسليم التنبيه
  SmartNotification markAsDelivered() {
    return copyWith(
      isDelivered: true,
      deliveredAt: DateTime.now(),
    );
  }

  /// قراءة التنبيه
  SmartNotification markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now(),
    );
  }

  /// التحقق من انتهاء صلاحية التنبيه
  bool get isExpired {
    final now = DateTime.now();
    return scheduledTime.isBefore(now.subtract(const Duration(hours: 24)));
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmartNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SmartNotification(id: $id, title: $title, type: ${type.displayName})';
  }
}

/// أنواع التنبيهات الذكية
enum SmartNotificationType {
  breakReminder('break_reminder', 'تذكير استراحة', '🧘'),
  workReminder('work_reminder', 'تذكير عمل', '💪'),
  usageWarning('usage_warning', 'تحذير استخدام', '⚠️'),
  goalAchievement('goal_achievement', 'إنجاز هدف', '🎉'),
  taskReminder('task_reminder', 'تذكير مهمة', '📋'),
  focusSuggestion('focus_suggestion', 'اقتراح تركيز', '🎯'),
  habitReminder('habit_reminder', 'تذكير عادة', '🔄'),
  motivational('motivational', 'تحفيزي', '💪');

  const SmartNotificationType(this.value, this.displayName, this.emoji);

  final String value;
  final String displayName;
  final String emoji;

  static SmartNotificationType fromValue(String value) {
    return SmartNotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SmartNotificationType.motivational,
    );
  }
}

/// أولويات التنبيهات الذكية
enum SmartNotificationPriority {
  low('low', 'منخفضة'),
  normal('normal', 'عادية'),
  high('high', 'عالية'),
  urgent('urgent', 'عاجلة');

  const SmartNotificationPriority(this.value, this.displayName);

  final String value;
  final String displayName;

  static SmartNotificationPriority fromValue(String value) {
    return SmartNotificationPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => SmartNotificationPriority.normal,
    );
  }
}
