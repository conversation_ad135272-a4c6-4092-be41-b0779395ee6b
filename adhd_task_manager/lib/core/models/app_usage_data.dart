import 'dart:typed_data';

/// نموذج بيانات التطبيق للخدمة الحقيقية
class AppUsageData {
  final String appName;
  final String packageName;
  final Duration usageTime;
  final String category;
  final DateTime? lastTimeUsed;
  final Uint8List? icon;
  final int launchCount;

  const AppUsageData({
    required this.appName,
    required this.packageName,
    required this.usageTime,
    required this.category,
    this.lastTimeUsed,
    this.icon,
    this.launchCount = 0,
  });

  /// نسخ مع تعديلات
  AppUsageData copyWith({
    String? appName,
    String? packageName,
    Duration? usageTime,
    String? category,
    DateTime? lastTimeUsed,
    Uint8List? icon,
    int? launchCount,
  }) {
    return AppUsageData(
      appName: appName ?? this.appName,
      packageName: packageName ?? this.packageName,
      usageTime: usageTime ?? this.usageTime,
      category: category ?? this.category,
      lastTimeUsed: lastTimeUsed ?? this.lastTimeUsed,
      icon: icon ?? this.icon,
      launchCount: launchCount ?? this.launchCount,
    );
  }

  /// تنسيق الوقت للعرض
  String get formattedTime {
    final hours = usageTime.inHours;
    final minutes = usageTime.inMinutes % 60;

    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }

  /// تحديد إذا كان التطبيق مشتت
  bool get isDistractingApp {
    const distractingCategories = [
      'التواصل الاجتماعي',
      'الترفيه',
      'الألعاب',
    ];
    return distractingCategories.contains(category);
  }

  /// تحديد إذا كان التطبيق منتج
  bool get isProductiveApp {
    const productiveCategories = [
      'الإنتاجية',
      'الأعمال',
      'التعليم',
    ];
    return productiveCategories.contains(category);
  }

  @override
  String toString() {
    return 'AppUsageData(appName: $appName, usageTime: $formattedTime)';
  }
}
