class FocusSession {
  final int? id;
  final DateTime startTime;
  final DateTime? endTime;
  final int plannedDuration; // بالدقائق
  final int? actualDuration; // بالدقائق
  final FocusSessionType type;
  final FocusSessionStatus status;
  final String? notes;
  final int? taskId; // المهمة المرتبطة (اختياري)
  final DateTime createdAt;
  final DateTime updatedAt;

  const FocusSession({
    this.id,
    required this.startTime,
    this.endTime,
    required this.plannedDuration,
    this.actualDuration,
    required this.type,
    required this.status,
    this.notes,
    this.taskId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory constructor لإنشاء جلسة جديدة
  factory FocusSession.create({
    required FocusSessionType type,
    required int plannedDuration,
    int? taskId,
    String? notes,
  }) {
    final now = DateTime.now();
    return FocusSession(
      startTime: now,
      plannedDuration: plannedDuration,
      type: type,
      status: FocusSessionStatus.active,
      taskId: taskId,
      notes: notes,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Factory constructor من قاعدة البيانات
  factory FocusSession.fromDatabase(Map<String, dynamic> map) {
    return FocusSession(
      id: map['id'] as int?,
      startTime: DateTime.parse(map['startTime'] as String),
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime'] as String) : null,
      plannedDuration: map['plannedDuration'] as int,
      actualDuration: map['actualDuration'] as int?,
      type: FocusSessionType.fromString(map['type'] as String),
      status: FocusSessionStatus.fromString(map['status'] as String),
      notes: map['notes'] as String?,
      taskId: map['taskId'] as int?,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  // تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'plannedDuration': plannedDuration,
      'actualDuration': actualDuration,
      'type': type.value,
      'status': status.value,
      'notes': notes,
      'taskId': taskId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // نسخ مع تعديلات
  FocusSession copyWith({
    int? id,
    DateTime? startTime,
    DateTime? endTime,
    int? plannedDuration,
    int? actualDuration,
    FocusSessionType? type,
    FocusSessionStatus? status,
    String? notes,
    int? taskId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FocusSession(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      plannedDuration: plannedDuration ?? this.plannedDuration,
      actualDuration: actualDuration ?? this.actualDuration,
      type: type ?? this.type,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      taskId: taskId ?? this.taskId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // إنهاء الجلسة
  FocusSession complete() {
    final now = DateTime.now();
    final duration = now.difference(startTime).inMinutes;
    
    return copyWith(
      endTime: now,
      actualDuration: duration,
      status: FocusSessionStatus.completed,
      updatedAt: now,
    );
  }

  // إلغاء الجلسة
  FocusSession cancel() {
    final now = DateTime.now();
    final duration = now.difference(startTime).inMinutes;
    
    return copyWith(
      endTime: now,
      actualDuration: duration,
      status: FocusSessionStatus.cancelled,
      updatedAt: now,
    );
  }

  // إيقاف مؤقت الجلسة
  FocusSession pause() {
    return copyWith(
      status: FocusSessionStatus.paused,
      updatedAt: DateTime.now(),
    );
  }

  // استئناف الجلسة
  FocusSession resume() {
    return copyWith(
      status: FocusSessionStatus.active,
      updatedAt: DateTime.now(),
    );
  }

  // حساب المدة المتبقية
  Duration get remainingDuration {
    if (status == FocusSessionStatus.completed || status == FocusSessionStatus.cancelled) {
      return Duration.zero;
    }
    
    final elapsed = DateTime.now().difference(startTime);
    final planned = Duration(minutes: plannedDuration);
    final remaining = planned - elapsed;
    
    return remaining.isNegative ? Duration.zero : remaining;
  }

  // حساب المدة المنقضية
  Duration get elapsedDuration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  // حساب نسبة الإكمال
  double get completionPercentage {
    if (plannedDuration == 0) return 0.0;
    
    final elapsed = elapsedDuration.inMinutes;
    return (elapsed / plannedDuration).clamp(0.0, 1.0);
  }

  // التحقق من انتهاء الجلسة
  bool get isExpired {
    return remainingDuration == Duration.zero;
  }

  // التحقق من نجاح الجلسة
  bool get isSuccessful {
    return status == FocusSessionStatus.completed && 
           actualDuration != null && 
           actualDuration! >= (plannedDuration * 0.8); // 80% من المدة المخططة
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FocusSession &&
        other.id == id &&
        other.startTime == startTime &&
        other.type == type &&
        other.status == status;
  }

  @override
  int get hashCode {
    return Object.hash(id, startTime, type, status);
  }

  @override
  String toString() {
    return 'FocusSession(id: $id, type: $type, status: $status, duration: ${plannedDuration}min)';
  }
}

// تعداد أنواع جلسات التركيز
enum FocusSessionType {
  work('عمل'),
  shortBreak('راحة قصيرة'),
  longBreak('راحة طويلة');

  const FocusSessionType(this.value);

  final String value;

  static FocusSessionType fromString(String value) {
    switch (value) {
      case 'عمل':
        return FocusSessionType.work;
      case 'راحة قصيرة':
        return FocusSessionType.shortBreak;
      case 'راحة طويلة':
        return FocusSessionType.longBreak;
      default:
        return FocusSessionType.work;
    }
  }

  // الحصول على اللون المناسب
  String get colorHex {
    switch (this) {
      case FocusSessionType.work:
        return '#E53E3E'; // أحمر
      case FocusSessionType.shortBreak:
        return '#38A169'; // أخضر
      case FocusSessionType.longBreak:
        return '#3182CE'; // أزرق
    }
  }

  // الحصول على الأيقونة المناسبة
  String get iconName {
    switch (this) {
      case FocusSessionType.work:
        return 'work';
      case FocusSessionType.shortBreak:
        return 'coffee';
      case FocusSessionType.longBreak:
        return 'hotel';
    }
  }
}

// تعداد حالات جلسات التركيز
enum FocusSessionStatus {
  active('نشطة'),
  paused('متوقفة'),
  completed('مكتملة'),
  cancelled('ملغية');

  const FocusSessionStatus(this.value);

  final String value;

  static FocusSessionStatus fromString(String value) {
    switch (value) {
      case 'نشطة':
        return FocusSessionStatus.active;
      case 'متوقفة':
        return FocusSessionStatus.paused;
      case 'مكتملة':
        return FocusSessionStatus.completed;
      case 'ملغية':
        return FocusSessionStatus.cancelled;
      default:
        return FocusSessionStatus.active;
    }
  }

  // التحقق من كون الجلسة نشطة
  bool get isActive => this == FocusSessionStatus.active;
  
  // التحقق من كون الجلسة متوقفة
  bool get isPaused => this == FocusSessionStatus.paused;
  
  // التحقق من كون الجلسة منتهية
  bool get isFinished => this == FocusSessionStatus.completed || this == FocusSessionStatus.cancelled;
}

// فئة إعدادات Pomodoro
class PomodoroSettings {
  final int workDuration; // بالدقائق
  final int shortBreakDuration; // بالدقائق
  final int longBreakDuration; // بالدقائق
  final int sessionsUntilLongBreak; // عدد جلسات العمل قبل الراحة الطويلة
  final bool autoStartBreaks; // بدء الراحة تلقائياً
  final bool autoStartWork; // بدء العمل تلقائياً بعد الراحة
  final bool enableNotifications; // تفعيل التنبيهات
  final bool enableSounds; // تفعيل الأصوات

  const PomodoroSettings({
    this.workDuration = 25,
    this.shortBreakDuration = 5,
    this.longBreakDuration = 15,
    this.sessionsUntilLongBreak = 4,
    this.autoStartBreaks = false,
    this.autoStartWork = false,
    this.enableNotifications = true,
    this.enableSounds = true,
  });

  PomodoroSettings copyWith({
    int? workDuration,
    int? shortBreakDuration,
    int? longBreakDuration,
    int? sessionsUntilLongBreak,
    bool? autoStartBreaks,
    bool? autoStartWork,
    bool? enableNotifications,
    bool? enableSounds,
  }) {
    return PomodoroSettings(
      workDuration: workDuration ?? this.workDuration,
      shortBreakDuration: shortBreakDuration ?? this.shortBreakDuration,
      longBreakDuration: longBreakDuration ?? this.longBreakDuration,
      sessionsUntilLongBreak: sessionsUntilLongBreak ?? this.sessionsUntilLongBreak,
      autoStartBreaks: autoStartBreaks ?? this.autoStartBreaks,
      autoStartWork: autoStartWork ?? this.autoStartWork,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableSounds: enableSounds ?? this.enableSounds,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PomodoroSettings &&
        other.workDuration == workDuration &&
        other.shortBreakDuration == shortBreakDuration &&
        other.longBreakDuration == longBreakDuration &&
        other.sessionsUntilLongBreak == sessionsUntilLongBreak;
  }

  @override
  int get hashCode {
    return Object.hash(
      workDuration,
      shortBreakDuration,
      longBreakDuration,
      sessionsUntilLongBreak,
    );
  }
}
