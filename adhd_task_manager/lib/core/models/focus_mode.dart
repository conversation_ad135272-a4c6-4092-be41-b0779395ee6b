/// نموذج بيانات وضع التركيز المتقدم
class FocusMode {
  final int? id;
  final String name;
  final String description;
  final bool isActive;
  final List<String> blockedApps;
  final List<String> allowedApps;
  final FocusModeType type;
  final int? duration; // بالدقائق، null للوضع اللانهائي
  final DateTime? startTime;
  final DateTime? endTime;
  final bool blockNotifications;
  final bool allowCalls;
  final bool allowMessages;
  final String? customMessage; // رسالة مخصصة عند محاولة فتح تطبيق محجوب
  final Map<String, dynamic>? settings; // إعدادات إضافية
  final DateTime createdAt;
  final DateTime updatedAt;

  const FocusMode({
    this.id,
    required this.name,
    required this.description,
    this.isActive = false,
    this.blockedApps = const [],
    this.allowedApps = const [],
    this.type = FocusModeType.work,
    this.duration,
    this.startTime,
    this.endTime,
    this.blockNotifications = false,
    this.allowCalls = true,
    this.allowMessages = false,
    this.customMessage,
    this.settings,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Factory constructor لإنشاء وضع تركيز جديد
  factory FocusMode.create({
    required String name,
    required String description,
    FocusModeType type = FocusModeType.work,
    List<String> blockedApps = const [],
    List<String> allowedApps = const [],
    int? duration,
    bool blockNotifications = false,
    bool allowCalls = true,
    bool allowMessages = false,
    String? customMessage,
    Map<String, dynamic>? settings,
  }) {
    final now = DateTime.now();
    return FocusMode(
      name: name,
      description: description,
      type: type,
      blockedApps: blockedApps,
      allowedApps: allowedApps,
      duration: duration,
      blockNotifications: blockNotifications,
      allowCalls: allowCalls,
      allowMessages: allowMessages,
      customMessage: customMessage,
      settings: settings,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// أوضاع تركيز مُعرفة مسبقاً
  static FocusMode get workMode => FocusMode.create(
    name: 'وضع العمل',
    description: 'للتركيز على المهام المهنية والإنتاجية',
    type: FocusModeType.work,
    blockedApps: [
      'com.facebook.katana',
      'com.instagram.android',
      'com.twitter.android',
      'com.snapchat.android',
      'com.google.android.youtube',
      'com.netflix.mediaclient',
    ],
    allowedApps: [
      'com.microsoft.office.word',
      'com.google.android.apps.docs.editors.docs',
      'com.microsoft.office.excel',
      'com.slack',
      'com.microsoft.teams',
    ],
    blockNotifications: true,
    allowCalls: true,
    allowMessages: false,
    customMessage: 'أنت في وضع العمل. ركز على مهامك!',
  );

  static FocusMode get studyMode => FocusMode.create(
    name: 'وضع الدراسة',
    description: 'للتركيز على التعلم والدراسة',
    type: FocusModeType.study,
    blockedApps: [
      'com.facebook.katana',
      'com.instagram.android',
      'com.twitter.android',
      'com.google.android.youtube',
      'com.king.candycrushsaga',
    ],
    allowedApps: [
      'com.duolingo',
      'com.khanacademy.android',
      'com.coursera.android',
      'com.google.android.apps.docs.editors.docs',
    ],
    blockNotifications: true,
    allowCalls: false,
    allowMessages: false,
    customMessage: 'وقت الدراسة! ركز على تعلمك.',
  );

  static FocusMode get sleepMode => FocusMode.create(
    name: 'وضع النوم',
    description: 'لتحسين جودة النوم وتقليل التشتت',
    type: FocusModeType.sleep,
    blockedApps: [
      'com.facebook.katana',
      'com.instagram.android',
      'com.twitter.android',
      'com.google.android.youtube',
      'com.netflix.mediaclient',
    ],
    allowedApps: [
      'com.headspace.android',
      'com.calm.android',
      'com.urbandroid.sleep',
    ],
    blockNotifications: true,
    allowCalls: true,
    allowMessages: false,
    customMessage: 'وقت النوم. أغلق الهاتف واسترح.',
  );

  /// تحويل من قاعدة البيانات
  factory FocusMode.fromDatabase(Map<String, dynamic> map) {
    return FocusMode(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String,
      isActive: (map['isActive'] as int) == 1,
      blockedApps: (map['blockedApps'] as String).split(',').where((app) => app.isNotEmpty).toList(),
      allowedApps: (map['allowedApps'] as String).split(',').where((app) => app.isNotEmpty).toList(),
      type: FocusModeType.fromValue(map['type'] as String),
      duration: map['duration'] as int?,
      startTime: map['startTime'] != null ? DateTime.parse(map['startTime'] as String) : null,
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime'] as String) : null,
      blockNotifications: (map['blockNotifications'] as int) == 1,
      allowCalls: (map['allowCalls'] as int) == 1,
      allowMessages: (map['allowMessages'] as int) == 1,
      customMessage: map['customMessage'] as String?,
      settings: map['settings'] != null ? Map<String, dynamic>.from(map['settings'] as Map) : null,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isActive': isActive ? 1 : 0,
      'blockedApps': blockedApps.join(','),
      'allowedApps': allowedApps.join(','),
      'type': type.value,
      'duration': duration,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'blockNotifications': blockNotifications ? 1 : 0,
      'allowCalls': allowCalls ? 1 : 0,
      'allowMessages': allowMessages ? 1 : 0,
      'customMessage': customMessage,
      'settings': settings?.toString(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديلات
  FocusMode copyWith({
    int? id,
    String? name,
    String? description,
    bool? isActive,
    List<String>? blockedApps,
    List<String>? allowedApps,
    FocusModeType? type,
    int? duration,
    DateTime? startTime,
    DateTime? endTime,
    bool? blockNotifications,
    bool? allowCalls,
    bool? allowMessages,
    String? customMessage,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FocusMode(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      blockedApps: blockedApps ?? this.blockedApps,
      allowedApps: allowedApps ?? this.allowedApps,
      type: type ?? this.type,
      duration: duration ?? this.duration,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      blockNotifications: blockNotifications ?? this.blockNotifications,
      allowCalls: allowCalls ?? this.allowCalls,
      allowMessages: allowMessages ?? this.allowMessages,
      customMessage: customMessage ?? this.customMessage,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تفعيل وضع التركيز
  FocusMode activate({DateTime? startTime, int? duration}) {
    final now = startTime ?? DateTime.now();
    DateTime? endTime;
    
    if (duration != null) {
      endTime = now.add(Duration(minutes: duration));
    } else if (this.duration != null) {
      endTime = now.add(Duration(minutes: this.duration!));
    }

    return copyWith(
      isActive: true,
      startTime: now,
      endTime: endTime,
      updatedAt: DateTime.now(),
    );
  }

  /// إلغاء تفعيل وضع التركيز
  FocusMode deactivate() {
    return copyWith(
      isActive: false,
      startTime: null,
      endTime: null,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث البيانات
  FocusMode update({
    String? name,
    String? description,
    List<String>? blockedApps,
    List<String>? allowedApps,
    FocusModeType? type,
    int? duration,
    bool? blockNotifications,
    bool? allowCalls,
    bool? allowMessages,
    String? customMessage,
    Map<String, dynamic>? settings,
  }) {
    return copyWith(
      name: name,
      description: description,
      blockedApps: blockedApps,
      allowedApps: allowedApps,
      type: type,
      duration: duration,
      blockNotifications: blockNotifications,
      allowCalls: allowCalls,
      allowMessages: allowMessages,
      customMessage: customMessage,
      settings: settings,
      updatedAt: DateTime.now(),
    );
  }

  /// التحقق من انتهاء وضع التركيز
  bool get isExpired {
    if (!isActive || endTime == null) return false;
    return DateTime.now().isAfter(endTime!);
  }

  /// الحصول على الوقت المتبقي
  Duration? get remainingTime {
    if (!isActive || endTime == null) return null;
    final now = DateTime.now();
    if (now.isAfter(endTime!)) return Duration.zero;
    return endTime!.difference(now);
  }

  /// تنسيق الوقت المتبقي للعرض
  String get formattedRemainingTime {
    final remaining = remainingTime;
    if (remaining == null) return 'غير محدد';
    
    final hours = remaining.inHours;
    final minutes = remaining.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }

  /// التحقق من حجب تطبيق معين
  bool isAppBlocked(String packageName) {
    if (!isActive) return false;
    
    // إذا كان التطبيق في قائمة المسموح، فهو غير محجوب
    if (allowedApps.contains(packageName)) return false;
    
    // إذا كان التطبيق في قائمة المحجوب، فهو محجوب
    if (blockedApps.contains(packageName)) return true;
    
    // إذا لم يكن في أي قائمة، فهو غير محجوب افتراضياً
    return false;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FocusMode && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FocusMode(id: $id, name: $name, isActive: $isActive, type: ${type.displayName})';
  }
}

/// أنواع أوضاع التركيز
enum FocusModeType {
  work('work', 'العمل', 'للتركيز على المهام المهنية'),
  study('study', 'الدراسة', 'للتركيز على التعلم والدراسة'),
  sleep('sleep', 'النوم', 'لتحسين جودة النوم'),
  meditation('meditation', 'التأمل', 'للتأمل والاسترخاء'),
  exercise('exercise', 'الرياضة', 'للتركيز على التمارين'),
  custom('custom', 'مخصص', 'وضع مخصص حسب الحاجة');

  const FocusModeType(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;

  static FocusModeType fromValue(String value) {
    return FocusModeType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => FocusModeType.custom,
    );
  }
}
