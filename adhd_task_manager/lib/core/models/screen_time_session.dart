/// نموذج بيانات جلسة وقت الشاشة
class ScreenTimeSession {
  final int? id;
  final DateTime date;
  final DateTime startTime;
  final DateTime? endTime;
  final int? duration; // بالميلي ثانية
  final String sessionType; // 'active', 'idle', 'locked'
  final int? focusSessionId; // ربط مع جلسة Pomodoro
  final int? taskId; // ربط مع مهمة
  final Map<String, dynamic>? metadata; // بيانات إضافية
  final DateTime createdAt;
  final DateTime updatedAt;

  const ScreenTimeSession({
    this.id,
    required this.date,
    required this.startTime,
    this.endTime,
    this.duration,
    this.sessionType = 'active',
    this.focusSessionId,
    this.taskId,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Factory constructor لإنشاء جلسة جديدة
  factory ScreenTimeSession.create({
    required DateTime startTime,
    String sessionType = 'active',
    int? focusSessionId,
    int? taskId,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return ScreenTimeSession(
      date: DateTime(startTime.year, startTime.month, startTime.day),
      startTime: startTime,
      sessionType: sessionType,
      focusSessionId: focusSessionId,
      taskId: taskId,
      metadata: metadata,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// تحويل من JSON
  factory ScreenTimeSession.fromJson(Map<String, dynamic> json) {
    return ScreenTimeSession(
      id: json['id'] as int?,
      date: DateTime.parse(json['date'] as String),
      startTime: DateTime.parse(json['startTime'] as String),
      endTime:
          json['endTime'] != null
              ? DateTime.parse(json['endTime'] as String)
              : null,
      duration: json['duration'] as int?,
      sessionType: json['sessionType'] as String,
      focusSessionId: json['focusSessionId'] as int?,
      taskId: json['taskId'] as int?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'duration': duration,
      'sessionType': sessionType,
      'focusSessionId': focusSessionId,
      'taskId': taskId,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// تحويل من قاعدة البيانات
  factory ScreenTimeSession.fromDatabase(Map<String, dynamic> map) {
    return ScreenTimeSession(
      id: map['id'] as int?,
      date: DateTime.parse(map['date'] as String),
      startTime: DateTime.parse(map['startTime'] as String),
      endTime:
          map['endTime'] != null
              ? DateTime.parse(map['endTime'] as String)
              : null,
      duration: map['duration'] as int?,
      sessionType: map['sessionType'] as String,
      focusSessionId: map['focusSessionId'] as int?,
      taskId: map['taskId'] as int?,
      metadata:
          map['metadata'] != null
              ? Map<String, dynamic>.from(map['metadata'] as Map)
              : null,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'date': date.toIso8601String().split('T')[0], // تاريخ فقط
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'duration': duration,
      'sessionType': sessionType,
      'focusSessionId': focusSessionId,
      'taskId': taskId,
      'metadata': metadata != null ? metadata.toString() : null,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديلات
  ScreenTimeSession copyWith({
    int? id,
    DateTime? date,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
    String? sessionType,
    int? focusSessionId,
    int? taskId,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ScreenTimeSession(
      id: id ?? this.id,
      date: date ?? this.date,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      sessionType: sessionType ?? this.sessionType,
      focusSessionId: focusSessionId ?? this.focusSessionId,
      taskId: taskId ?? this.taskId,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// إنهاء الجلسة
  ScreenTimeSession endSession({DateTime? endTime}) {
    final actualEndTime = endTime ?? DateTime.now();
    final sessionDuration = actualEndTime.difference(startTime).inMilliseconds;

    return copyWith(
      endTime: actualEndTime,
      duration: sessionDuration,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث البيانات
  ScreenTimeSession update({
    String? sessionType,
    int? focusSessionId,
    int? taskId,
    Map<String, dynamic>? metadata,
  }) {
    return copyWith(
      sessionType: sessionType,
      focusSessionId: focusSessionId,
      taskId: taskId,
      metadata: metadata,
      updatedAt: DateTime.now(),
    );
  }

  /// الحصول على المدة بالدقائق
  double get durationInMinutes {
    if (duration != null) {
      return duration! / (1000 * 60);
    }
    if (endTime != null) {
      return endTime!.difference(startTime).inMilliseconds / (1000 * 60);
    }
    return 0;
  }

  /// الحصول على المدة بالساعات
  double get durationInHours {
    return durationInMinutes / 60;
  }

  /// تحديد إذا كانت الجلسة نشطة
  bool get isActive => endTime == null;

  /// تحديد إذا كانت الجلسة مرتبطة بالتركيز
  bool get isFocusSession => focusSessionId != null;

  /// تحديد إذا كانت الجلسة مرتبطة بمهمة
  bool get isTaskRelated => taskId != null;

  /// تنسيق المدة للعرض
  String get formattedDuration {
    final minutes = durationInMinutes.floor();
    final hours = (minutes / 60).floor();
    final remainingMinutes = minutes % 60;

    if (hours > 0) {
      return '${hours}س ${remainingMinutes}د';
    } else {
      return '${minutes}د';
    }
  }

  /// تحديد نوع الجلسة
  ScreenTimeSessionType get type =>
      ScreenTimeSessionType.fromValue(sessionType);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScreenTimeSession &&
        other.id == id &&
        other.startTime == startTime;
  }

  @override
  int get hashCode {
    return Object.hash(id, startTime);
  }

  @override
  String toString() {
    return 'ScreenTimeSession(id: $id, startTime: $startTime, '
        'duration: ${formattedDuration}, type: $sessionType)';
  }
}

/// أنواع جلسات وقت الشاشة
enum ScreenTimeSessionType {
  active('active', 'نشط'),
  idle('idle', 'خامل'),
  locked('locked', 'مقفل'),
  focus('focus', 'تركيز'),
  break_('break', 'استراحة');

  const ScreenTimeSessionType(this.value, this.displayName);

  final String value;
  final String displayName;

  static ScreenTimeSessionType fromValue(String value) {
    return ScreenTimeSessionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ScreenTimeSessionType.active,
    );
  }
}

/// إحصائيات وقت الشاشة اليومية
class DailyScreenTimeStats {
  final DateTime date;
  final int totalScreenTime; // بالميلي ثانية
  final int activeTime;
  final int idleTime;
  final int focusTime;
  final int sessionCount;
  final int unlockCount;
  final List<ScreenTimeSession> sessions;

  const DailyScreenTimeStats({
    required this.date,
    required this.totalScreenTime,
    required this.activeTime,
    required this.idleTime,
    required this.focusTime,
    required this.sessionCount,
    required this.unlockCount,
    required this.sessions,
  });

  /// إنشاء إحصائيات من قائمة الجلسات
  factory DailyScreenTimeStats.fromSessions(
    DateTime date,
    List<ScreenTimeSession> sessions,
  ) {
    int totalTime = 0;
    int activeTime = 0;
    int idleTime = 0;
    int focusTime = 0;
    int unlockCount = 0;

    for (final session in sessions) {
      final duration = session.duration ?? 0;
      totalTime += duration;

      switch (session.type) {
        case ScreenTimeSessionType.active:
          activeTime += duration;
          unlockCount++;
          break;
        case ScreenTimeSessionType.idle:
          idleTime += duration;
          break;
        case ScreenTimeSessionType.focus:
          focusTime += duration;
          break;
        default:
          break;
      }
    }

    return DailyScreenTimeStats(
      date: date,
      totalScreenTime: totalTime,
      activeTime: activeTime,
      idleTime: idleTime,
      focusTime: focusTime,
      sessionCount: sessions.length,
      unlockCount: unlockCount,
      sessions: sessions,
    );
  }

  /// الحصول على الوقت الإجمالي بالساعات
  double get totalHours => totalScreenTime / (1000 * 60 * 60);

  /// الحصول على الوقت النشط بالساعات
  double get activeHours => activeTime / (1000 * 60 * 60);

  /// الحصول على وقت التركيز بالساعات
  double get focusHours => focusTime / (1000 * 60 * 60);

  /// نسبة وقت التركيز من الوقت الإجمالي
  double get focusPercentage {
    if (totalScreenTime == 0) return 0;
    return (focusTime / totalScreenTime) * 100;
  }

  /// متوسط مدة الجلسة
  double get averageSessionDuration {
    if (sessionCount == 0) return 0;
    return totalScreenTime / sessionCount / (1000 * 60); // بالدقائق
  }
}
