import 'package:json_annotation/json_annotation.dart';

part 'task.g.dart';

@JsonSerializable()
class Task {
  final int? id;
  final String title;
  final String description;
  final TaskPriority priority;
  final bool isCompleted;
  final DateTime? dueDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Task({
    this.id,
    required this.title,
    required this.description,
    required this.priority,
    this.isCompleted = false,
    this.dueDate,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory constructor لإنشاء مهمة جديدة
  factory Task.create({
    required String title,
    required String description,
    required TaskPriority priority,
    DateTime? dueDate,
  }) {
    final now = DateTime.now();
    return Task(
      title: title,
      description: description,
      priority: priority,
      dueDate: dueDate,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Factory constructor من JSON
  factory Task.fromJson(Map<String, dynamic> json) => _$TaskFromJson(json);

  // تحويل إلى JSON
  Map<String, dynamic> toJson() => _$TaskToJson(this);

  // Factory constructor من قاعدة البيانات
  factory Task.fromDatabase(Map<String, dynamic> map) {
    return Task(
      id: map['id'] as int?,
      title: map['title'] as String,
      description: map['description'] as String,
      priority: TaskPriority.fromString(map['priority'] as String),
      isCompleted: (map['isCompleted'] as int) == 1,
      dueDate: map['dueDate'] != null 
          ? DateTime.parse(map['dueDate'] as String)
          : null,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  // تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'priority': priority.value,
      'isCompleted': isCompleted ? 1 : 0,
      'dueDate': dueDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // نسخ مع تعديلات
  Task copyWith({
    int? id,
    String? title,
    String? description,
    TaskPriority? priority,
    bool? isCompleted,
    DateTime? dueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      isCompleted: isCompleted ?? this.isCompleted,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // تحديث حالة الإكمال
  Task toggleCompleted() {
    return copyWith(
      isCompleted: !isCompleted,
      updatedAt: DateTime.now(),
    );
  }

  // تحديث المهمة
  Task update({
    String? title,
    String? description,
    TaskPriority? priority,
    DateTime? dueDate,
  }) {
    return copyWith(
      title: title,
      description: description,
      priority: priority,
      dueDate: dueDate,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Task &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.priority == priority &&
        other.isCompleted == isCompleted &&
        other.dueDate == dueDate;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      description,
      priority,
      isCompleted,
      dueDate,
    );
  }

  @override
  String toString() {
    return 'Task(id: $id, title: $title, priority: $priority, isCompleted: $isCompleted)';
  }
}

// تعداد أولويات المهام
enum TaskPriority {
  urgent('عاجل'),
  important('مهم'),
  normal('عادي');

  const TaskPriority(this.value);

  final String value;

  // الحصول على الأولوية من النص
  static TaskPriority fromString(String value) {
    switch (value) {
      case 'عاجل':
        return TaskPriority.urgent;
      case 'مهم':
        return TaskPriority.important;
      case 'عادي':
        return TaskPriority.normal;
      default:
        return TaskPriority.normal;
    }
  }

  // ترتيب الأولوية (أعلى رقم = أولوية أعلى)
  int get order {
    switch (this) {
      case TaskPriority.urgent:
        return 3;
      case TaskPriority.important:
        return 2;
      case TaskPriority.normal:
        return 1;
    }
  }
}
