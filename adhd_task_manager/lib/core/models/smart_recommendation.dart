/// نموذج بيانات التوصيات الذكية
class SmartRecommendation {
  final int? id;
  final String userId;
  final RecommendationType type;
  final String title;
  final String description;
  final String actionText;
  final String? actionData; // بيانات الإجراء المطلوب
  final RecommendationPriority priority;
  final double relevanceScore; // نقاط الصلة (0-1)
  final List<String> tags;
  final Map<String, dynamic>? metadata;
  final DateTime validUntil;
  final bool isRead;
  final bool isActioned;
  final DateTime? actionedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SmartRecommendation({
    this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.description,
    required this.actionText,
    this.actionData,
    this.priority = RecommendationPriority.medium,
    this.relevanceScore = 0.5,
    this.tags = const [],
    this.metadata,
    required this.validUntil,
    this.isRead = false,
    this.isActioned = false,
    this.actionedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Factory constructor لإنشاء توصية جديدة
  factory SmartRecommendation.create({
    required String userId,
    required RecommendationType type,
    required String title,
    required String description,
    required String actionText,
    String? actionData,
    RecommendationPriority priority = RecommendationPriority.medium,
    double relevanceScore = 0.5,
    List<String> tags = const [],
    Map<String, dynamic>? metadata,
    Duration validFor = const Duration(days: 7),
  }) {
    final now = DateTime.now();
    return SmartRecommendation(
      userId: userId,
      type: type,
      title: title,
      description: description,
      actionText: actionText,
      actionData: actionData,
      priority: priority,
      relevanceScore: relevanceScore,
      tags: tags,
      metadata: metadata,
      validUntil: now.add(validFor),
      createdAt: now,
      updatedAt: now,
    );
  }

  /// توصيات ذكية مُعرفة مسبقاً

  /// توصية تفعيل وضع التركيز
  static SmartRecommendation focusModeRecommendation({
    required String userId,
    required String focusModeName,
    required String reason,
    double relevanceScore = 0.8,
  }) {
    return SmartRecommendation.create(
      userId: userId,
      type: RecommendationType.focusMode,
      title: 'تفعيل وضع التركيز',
      description: 'نقترح تفعيل "$focusModeName" الآن. $reason',
      actionText: 'تفعيل الآن',
      actionData: focusModeName,
      priority: RecommendationPriority.high,
      relevanceScore: relevanceScore,
      tags: ['تركيز', 'إنتاجية'],
      metadata: {
        'focusMode': focusModeName,
        'reason': reason,
      },
    );
  }

  /// توصية أخذ استراحة
  static SmartRecommendation breakRecommendation({
    required String userId,
    required int workMinutes,
    required String breakType,
    double relevanceScore = 0.7,
  }) {
    return SmartRecommendation.create(
      userId: userId,
      type: RecommendationType.takeBreak,
      title: 'وقت الاستراحة',
      description: 'لقد عملت لمدة $workMinutes دقيقة. حان وقت $breakType.',
      actionText: 'بدء الاستراحة',
      actionData: breakType,
      priority: RecommendationPriority.medium,
      relevanceScore: relevanceScore,
      tags: ['استراحة', 'صحة'],
      metadata: {
        'workMinutes': workMinutes,
        'breakType': breakType,
      },
    );
  }

  /// توصية تقليل استخدام تطبيق
  static SmartRecommendation reduceAppUsage({
    required String userId,
    required String appName,
    required int dailyMinutes,
    required int recommendedLimit,
    double relevanceScore = 0.9,
  }) {
    return SmartRecommendation.create(
      userId: userId,
      type: RecommendationType.reduceUsage,
      title: 'تقليل استخدام $appName',
      description: 'استخدمت $appName لمدة $dailyMinutes دقيقة اليوم. نقترح تقليل الاستخدام إلى $recommendedLimit دقيقة.',
      actionText: 'تعيين حد زمني',
      actionData: appName,
      priority: RecommendationPriority.high,
      relevanceScore: relevanceScore,
      tags: ['تحكم', 'صحة رقمية'],
      metadata: {
        'appName': appName,
        'currentUsage': dailyMinutes,
        'recommendedLimit': recommendedLimit,
      },
    );
  }

  /// توصية إنشاء مهمة
  static SmartRecommendation createTaskRecommendation({
    required String userId,
    required String taskSuggestion,
    required String category,
    double relevanceScore = 0.6,
  }) {
    return SmartRecommendation.create(
      userId: userId,
      type: RecommendationType.createTask,
      title: 'إضافة مهمة جديدة',
      description: 'بناءً على نشاطك، نقترح إضافة: "$taskSuggestion"',
      actionText: 'إضافة المهمة',
      actionData: taskSuggestion,
      priority: RecommendationPriority.low,
      relevanceScore: relevanceScore,
      tags: ['مهام', 'تنظيم'],
      metadata: {
        'taskSuggestion': taskSuggestion,
        'category': category,
      },
    );
  }

  /// توصية تحسين النوم
  static SmartRecommendation sleepImprovementRecommendation({
    required String userId,
    required int nightUsageMinutes,
    double relevanceScore = 0.8,
  }) {
    return SmartRecommendation.create(
      userId: userId,
      type: RecommendationType.improveHabits,
      title: 'تحسين جودة النوم',
      description: 'استخدمت الهاتف لمدة $nightUsageMinutes دقيقة ليلاً. فعّل وضع النوم لتحسين راحتك.',
      actionText: 'تفعيل وضع النوم',
      actionData: 'sleep_mode',
      priority: RecommendationPriority.medium,
      relevanceScore: relevanceScore,
      tags: ['نوم', 'صحة'],
      metadata: {
        'nightUsage': nightUsageMinutes,
      },
    );
  }

  /// توصية تحدي شخصي
  static SmartRecommendation personalChallengeRecommendation({
    required String userId,
    required String challengeTitle,
    required String challengeDescription,
    required int durationDays,
    double relevanceScore = 0.7,
  }) {
    return SmartRecommendation.create(
      userId: userId,
      type: RecommendationType.personalChallenge,
      title: 'تحدي شخصي: $challengeTitle',
      description: challengeDescription,
      actionText: 'قبول التحدي',
      actionData: challengeTitle,
      priority: RecommendationPriority.medium,
      relevanceScore: relevanceScore,
      tags: ['تحدي', 'تحفيز'],
      metadata: {
        'challengeTitle': challengeTitle,
        'durationDays': durationDays,
      },
      validFor: Duration(days: durationDays),
    );
  }

  /// تحويل من قاعدة البيانات
  factory SmartRecommendation.fromDatabase(Map<String, dynamic> map) {
    return SmartRecommendation(
      id: map['id'] as int?,
      userId: map['userId'] as String,
      type: RecommendationType.fromValue(map['type'] as String),
      title: map['title'] as String,
      description: map['description'] as String,
      actionText: map['actionText'] as String,
      actionData: map['actionData'] as String?,
      priority: RecommendationPriority.fromValue(map['priority'] as String),
      relevanceScore: (map['relevanceScore'] as num).toDouble(),
      tags: (map['tags'] as String).split(',').where((tag) => tag.isNotEmpty).toList(),
      metadata: map['metadata'] != null ? Map<String, dynamic>.from(map['metadata'] as Map) : null,
      validUntil: DateTime.parse(map['validUntil'] as String),
      isRead: (map['isRead'] as int) == 1,
      isActioned: (map['isActioned'] as int) == 1,
      actionedAt: map['actionedAt'] != null ? DateTime.parse(map['actionedAt'] as String) : null,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// تحويل إلى قاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'userId': userId,
      'type': type.value,
      'title': title,
      'description': description,
      'actionText': actionText,
      'actionData': actionData,
      'priority': priority.value,
      'relevanceScore': relevanceScore,
      'tags': tags.join(','),
      'metadata': metadata?.toString(),
      'validUntil': validUntil.toIso8601String(),
      'isRead': isRead ? 1 : 0,
      'isActioned': isActioned ? 1 : 0,
      'actionedAt': actionedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديلات
  SmartRecommendation copyWith({
    int? id,
    String? userId,
    RecommendationType? type,
    String? title,
    String? description,
    String? actionText,
    String? actionData,
    RecommendationPriority? priority,
    double? relevanceScore,
    List<String>? tags,
    Map<String, dynamic>? metadata,
    DateTime? validUntil,
    bool? isRead,
    bool? isActioned,
    DateTime? actionedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SmartRecommendation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      actionText: actionText ?? this.actionText,
      actionData: actionData ?? this.actionData,
      priority: priority ?? this.priority,
      relevanceScore: relevanceScore ?? this.relevanceScore,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
      validUntil: validUntil ?? this.validUntil,
      isRead: isRead ?? this.isRead,
      isActioned: isActioned ?? this.isActioned,
      actionedAt: actionedAt ?? this.actionedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحديد التوصية كمقروءة
  SmartRecommendation markAsRead() {
    return copyWith(
      isRead: true,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديد التوصية كمنفذة
  SmartRecommendation markAsActioned() {
    return copyWith(
      isActioned: true,
      actionedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// التحقق من انتهاء صلاحية التوصية
  bool get isExpired => DateTime.now().isAfter(validUntil);

  /// التحقق من أهمية التوصية
  bool get isImportant => priority == RecommendationPriority.high && relevanceScore > 0.7;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmartRecommendation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SmartRecommendation(id: $id, title: $title, type: ${type.displayName})';
  }
}

/// أنواع التوصيات
enum RecommendationType {
  focusMode('focus_mode', 'وضع التركيز'),
  takeBreak('take_break', 'أخذ استراحة'),
  reduceUsage('reduce_usage', 'تقليل الاستخدام'),
  createTask('create_task', 'إنشاء مهمة'),
  improveHabits('improve_habits', 'تحسين العادات'),
  personalChallenge('personal_challenge', 'تحدي شخصي'),
  healthReminder('health_reminder', 'تذكير صحي'),
  productivityTip('productivity_tip', 'نصيحة إنتاجية');

  const RecommendationType(this.value, this.displayName);

  final String value;
  final String displayName;

  static RecommendationType fromValue(String value) {
    return RecommendationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => RecommendationType.productivityTip,
    );
  }
}

/// أولوية التوصية
enum RecommendationPriority {
  low('low', 'منخفضة'),
  medium('medium', 'متوسطة'),
  high('high', 'عالية'),
  urgent('urgent', 'عاجلة');

  const RecommendationPriority(this.value, this.displayName);

  final String value;
  final String displayName;

  static RecommendationPriority fromValue(String value) {
    return RecommendationPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => RecommendationPriority.medium,
    );
  }
}
