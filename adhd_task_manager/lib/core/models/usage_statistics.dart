import 'app_usage.dart';
import 'screen_time_session.dart';

/// إحصائيات الاستخدام الشاملة
class UsageStatistics {
  final DateTime date;
  final int totalScreenTime; // بالميلي ثانية
  final int productiveTime;
  final int distractingTime;
  final int focusTime;
  final int totalAppUsages;
  final int totalUnlocks;
  final List<AppUsage> topApps;
  final List<AppUsage> distractingApps;
  final List<AppUsage> productiveApps;
  final DailyScreenTimeStats? screenTimeStats;

  const UsageStatistics({
    required this.date,
    required this.totalScreenTime,
    required this.productiveTime,
    required this.distractingTime,
    required this.focusTime,
    required this.totalAppUsages,
    required this.totalUnlocks,
    required this.topApps,
    required this.distractingApps,
    required this.productiveApps,
    this.screenTimeStats,
  });

  /// إنشاء إحصائيات من بيانات الاستخدام
  factory UsageStatistics.fromData({
    required DateTime date,
    required List<AppUsage> appUsages,
    DailyScreenTimeStats? screenTimeStats,
  }) {
    // حساب الأوقات المختلفة
    int totalTime = 0;
    int productiveTime = 0;
    int distractingTime = 0;
    int totalLaunches = 0;

    for (final usage in appUsages) {
      totalTime += usage.totalTimeInForeground;
      totalLaunches += usage.launchCount;

      if (usage.isProductiveApp) {
        productiveTime += usage.totalTimeInForeground;
      } else if (usage.isDistractingApp) {
        distractingTime += usage.totalTimeInForeground;
      }
    }

    // ترتيب التطبيقات حسب الوقت
    final sortedApps = List<AppUsage>.from(appUsages)
      ..sort((a, b) => b.totalTimeInForeground.compareTo(a.totalTimeInForeground));

    // فلترة التطبيقات
    final distractingApps = appUsages
        .where((app) => app.isDistractingApp)
        .toList()
      ..sort((a, b) => b.totalTimeInForeground.compareTo(a.totalTimeInForeground));

    final productiveApps = appUsages
        .where((app) => app.isProductiveApp)
        .toList()
      ..sort((a, b) => b.totalTimeInForeground.compareTo(a.totalTimeInForeground));

    return UsageStatistics(
      date: date,
      totalScreenTime: totalTime,
      productiveTime: productiveTime,
      distractingTime: distractingTime,
      focusTime: screenTimeStats?.focusTime ?? 0,
      totalAppUsages: totalLaunches,
      totalUnlocks: screenTimeStats?.unlockCount ?? 0,
      topApps: sortedApps.take(10).toList(),
      distractingApps: distractingApps,
      productiveApps: productiveApps,
      screenTimeStats: screenTimeStats,
    );
  }

  /// الحصول على الوقت الإجمالي بالساعات
  double get totalHours => totalScreenTime / (1000 * 60 * 60);

  /// الحصول على الوقت المنتج بالساعات
  double get productiveHours => productiveTime / (1000 * 60 * 60);

  /// الحصول على الوقت المشتت بالساعات
  double get distractingHours => distractingTime / (1000 * 60 * 60);

  /// الحصول على وقت التركيز بالساعات
  double get focusHours => focusTime / (1000 * 60 * 60);

  /// نسبة الوقت المنتج
  double get productivePercentage {
    if (totalScreenTime == 0) return 0;
    return (productiveTime / totalScreenTime) * 100;
  }

  /// نسبة الوقت المشتت
  double get distractingPercentage {
    if (totalScreenTime == 0) return 0;
    return (distractingTime / totalScreenTime) * 100;
  }

  /// نسبة وقت التركيز
  double get focusPercentage {
    if (totalScreenTime == 0) return 0;
    return (focusTime / totalScreenTime) * 100;
  }

  /// متوسط الوقت لكل تطبيق
  double get averageTimePerApp {
    if (topApps.isEmpty) return 0;
    return totalScreenTime / topApps.length / (1000 * 60); // بالدقائق
  }

  /// متوسط عدد مرات الفتح لكل تطبيق
  double get averageLaunchesPerApp {
    if (topApps.isEmpty) return 0;
    return totalAppUsages / topApps.length;
  }

  /// تقييم جودة الاستخدام (من 0 إلى 100)
  int get usageQualityScore {
    double score = 0;

    // نقاط للوقت المنتج (40% من النقاط)
    score += (productivePercentage / 100) * 40;

    // نقاط لوقت التركيز (30% من النقاط)
    score += (focusPercentage / 100) * 30;

    // خصم نقاط للوقت المشتت (20% من النقاط)
    score += (1 - (distractingPercentage / 100)) * 20;

    // نقاط لقلة عدد مرات فتح الهاتف (10% من النقاط)
    final unlockPenalty = (totalUnlocks / 100).clamp(0.0, 1.0);
    score += (1 - unlockPenalty) * 10;

    return score.round().clamp(0, 100);
  }

  /// تحديد مستوى الاستخدام
  UsageLevel get usageLevel {
    if (totalHours < 2) return UsageLevel.low;
    if (totalHours < 4) return UsageLevel.moderate;
    if (totalHours < 6) return UsageLevel.high;
    return UsageLevel.excessive;
  }

  /// تحديد نوع المستخدم
  UserType get userType {
    if (productivePercentage > 60) return UserType.productive;
    if (distractingPercentage > 60) return UserType.distracted;
    if (focusPercentage > 30) return UserType.focused;
    return UserType.balanced;
  }

  /// الحصول على أهم التطبيقات المشتتة
  List<AppUsage> get topDistractingApps => distractingApps.take(5).toList();

  /// الحصول على أهم التطبيقات المنتجة
  List<AppUsage> get topProductiveApps => productiveApps.take(5).toList();

  /// تنسيق الوقت الإجمالي للعرض
  String get formattedTotalTime {
    final hours = totalHours.floor();
    final minutes = ((totalHours % 1) * 60).floor();
    
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }

  /// تحويل إلى JSON للحفظ
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'totalScreenTime': totalScreenTime,
      'productiveTime': productiveTime,
      'distractingTime': distractingTime,
      'focusTime': focusTime,
      'totalAppUsages': totalAppUsages,
      'totalUnlocks': totalUnlocks,
      'topApps': topApps.map((app) => app.toJson()).toList(),
      'distractingApps': distractingApps.map((app) => app.toJson()).toList(),
      'productiveApps': productiveApps.map((app) => app.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'UsageStatistics(date: $date, totalTime: ${formattedTotalTime}, '
        'quality: $usageQualityScore%, level: ${usageLevel.displayName})';
  }
}

/// مستويات الاستخدام
enum UsageLevel {
  low('low', 'منخفض', 'استخدام صحي ومتوازن'),
  moderate('moderate', 'متوسط', 'استخدام معقول مع إمكانية التحسين'),
  high('high', 'عالي', 'استخدام مرتفع يحتاج مراقبة'),
  excessive('excessive', 'مفرط', 'استخدام مفرط يحتاج تدخل فوري');

  const UsageLevel(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;
}

/// أنواع المستخدمين
enum UserType {
  productive('productive', 'منتج', 'يركز على التطبيقات المفيدة'),
  distracted('distracted', 'مشتت', 'يقضي وقتاً كثيراً في التطبيقات المشتتة'),
  focused('focused', 'مركز', 'يستخدم أدوات التركيز بفعالية'),
  balanced('balanced', 'متوازن', 'استخدام متوازن بين المختلف');

  const UserType(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;
}

/// إحصائيات أسبوعية
class WeeklyUsageStatistics {
  final DateTime weekStart;
  final DateTime weekEnd;
  final List<UsageStatistics> dailyStats;
  final double averageDailyScreenTime;
  final double totalWeeklyScreenTime;
  final int bestDay; // أفضل يوم (أقل استخدام مشتت)
  final int worstDay; // أسوأ يوم (أكثر استخدام مشتت)

  const WeeklyUsageStatistics({
    required this.weekStart,
    required this.weekEnd,
    required this.dailyStats,
    required this.averageDailyScreenTime,
    required this.totalWeeklyScreenTime,
    required this.bestDay,
    required this.worstDay,
  });

  /// إنشاء إحصائيات أسبوعية من الإحصائيات اليومية
  factory WeeklyUsageStatistics.fromDailyStats(List<UsageStatistics> dailyStats) {
    if (dailyStats.isEmpty) {
      throw ArgumentError('Daily stats cannot be empty');
    }

    dailyStats.sort((a, b) => a.date.compareTo(b.date));
    
    final weekStart = dailyStats.first.date;
    final weekEnd = dailyStats.last.date;
    
    double totalTime = 0;
    int bestDayIndex = 0;
    int worstDayIndex = 0;
    double bestScore = -1;
    double worstScore = 101;

    for (int i = 0; i < dailyStats.length; i++) {
      final stat = dailyStats[i];
      totalTime += stat.totalScreenTime;
      
      final score = stat.usageQualityScore.toDouble();
      if (score > bestScore) {
        bestScore = score;
        bestDayIndex = i;
      }
      if (score < worstScore) {
        worstScore = score;
        worstDayIndex = i;
      }
    }

    return WeeklyUsageStatistics(
      weekStart: weekStart,
      weekEnd: weekEnd,
      dailyStats: dailyStats,
      averageDailyScreenTime: totalTime / dailyStats.length,
      totalWeeklyScreenTime: totalTime,
      bestDay: bestDayIndex,
      worstDay: worstDayIndex,
    );
  }

  /// الحصول على الوقت الأسبوعي بالساعات
  double get totalWeeklyHours => totalWeeklyScreenTime / (1000 * 60 * 60);

  /// الحصول على متوسط الوقت اليومي بالساعات
  double get averageDailyHours => averageDailyScreenTime / (1000 * 60 * 60);

  /// متوسط نقاط جودة الاستخدام الأسبوعية
  double get averageQualityScore {
    if (dailyStats.isEmpty) return 0;
    return dailyStats.map((s) => s.usageQualityScore).reduce((a, b) => a + b) / dailyStats.length;
  }

  /// اتجاه التحسن (إيجابي = تحسن، سلبي = تراجع)
  double get improvementTrend {
    if (dailyStats.length < 2) return 0;
    
    final firstHalf = dailyStats.take(dailyStats.length ~/ 2);
    final secondHalf = dailyStats.skip(dailyStats.length ~/ 2);
    
    final firstAvg = firstHalf.map((s) => s.usageQualityScore).reduce((a, b) => a + b) / firstHalf.length;
    final secondAvg = secondHalf.map((s) => s.usageQualityScore).reduce((a, b) => a + b) / secondHalf.length;
    
    return secondAvg - firstAvg;
  }
}
