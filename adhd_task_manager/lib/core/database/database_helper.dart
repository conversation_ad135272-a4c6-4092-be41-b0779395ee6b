import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  // الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  // تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    try {
      // الحصول على مسار قاعدة البيانات
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, 'adhd_tasks.db');

      // فتح قاعدة البيانات
      return await openDatabase(
        path,
        version: 1,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );
    } catch (e) {
      throw Exception('فشل في تهيئة قاعدة البيانات: $e');
    }
  }

  // إنشاء جداول قاعدة البيانات
  Future<void> _createDatabase(Database db, int version) async {
    try {
      // جدول المهام
      await db.execute('''
        CREATE TABLE tasks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          priority TEXT NOT NULL CHECK (priority IN ('عاجل', 'مهم', 'عادي')),
          isCompleted INTEGER NOT NULL DEFAULT 0 CHECK (isCompleted IN (0, 1)),
          dueDate TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL
        )
      ''');

      // جدول التذكيرات
      await db.execute('''
        CREATE TABLE reminders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          taskId INTEGER NOT NULL,
          reminderDateTime TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('إشعار', 'صوتي', 'اهتزاز')),
          frequency TEXT NOT NULL CHECK (frequency IN ('مرة واحدة', 'يومياً', 'أسبوعياً', 'شهرياً')),
          isEnabled INTEGER NOT NULL DEFAULT 1 CHECK (isEnabled IN (0, 1)),
          customMessage TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          FOREIGN KEY (taskId) REFERENCES tasks (id) ON DELETE CASCADE
        )
      ''');

      // إنشاء فهارس لتحسين الأداء
      await db.execute('''
        CREATE INDEX idx_tasks_priority ON tasks(priority)
      ''');

      await db.execute('''
        CREATE INDEX idx_tasks_completed ON tasks(isCompleted)
      ''');

      await db.execute('''
        CREATE INDEX idx_tasks_due_date ON tasks(dueDate)
      ''');

      await db.execute('''
        CREATE INDEX idx_tasks_created_at ON tasks(createdAt)
      ''');

      await db.execute('''
        CREATE INDEX idx_reminders_task_id ON reminders(taskId)
      ''');

      await db.execute('''
        CREATE INDEX idx_reminders_datetime ON reminders(reminderDateTime)
      ''');

      await db.execute('''
        CREATE INDEX idx_reminders_enabled ON reminders(isEnabled)
      ''');

      print('تم إنشاء قاعدة البيانات بنجاح');
    } catch (e) {
      throw Exception('فشل في إنشاء جداول قاعدة البيانات: $e');
    }
  }

  // ترقية قاعدة البيانات
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    try {
      // في المستقبل، يمكن إضافة منطق الترقية هنا
      if (oldVersion < newVersion) {
        // مثال: إضافة أعمدة جديدة أو تعديل الجداول
        print('ترقية قاعدة البيانات من الإصدار $oldVersion إلى $newVersion');
      }
    } catch (e) {
      throw Exception('فشل في ترقية قاعدة البيانات: $e');
    }
  }

  // إدراج مهمة جديدة
  Future<int> insertTask(Map<String, dynamic> task) async {
    try {
      final db = await database;
      return await db.insert(
        'tasks',
        task,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في إدراج المهمة: $e');
    }
  }

  // الحصول على جميع المهام
  Future<List<Map<String, dynamic>>> getAllTasks() async {
    try {
      final db = await database;
      return await db.query(
        'tasks',
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في جلب المهام: $e');
    }
  }

  // الحصول على مهمة بالمعرف
  Future<Map<String, dynamic>?> getTaskById(int id) async {
    try {
      final db = await database;
      final results = await db.query(
        'tasks',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );
      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      throw Exception('فشل في جلب المهمة: $e');
    }
  }

  // الحصول على المهام حسب الأولوية
  Future<List<Map<String, dynamic>>> getTasksByPriority(String priority) async {
    try {
      final db = await database;
      return await db.query(
        'tasks',
        where: 'priority = ?',
        whereArgs: [priority],
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في جلب المهام حسب الأولوية: $e');
    }
  }

  // الحصول على المهام المكتملة/غير المكتملة
  Future<List<Map<String, dynamic>>> getTasksByCompletion(bool isCompleted) async {
    try {
      final db = await database;
      return await db.query(
        'tasks',
        where: 'isCompleted = ?',
        whereArgs: [isCompleted ? 1 : 0],
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في جلب المهام حسب حالة الإكمال: $e');
    }
  }

  // تحديث مهمة
  Future<int> updateTask(int id, Map<String, dynamic> task) async {
    try {
      final db = await database;
      return await db.update(
        'tasks',
        task,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في تحديث المهمة: $e');
    }
  }

  // حذف مهمة
  Future<int> deleteTask(int id) async {
    try {
      final db = await database;
      return await db.delete(
        'tasks',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في حذف المهمة: $e');
    }
  }

  // حذف جميع المهام
  Future<int> deleteAllTasks() async {
    try {
      final db = await database;
      return await db.delete('tasks');
    } catch (e) {
      throw Exception('فشل في حذف جميع المهام: $e');
    }
  }

  // الحصول على عدد المهام
  Future<int> getTasksCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM tasks');
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      throw Exception('فشل في حساب عدد المهام: $e');
    }
  }

  // الحصول على عدد المهام المكتملة
  Future<int> getCompletedTasksCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM tasks WHERE isCompleted = 1'
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      throw Exception('فشل في حساب عدد المهام المكتملة: $e');
    }
  }

  // البحث في المهام
  Future<List<Map<String, dynamic>>> searchTasks(String query) async {
    try {
      final db = await database;
      return await db.query(
        'tasks',
        where: 'title LIKE ? OR description LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في البحث في المهام: $e');
    }
  }

  // === دوال إدارة التذكيرات ===

  // إدراج تذكير جديد
  Future<int> insertReminder(Map<String, dynamic> reminder) async {
    try {
      final db = await database;
      return await db.insert(
        'reminders',
        reminder,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في إدراج التذكير: $e');
    }
  }

  // الحصول على جميع التذكيرات
  Future<List<Map<String, dynamic>>> getAllReminders() async {
    try {
      final db = await database;
      return await db.query(
        'reminders',
        orderBy: 'reminderDateTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات: $e');
    }
  }

  // الحصول على تذكيرات مهمة محددة
  Future<List<Map<String, dynamic>>> getRemindersByTaskId(int taskId) async {
    try {
      final db = await database;
      return await db.query(
        'reminders',
        where: 'taskId = ?',
        whereArgs: [taskId],
        orderBy: 'reminderDateTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب تذكيرات المهمة: $e');
    }
  }

  // الحصول على التذكيرات المفعلة
  Future<List<Map<String, dynamic>>> getEnabledReminders() async {
    try {
      final db = await database;
      return await db.query(
        'reminders',
        where: 'isEnabled = ?',
        whereArgs: [1],
        orderBy: 'reminderDateTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات المفعلة: $e');
    }
  }

  // تحديث تذكير
  Future<int> updateReminder(int id, Map<String, dynamic> reminder) async {
    try {
      final db = await database;
      return await db.update(
        'reminders',
        reminder,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في تحديث التذكير: $e');
    }
  }

  // حذف تذكير
  Future<int> deleteReminder(int id) async {
    try {
      final db = await database;
      return await db.delete(
        'reminders',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في حذف التذكير: $e');
    }
  }

  // حذف جميع تذكيرات مهمة
  Future<int> deleteRemindersByTaskId(int taskId) async {
    try {
      final db = await database;
      return await db.delete(
        'reminders',
        where: 'taskId = ?',
        whereArgs: [taskId],
      );
    } catch (e) {
      throw Exception('فشل في حذف تذكيرات المهمة: $e');
    }
  }

  // الحصول على عدد التذكيرات
  Future<int> getRemindersCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM reminders');
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      throw Exception('فشل في حساب عدد التذكيرات: $e');
    }
  }

  // الحصول على التذكيرات القادمة (خلال 24 ساعة)
  Future<List<Map<String, dynamic>>> getUpcomingReminders() async {
    try {
      final db = await database;
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));

      return await db.query(
        'reminders',
        where: 'isEnabled = ? AND reminderDateTime BETWEEN ? AND ?',
        whereArgs: [
          1,
          now.toIso8601String(),
          tomorrow.toIso8601String(),
        ],
        orderBy: 'reminderDateTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات القادمة: $e');
    }
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
