import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  // الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  // تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    try {
      // الحصول على مسار قاعدة البيانات
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, 'adhd_tasks.db');

      // فتح قاعدة البيانات
      return await openDatabase(
        path,
        version: 2,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );
    } catch (e) {
      throw Exception('فشل في تهيئة قاعدة البيانات: $e');
    }
  }

  // إنشاء جداول قاعدة البيانات
  Future<void> _createDatabase(Database db, int version) async {
    try {
      // جدول المهام
      await db.execute('''
        CREATE TABLE tasks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          priority TEXT NOT NULL CHECK (priority IN ('عاجل', 'مهم', 'عادي')),
          isCompleted INTEGER NOT NULL DEFAULT 0 CHECK (isCompleted IN (0, 1)),
          dueDate TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL
        )
      ''');

      // جدول التذكيرات
      await db.execute('''
        CREATE TABLE reminders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          taskId INTEGER NOT NULL,
          reminderDateTime TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('إشعار', 'صوتي', 'اهتزاز')),
          frequency TEXT NOT NULL CHECK (frequency IN ('مرة واحدة', 'يومياً', 'أسبوعياً', 'شهرياً')),
          isEnabled INTEGER NOT NULL DEFAULT 1 CHECK (isEnabled IN (0, 1)),
          customMessage TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          FOREIGN KEY (taskId) REFERENCES tasks (id) ON DELETE CASCADE
        )
      ''');

      // جدول جلسات التركيز
      await db.execute('''
        CREATE TABLE focus_sessions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          startTime TEXT NOT NULL,
          endTime TEXT,
          plannedDuration INTEGER NOT NULL,
          actualDuration INTEGER,
          type TEXT NOT NULL CHECK (type IN ('عمل', 'راحة قصيرة', 'راحة طويلة')),
          status TEXT NOT NULL CHECK (status IN ('نشطة', 'متوقفة', 'مكتملة', 'ملغية')),
          notes TEXT,
          taskId INTEGER,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          FOREIGN KEY (taskId) REFERENCES tasks (id) ON DELETE SET NULL
        )
      ''');

      // إنشاء فهارس لتحسين الأداء
      await db.execute('''
        CREATE INDEX idx_tasks_priority ON tasks(priority)
      ''');

      await db.execute('''
        CREATE INDEX idx_tasks_completed ON tasks(isCompleted)
      ''');

      await db.execute('''
        CREATE INDEX idx_tasks_due_date ON tasks(dueDate)
      ''');

      await db.execute('''
        CREATE INDEX idx_tasks_created_at ON tasks(createdAt)
      ''');

      await db.execute('''
        CREATE INDEX idx_reminders_task_id ON reminders(taskId)
      ''');

      await db.execute('''
        CREATE INDEX idx_reminders_datetime ON reminders(reminderDateTime)
      ''');

      await db.execute('''
        CREATE INDEX idx_reminders_enabled ON reminders(isEnabled)
      ''');

      await db.execute('''
        CREATE INDEX idx_focus_sessions_start_time ON focus_sessions(startTime)
      ''');

      await db.execute('''
        CREATE INDEX idx_focus_sessions_type ON focus_sessions(type)
      ''');

      await db.execute('''
        CREATE INDEX idx_focus_sessions_status ON focus_sessions(status)
      ''');

      await db.execute('''
        CREATE INDEX idx_focus_sessions_task_id ON focus_sessions(taskId)
      ''');

      // جدول استخدام التطبيقات
      await db.execute('''
        CREATE TABLE app_usage (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          packageName TEXT NOT NULL,
          appName TEXT NOT NULL,
          appIcon TEXT,
          date TEXT NOT NULL,
          totalTimeInForeground INTEGER NOT NULL DEFAULT 0,
          launchCount INTEGER NOT NULL DEFAULT 0,
          firstTimeStamp TEXT NOT NULL,
          lastTimeStamp TEXT NOT NULL,
          isSystemApp INTEGER NOT NULL DEFAULT 0 CHECK (isSystemApp IN (0, 1)),
          category TEXT NOT NULL DEFAULT 'other',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          UNIQUE(packageName, date)
        )
      ''');

      // جدول جلسات وقت الشاشة
      await db.execute('''
        CREATE TABLE screen_time_sessions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date TEXT NOT NULL,
          startTime TEXT NOT NULL,
          endTime TEXT,
          duration INTEGER,
          sessionType TEXT NOT NULL DEFAULT 'active',
          focusSessionId INTEGER,
          taskId INTEGER,
          metadata TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          FOREIGN KEY (focusSessionId) REFERENCES focus_sessions (id) ON DELETE SET NULL,
          FOREIGN KEY (taskId) REFERENCES tasks (id) ON DELETE SET NULL
        )
      ''');

      // فهارس لجدول استخدام التطبيقات
      await db.execute('''
        CREATE INDEX idx_app_usage_date ON app_usage(date)
      ''');

      await db.execute('''
        CREATE INDEX idx_app_usage_package_date ON app_usage(packageName, date)
      ''');

      await db.execute('''
        CREATE INDEX idx_app_usage_category ON app_usage(category)
      ''');

      await db.execute('''
        CREATE INDEX idx_app_usage_total_time ON app_usage(totalTimeInForeground)
      ''');

      // فهارس لجدول جلسات وقت الشاشة
      await db.execute('''
        CREATE INDEX idx_screen_time_date ON screen_time_sessions(date)
      ''');

      await db.execute('''
        CREATE INDEX idx_screen_time_start_time ON screen_time_sessions(startTime)
      ''');

      await db.execute('''
        CREATE INDEX idx_screen_time_session_type ON screen_time_sessions(sessionType)
      ''');

      await db.execute('''
        CREATE INDEX idx_screen_time_focus_session ON screen_time_sessions(focusSessionId)
      ''');

      await db.execute('''
        CREATE INDEX idx_screen_time_task ON screen_time_sessions(taskId)
      ''');

      print('تم إنشاء قاعدة البيانات بنجاح');
    } catch (e) {
      throw Exception('فشل في إنشاء جداول قاعدة البيانات: $e');
    }
  }

  // ترقية قاعدة البيانات
  Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    try {
      print('ترقية قاعدة البيانات من الإصدار $oldVersion إلى $newVersion');

      if (oldVersion < 2) {
        // إضافة جدول التذكيرات
        await db.execute('''
          CREATE TABLE IF NOT EXISTS reminders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            taskId INTEGER NOT NULL,
            reminderDateTime TEXT NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('إشعار', 'صوتي', 'اهتزاز')),
            frequency TEXT NOT NULL CHECK (frequency IN ('مرة واحدة', 'يومياً', 'أسبوعياً', 'شهرياً')),
            isEnabled INTEGER NOT NULL DEFAULT 1 CHECK (isEnabled IN (0, 1)),
            customMessage TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            FOREIGN KEY (taskId) REFERENCES tasks (id) ON DELETE CASCADE
          )
        ''');

        // إضافة جدول جلسات التركيز
        await db.execute('''
          CREATE TABLE IF NOT EXISTS focus_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            startTime TEXT NOT NULL,
            endTime TEXT,
            plannedDuration INTEGER NOT NULL,
            actualDuration INTEGER,
            type TEXT NOT NULL CHECK (type IN ('عمل', 'راحة قصيرة', 'راحة طويلة')),
            status TEXT NOT NULL CHECK (status IN ('نشطة', 'متوقفة', 'مكتملة', 'ملغية')),
            notes TEXT,
            taskId INTEGER,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            FOREIGN KEY (taskId) REFERENCES tasks (id) ON DELETE SET NULL
          )
        ''');

        // إضافة الفهارس
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_reminders_task_id ON reminders(taskId)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_reminders_datetime ON reminders(reminderDateTime)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_reminders_enabled ON reminders(isEnabled)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_focus_sessions_start_time ON focus_sessions(startTime)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_focus_sessions_type ON focus_sessions(type)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_focus_sessions_status ON focus_sessions(status)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_focus_sessions_task_id ON focus_sessions(taskId)',
        );

        print('تم إضافة جداول التذكيرات وجلسات التركيز بنجاح');
      }

      // إضافة جداول تتبع الاستخدام في الإصدار 3
      if (oldVersion < 3) {
        // جدول استخدام التطبيقات
        await db.execute('''
          CREATE TABLE IF NOT EXISTS app_usage (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            packageName TEXT NOT NULL,
            appName TEXT NOT NULL,
            appIcon TEXT,
            date TEXT NOT NULL,
            totalTimeInForeground INTEGER NOT NULL DEFAULT 0,
            launchCount INTEGER NOT NULL DEFAULT 0,
            firstTimeStamp TEXT NOT NULL,
            lastTimeStamp TEXT NOT NULL,
            isSystemApp INTEGER NOT NULL DEFAULT 0 CHECK (isSystemApp IN (0, 1)),
            category TEXT NOT NULL DEFAULT 'other',
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            UNIQUE(packageName, date)
          )
        ''');

        // جدول جلسات وقت الشاشة
        await db.execute('''
          CREATE TABLE IF NOT EXISTS screen_time_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            startTime TEXT NOT NULL,
            endTime TEXT,
            duration INTEGER,
            sessionType TEXT NOT NULL DEFAULT 'active',
            focusSessionId INTEGER,
            taskId INTEGER,
            metadata TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            FOREIGN KEY (focusSessionId) REFERENCES focus_sessions (id) ON DELETE SET NULL,
            FOREIGN KEY (taskId) REFERENCES tasks (id) ON DELETE SET NULL
          )
        ''');

        // فهارس لجدول استخدام التطبيقات
        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_app_usage_date ON app_usage(date)
        ''');

        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_app_usage_package_date ON app_usage(packageName, date)
        ''');

        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_app_usage_category ON app_usage(category)
        ''');

        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_app_usage_total_time ON app_usage(totalTimeInForeground)
        ''');

        // فهارس لجدول جلسات وقت الشاشة
        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_screen_time_date ON screen_time_sessions(date)
        ''');

        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_screen_time_start_time ON screen_time_sessions(startTime)
        ''');

        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_screen_time_session_type ON screen_time_sessions(sessionType)
        ''');

        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_screen_time_focus_session ON screen_time_sessions(focusSessionId)
        ''');

        await db.execute('''
          CREATE INDEX IF NOT EXISTS idx_screen_time_task ON screen_time_sessions(taskId)
        ''');

        print('تم إضافة جداول تتبع الاستخدام بنجاح');
      }
    } catch (e) {
      throw Exception('فشل في ترقية قاعدة البيانات: $e');
    }
  }

  // إدراج مهمة جديدة
  Future<int> insertTask(Map<String, dynamic> task) async {
    try {
      final db = await database;
      return await db.insert(
        'tasks',
        task,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في إدراج المهمة: $e');
    }
  }

  // الحصول على جميع المهام
  Future<List<Map<String, dynamic>>> getAllTasks() async {
    try {
      final db = await database;
      return await db.query('tasks', orderBy: 'createdAt DESC');
    } catch (e) {
      throw Exception('فشل في جلب المهام: $e');
    }
  }

  // الحصول على مهمة بالمعرف
  Future<Map<String, dynamic>?> getTaskById(int id) async {
    try {
      final db = await database;
      final results = await db.query(
        'tasks',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );
      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      throw Exception('فشل في جلب المهمة: $e');
    }
  }

  // الحصول على المهام حسب الأولوية
  Future<List<Map<String, dynamic>>> getTasksByPriority(String priority) async {
    try {
      final db = await database;
      return await db.query(
        'tasks',
        where: 'priority = ?',
        whereArgs: [priority],
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في جلب المهام حسب الأولوية: $e');
    }
  }

  // الحصول على المهام المكتملة/غير المكتملة
  Future<List<Map<String, dynamic>>> getTasksByCompletion(
    bool isCompleted,
  ) async {
    try {
      final db = await database;
      return await db.query(
        'tasks',
        where: 'isCompleted = ?',
        whereArgs: [isCompleted ? 1 : 0],
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في جلب المهام حسب حالة الإكمال: $e');
    }
  }

  // تحديث مهمة
  Future<int> updateTask(int id, Map<String, dynamic> task) async {
    try {
      final db = await database;
      return await db.update('tasks', task, where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      throw Exception('فشل في تحديث المهمة: $e');
    }
  }

  // حذف مهمة
  Future<int> deleteTask(int id) async {
    try {
      final db = await database;
      return await db.delete('tasks', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      throw Exception('فشل في حذف المهمة: $e');
    }
  }

  // حذف جميع المهام
  Future<int> deleteAllTasks() async {
    try {
      final db = await database;
      return await db.delete('tasks');
    } catch (e) {
      throw Exception('فشل في حذف جميع المهام: $e');
    }
  }

  // الحصول على عدد المهام
  Future<int> getTasksCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM tasks');
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      throw Exception('فشل في حساب عدد المهام: $e');
    }
  }

  // الحصول على عدد المهام المكتملة
  Future<int> getCompletedTasksCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM tasks WHERE isCompleted = 1',
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      throw Exception('فشل في حساب عدد المهام المكتملة: $e');
    }
  }

  // البحث في المهام
  Future<List<Map<String, dynamic>>> searchTasks(String query) async {
    try {
      final db = await database;
      return await db.query(
        'tasks',
        where: 'title LIKE ? OR description LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في البحث في المهام: $e');
    }
  }

  // === دوال إدارة التذكيرات ===

  // إدراج تذكير جديد
  Future<int> insertReminder(Map<String, dynamic> reminder) async {
    try {
      final db = await database;
      return await db.insert(
        'reminders',
        reminder,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في إدراج التذكير: $e');
    }
  }

  // الحصول على جميع التذكيرات
  Future<List<Map<String, dynamic>>> getAllReminders() async {
    try {
      final db = await database;
      return await db.query('reminders', orderBy: 'reminderDateTime ASC');
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات: $e');
    }
  }

  // الحصول على تذكيرات مهمة محددة
  Future<List<Map<String, dynamic>>> getRemindersByTaskId(int taskId) async {
    try {
      final db = await database;
      return await db.query(
        'reminders',
        where: 'taskId = ?',
        whereArgs: [taskId],
        orderBy: 'reminderDateTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب تذكيرات المهمة: $e');
    }
  }

  // الحصول على التذكيرات المفعلة
  Future<List<Map<String, dynamic>>> getEnabledReminders() async {
    try {
      final db = await database;
      return await db.query(
        'reminders',
        where: 'isEnabled = ?',
        whereArgs: [1],
        orderBy: 'reminderDateTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات المفعلة: $e');
    }
  }

  // تحديث تذكير
  Future<int> updateReminder(int id, Map<String, dynamic> reminder) async {
    try {
      final db = await database;
      return await db.update(
        'reminders',
        reminder,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في تحديث التذكير: $e');
    }
  }

  // حذف تذكير
  Future<int> deleteReminder(int id) async {
    try {
      final db = await database;
      return await db.delete('reminders', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      throw Exception('فشل في حذف التذكير: $e');
    }
  }

  // حذف جميع تذكيرات مهمة
  Future<int> deleteRemindersByTaskId(int taskId) async {
    try {
      final db = await database;
      return await db.delete(
        'reminders',
        where: 'taskId = ?',
        whereArgs: [taskId],
      );
    } catch (e) {
      throw Exception('فشل في حذف تذكيرات المهمة: $e');
    }
  }

  // الحصول على عدد التذكيرات
  Future<int> getRemindersCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM reminders',
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      throw Exception('فشل في حساب عدد التذكيرات: $e');
    }
  }

  // الحصول على التذكيرات القادمة (خلال 24 ساعة)
  Future<List<Map<String, dynamic>>> getUpcomingReminders() async {
    try {
      final db = await database;
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));

      return await db.query(
        'reminders',
        where: 'isEnabled = ? AND reminderDateTime BETWEEN ? AND ?',
        whereArgs: [1, now.toIso8601String(), tomorrow.toIso8601String()],
        orderBy: 'reminderDateTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات القادمة: $e');
    }
  }

  // === دوال إدارة جلسات التركيز ===

  // إدراج جلسة تركيز جديدة
  Future<int> insertFocusSession(Map<String, dynamic> session) async {
    try {
      final db = await database;
      return await db.insert(
        'focus_sessions',
        session,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في إدراج جلسة التركيز: $e');
    }
  }

  // الحصول على جميع جلسات التركيز
  Future<List<Map<String, dynamic>>> getAllFocusSessions() async {
    try {
      final db = await database;
      return await db.query('focus_sessions', orderBy: 'startTime DESC');
    } catch (e) {
      throw Exception('فشل في جلب جلسات التركيز: $e');
    }
  }

  // الحصول على جلسة تركيز بالمعرف
  Future<Map<String, dynamic>?> getFocusSessionById(int id) async {
    try {
      final db = await database;
      final results = await db.query(
        'focus_sessions',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );
      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      throw Exception('فشل في جلب جلسة التركيز: $e');
    }
  }

  // الحصول على الجلسة النشطة
  Future<Map<String, dynamic>?> getActiveFocusSession() async {
    try {
      final db = await database;
      final results = await db.query(
        'focus_sessions',
        where: 'status IN (?, ?)',
        whereArgs: ['نشطة', 'متوقفة'],
        orderBy: 'startTime DESC',
        limit: 1,
      );
      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      throw Exception('فشل في جلب الجلسة النشطة: $e');
    }
  }

  // تحديث جلسة تركيز
  Future<int> updateFocusSession(int id, Map<String, dynamic> session) async {
    try {
      final db = await database;
      return await db.update(
        'focus_sessions',
        session,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في تحديث جلسة التركيز: $e');
    }
  }

  // حذف جلسة تركيز
  Future<int> deleteFocusSession(int id) async {
    try {
      final db = await database;
      return await db.delete(
        'focus_sessions',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في حذف جلسة التركيز: $e');
    }
  }

  // الحصول على جلسات التركيز حسب التاريخ
  Future<List<Map<String, dynamic>>> getFocusSessionsByDate(
    DateTime date,
  ) async {
    try {
      final db = await database;
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      return await db.query(
        'focus_sessions',
        where: 'startTime >= ? AND startTime < ?',
        whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
        orderBy: 'startTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في جلب جلسات التركيز للتاريخ: $e');
    }
  }

  // الحصول على جلسات التركيز حسب النوع
  Future<List<Map<String, dynamic>>> getFocusSessionsByType(String type) async {
    try {
      final db = await database;
      return await db.query(
        'focus_sessions',
        where: 'type = ?',
        whereArgs: [type],
        orderBy: 'startTime DESC',
      );
    } catch (e) {
      throw Exception('فشل في جلب جلسات التركيز حسب النوع: $e');
    }
  }

  // الحصول على عدد جلسات التركيز
  Future<int> getFocusSessionsCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM focus_sessions',
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      throw Exception('فشل في حساب عدد جلسات التركيز: $e');
    }
  }

  // ==================== دوال استخدام التطبيقات ====================

  // إضافة أو تحديث استخدام تطبيق
  Future<int> insertOrUpdateAppUsage(Map<String, dynamic> appUsage) async {
    try {
      final db = await database;

      // محاولة التحديث أولاً
      final updateCount = await db.update(
        'app_usage',
        appUsage,
        where: 'packageName = ? AND date = ?',
        whereArgs: [appUsage['packageName'], appUsage['date']],
      );

      if (updateCount > 0) {
        // إذا تم التحديث، نحصل على ID الموجود
        final existing = await db.query(
          'app_usage',
          where: 'packageName = ? AND date = ?',
          whereArgs: [appUsage['packageName'], appUsage['date']],
          limit: 1,
        );
        return existing.first['id'] as int;
      } else {
        // إذا لم يتم التحديث، نقوم بالإدراج
        return await db.insert('app_usage', appUsage);
      }
    } catch (e) {
      throw Exception('فشل في إضافة/تحديث استخدام التطبيق: $e');
    }
  }

  // الحصول على استخدام التطبيقات لتاريخ محدد
  Future<List<Map<String, dynamic>>> getAppUsageByDate(DateTime date) async {
    try {
      final db = await database;
      final dateStr = date.toIso8601String().split('T')[0];

      return await db.query(
        'app_usage',
        where: 'date = ?',
        whereArgs: [dateStr],
        orderBy: 'totalTimeInForeground DESC',
      );
    } catch (e) {
      throw Exception('فشل في الحصول على استخدام التطبيقات: $e');
    }
  }

  // الحصول على استخدام التطبيقات لفترة محددة
  Future<List<Map<String, dynamic>>> getAppUsageByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await database;
      final startDateStr = startDate.toIso8601String().split('T')[0];
      final endDateStr = endDate.toIso8601String().split('T')[0];

      return await db.query(
        'app_usage',
        where: 'date BETWEEN ? AND ?',
        whereArgs: [startDateStr, endDateStr],
        orderBy: 'date DESC, totalTimeInForeground DESC',
      );
    } catch (e) {
      throw Exception('فشل في الحصول على استخدام التطبيقات للفترة: $e');
    }
  }

  // الحصول على أهم التطبيقات المستخدمة
  Future<List<Map<String, dynamic>>> getTopApps({
    DateTime? date,
    int limit = 10,
    String? category,
  }) async {
    try {
      final db = await database;
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (date != null) {
        whereClause = 'date = ?';
        whereArgs.add(date.toIso8601String().split('T')[0]);
      }

      if (category != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'category = ?';
        whereArgs.add(category);
      }

      return await db.query(
        'app_usage',
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'totalTimeInForeground DESC',
        limit: limit,
      );
    } catch (e) {
      throw Exception('فشل في الحصول على أهم التطبيقات: $e');
    }
  }

  // ==================== دوال جلسات وقت الشاشة ====================

  // إضافة جلسة وقت شاشة
  Future<int> insertScreenTimeSession(Map<String, dynamic> session) async {
    try {
      final db = await database;
      return await db.insert('screen_time_sessions', session);
    } catch (e) {
      throw Exception('فشل في إضافة جلسة وقت الشاشة: $e');
    }
  }

  // تحديث جلسة وقت الشاشة
  Future<int> updateScreenTimeSession(
    int id,
    Map<String, dynamic> session,
  ) async {
    try {
      final db = await database;
      return await db.update(
        'screen_time_sessions',
        session,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('فشل في تحديث جلسة وقت الشاشة: $e');
    }
  }

  // الحصول على جلسات وقت الشاشة لتاريخ محدد
  Future<List<Map<String, dynamic>>> getScreenTimeSessionsByDate(
    DateTime date,
  ) async {
    try {
      final db = await database;
      final dateStr = date.toIso8601String().split('T')[0];

      return await db.query(
        'screen_time_sessions',
        where: 'date = ?',
        whereArgs: [dateStr],
        orderBy: 'startTime ASC',
      );
    } catch (e) {
      throw Exception('فشل في الحصول على جلسات وقت الشاشة: $e');
    }
  }

  // حذف بيانات استخدام التطبيقات القديمة
  Future<int> deleteOldAppUsage(int daysToKeep) async {
    try {
      final db = await database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      final cutoffDateStr = cutoffDate.toIso8601String().split('T')[0];

      return await db.delete(
        'app_usage',
        where: 'date < ?',
        whereArgs: [cutoffDateStr],
      );
    } catch (e) {
      throw Exception('فشل في حذف بيانات الاستخدام القديمة: $e');
    }
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
