import 'dart:async';
import 'dart:math';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../database/database_helper.dart';
import '../models/smart_notification.dart';
import '../models/usage_statistics.dart';
import '../models/focus_session.dart';

/// خدمة التنبيهات الذكية المتقدمة
class SmartNotificationService {
  static final SmartNotificationService _instance =
      SmartNotificationService._internal();
  factory SmartNotificationService() => _instance;
  SmartNotificationService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Timer? _notificationTimer;
  bool _isInitialized = false;

  // إعدادات التنبيهات الذكية
  bool _breakRemindersEnabled = true;
  bool _usageWarningsEnabled = true;
  bool _goalNotificationsEnabled = true;
  bool _focusSuggestionsEnabled = true;

  // حدود الاستخدام للتنبيهات
  static const int _maxDailyUsageMinutes = 240; // 4 ساعات
  static const int _maxAppUsageMinutes = 60; // ساعة واحدة لكل تطبيق
  static const int _maxUnlocksPerDay = 100;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة إعدادات التنبيهات
      const androidSettings = AndroidInitializationSettings(
        '@mipmap/ic_launcher',
      );
      const iosSettings = DarwinInitializationSettings();
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _notificationsPlugin.initialize(initSettings);
      _startNotificationMonitoring();
      _isInitialized = true;
    } catch (e) {
      throw Exception('فشل في تهيئة خدمة التنبيهات الذكية: $e');
    }
  }

  /// بدء مراقبة التنبيهات
  void _startNotificationMonitoring() {
    // فحص التنبيهات المجدولة كل دقيقة
    _notificationTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _processScheduledNotifications(),
    );
  }

  /// معالجة التنبيهات المجدولة
  Future<void> _processScheduledNotifications() async {
    try {
      final scheduledNotifications =
          await _databaseHelper.getScheduledNotifications();

      for (final notificationData in scheduledNotifications) {
        final notification = SmartNotification.fromDatabase(notificationData);
        await _deliverNotification(notification);
      }
    } catch (e) {
      print('خطأ في معالجة التنبيهات المجدولة: $e');
    }
  }

  /// تسليم التنبيه
  Future<void> _deliverNotification(SmartNotification notification) async {
    try {
      // إعداد التنبيه
      const androidDetails = AndroidNotificationDetails(
        'smart_notifications',
        'التنبيهات الذكية',
        channelDescription: 'تنبيهات ذكية لتحسين الإنتاجية',
        importance: Importance.high,
        priority: Priority.high,
      );

      const iosDetails = DarwinNotificationDetails();
      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // إرسال التنبيه
      await _notificationsPlugin.show(
        notification.id ?? Random().nextInt(100000),
        notification.title,
        notification.message,
        notificationDetails,
        payload: notification.actionType,
      );

      // تحديث حالة التنبيه في قاعدة البيانات
      final deliveredNotification = notification.markAsDelivered();
      await _databaseHelper.updateSmartNotification(
        notification.id!,
        deliveredNotification.toDatabase(),
      );
    } catch (e) {
      print('فشل في تسليم التنبيه: $e');
    }
  }

  /// جدولة تنبيه ذكي
  Future<void> scheduleNotification(SmartNotification notification) async {
    try {
      final id = await _databaseHelper.insertSmartNotification(
        notification.toDatabase(),
      );

      // إذا كان التنبيه مجدولاً للآن أو الماضي، أرسله فوراً
      if (notification.scheduledTime.isBefore(
        DateTime.now().add(const Duration(minutes: 1)),
      )) {
        await _deliverNotification(notification.copyWith(id: id));
      }
    } catch (e) {
      throw Exception('فشل في جدولة التنبيه: $e');
    }
  }

  /// تحليل أنماط الاستخدام وإرسال تنبيهات ذكية
  Future<void> analyzeUsageAndNotify(UsageStatistics stats) async {
    if (!_usageWarningsEnabled) return;

    try {
      // تحذير الاستخدام المفرط اليومي
      if (stats.totalHours > _maxDailyUsageMinutes / 60) {
        await scheduleNotification(
          SmartNotification.excessiveUsageWarning(
            appName: 'الهاتف',
            usageTime: Duration(milliseconds: stats.totalScreenTime),
            dailyLimit: _maxDailyUsageMinutes.toDouble(),
          ),
        );
      }

      // تحذير التطبيقات المشتتة
      if (stats.distractingPercentage > 60) {
        await scheduleNotification(
          SmartNotification.focusModesuggestion(
            reason:
                'قضيت ${stats.distractingPercentage.toStringAsFixed(0)}% من وقتك في تطبيقات مشتتة.',
            suggestedMode: 'وضع العمل',
          ),
        );
      }

      // تحذير التطبيقات الفردية
      for (final app in stats.topApps.take(3)) {
        if (app.totalTimeInMinutes > _maxAppUsageMinutes) {
          await scheduleNotification(
            SmartNotification.excessiveUsageWarning(
              appName: app.appName,
              usageTime: Duration(minutes: app.totalTimeInMinutes.round()),
              dailyLimit: _maxAppUsageMinutes.toDouble(),
            ),
          );
        }
      }

      // تحذير مرات فتح الهاتف المفرطة
      if (stats.totalUnlocks > _maxUnlocksPerDay) {
        await scheduleNotification(
          SmartNotification.create(
            title: 'تحذير: فتح مفرط للهاتف 📱',
            message:
                'فتحت الهاتف ${stats.totalUnlocks} مرة اليوم. حاول تقليل التشتت.',
            type: SmartNotificationType.usageWarning,
            scheduledTime: DateTime.now(),
            actionType: 'enable_focus_mode',
          ),
        );
      }
    } catch (e) {
      print('خطأ في تحليل الاستخدام: $e');
    }
  }

  /// تنبيهات جلسات Pomodoro
  Future<void> handlePomodoroSession(FocusSession session) async {
    if (!_breakRemindersEnabled) return;

    try {
      // التحقق من انتهاء الجلسة
      final now = DateTime.now();
      final sessionEnd = session.startTime.add(
        Duration(minutes: session.plannedDuration),
      );

      if (now.isAfter(sessionEnd)) {
        // تنبيه إكمال جلسة
        await scheduleNotification(
          SmartNotification.create(
            title: 'أحسنت! 🎉',
            message: 'أكملت جلسة تركيز لمدة ${session.plannedDuration} دقيقة.',
            type: SmartNotificationType.goalAchievement,
            scheduledTime: DateTime.now(),
            data: {
              'sessionId': session.id,
              'duration': session.plannedDuration,
              'type': session.type.value,
            },
          ),
        );

        // اقتراح استراحة بناءً على نوع الجلسة
        if (session.type == FocusSessionType.work) {
          final breakDuration = session.plannedDuration >= 25 ? 5 : 3;
          await scheduleNotification(
            SmartNotification.breakReminder(
              workDuration: Duration(minutes: session.plannedDuration),
              sessionsCompleted: 1,
            ),
          );
        }
      }
    } catch (e) {
      print('خطأ في معالجة جلسة Pomodoro: $e');
    }
  }

  /// تنبيهات الأهداف والإنجازات
  Future<void> notifyGoalAchievement({
    required String goalName,
    required String goalType,
    required double progress,
  }) async {
    if (!_goalNotificationsEnabled) return;

    try {
      await scheduleNotification(
        SmartNotification.goalAchievement(
          goalName: goalName,
          goalType: goalType,
          progress: progress,
        ),
      );
    } catch (e) {
      print('خطأ في إرسال تنبيه الإنجاز: $e');
    }
  }

  /// تنبيهات تحفيزية ذكية
  Future<void> sendMotivationalNotification() async {
    try {
      final motivationalMessages = [
        'أنت تقوم بعمل رائع! استمر في التركيز. 💪',
        'كل دقيقة تركيز تقربك من أهدافك. 🎯',
        'الإنتاجية تبدأ بخطوة واحدة. ابدأ الآن! ⚡',
        'تذكر: التقدم أهم من الكمال. 🌟',
        'أنت أقوى من أي تشتت. ركز! 🧘',
      ];

      final randomMessage =
          motivationalMessages[Random().nextInt(motivationalMessages.length)];

      await scheduleNotification(
        SmartNotification.create(
          title: 'رسالة تحفيزية',
          message: randomMessage,
          type: SmartNotificationType.motivational,
          scheduledTime: DateTime.now(),
        ),
      );
    } catch (e) {
      print('خطأ في إرسال التنبيه التحفيزي: $e');
    }
  }

  /// جدولة تنبيهات دورية ذكية
  Future<void> scheduleSmartReminders() async {
    try {
      final now = DateTime.now();

      // تنبيه صباحي لبدء اليوم
      if (now.hour == 8 && now.minute == 0) {
        await scheduleNotification(
          SmartNotification.create(
            title: 'صباح الخير! ☀️',
            message: 'ابدأ يومك بتحديد أهدافك وتفعيل وضع التركيز.',
            type: SmartNotificationType.motivational,
            scheduledTime: now,
            actionType: 'view_tasks',
          ),
        );
      }

      // تنبيه مسائي لمراجعة اليوم
      if (now.hour == 20 && now.minute == 0) {
        await scheduleNotification(
          SmartNotification.create(
            title: 'مراجعة اليوم 📊',
            message: 'راجع إنجازاتك اليوم وخطط للغد.',
            type: SmartNotificationType.motivational,
            scheduledTime: now,
            actionType: 'view_statistics',
          ),
        );
      }
    } catch (e) {
      print('خطأ في جدولة التنبيهات الدورية: $e');
    }
  }

  /// الحصول على التنبيهات غير المقروءة
  Future<List<SmartNotification>> getUnreadNotifications() async {
    try {
      final notificationsData = await _databaseHelper.getUnreadNotifications();
      return notificationsData
          .map((data) => SmartNotification.fromDatabase(data))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على التنبيهات غير المقروءة: $e');
    }
  }

  /// تحديد التنبيه كمقروء
  Future<void> markAsRead(int notificationId) async {
    try {
      final notificationData = await _databaseHelper.getUnreadNotifications();
      final notification = notificationData
          .map((data) => SmartNotification.fromDatabase(data))
          .firstWhere((n) => n.id == notificationId);

      final readNotification = notification.markAsRead();
      await _databaseHelper.updateSmartNotification(
        notificationId,
        readNotification.toDatabase(),
      );
    } catch (e) {
      throw Exception('فشل في تحديد التنبيه كمقروء: $e');
    }
  }

  /// تنظيف التنبيهات القديمة
  Future<void> cleanupOldNotifications({int daysToKeep = 7}) async {
    try {
      await _databaseHelper.deleteOldNotifications(daysToKeep);
    } catch (e) {
      print('خطأ في تنظيف التنبيهات القديمة: $e');
    }
  }

  /// تحديث إعدادات التنبيهات
  void updateSettings({
    bool? breakReminders,
    bool? usageWarnings,
    bool? goalNotifications,
    bool? focusSuggestions,
  }) {
    _breakRemindersEnabled = breakReminders ?? _breakRemindersEnabled;
    _usageWarningsEnabled = usageWarnings ?? _usageWarningsEnabled;
    _goalNotificationsEnabled = goalNotifications ?? _goalNotificationsEnabled;
    _focusSuggestionsEnabled = focusSuggestions ?? _focusSuggestionsEnabled;
  }

  /// الحصول على إعدادات التنبيهات الحالية
  Map<String, bool> get currentSettings => {
    'breakReminders': _breakRemindersEnabled,
    'usageWarnings': _usageWarningsEnabled,
    'goalNotifications': _goalNotificationsEnabled,
    'focusSuggestions': _focusSuggestionsEnabled,
  };

  /// تنظيف الموارد
  void dispose() {
    _notificationTimer?.cancel();
  }
}
