import '../database/database_helper.dart';
import '../models/reminder.dart';
import '../models/task.dart';
import 'notification_service.dart';

class ReminderService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final NotificationService _notificationService = NotificationService.instance;

  // إضافة تذكير جديد
  Future<Reminder> addReminder({
    required int taskId,
    required DateTime reminderDateTime,
    required ReminderType type,
    ReminderFrequency frequency = ReminderFrequency.once,
    String? customMessage,
  }) async {
    try {
      // التحقق من أن التاريخ في المستقبل
      if (reminderDateTime.isBefore(DateTime.now())) {
        throw Exception('لا يمكن إضافة تذكير في الماضي');
      }

      final reminder = Reminder.create(
        taskId: taskId,
        reminderDateTime: reminderDateTime,
        type: type,
        frequency: frequency,
        customMessage: customMessage,
      );

      final id = await _databaseHelper.insertReminder(reminder.toDatabase());
      final savedReminder = reminder.copyWith(id: id);

      // جدولة التنبيه
      await _scheduleNotification(savedReminder);

      return savedReminder;
    } catch (e) {
      throw Exception('فشل في إضافة التذكير: $e');
    }
  }

  // الحصول على جميع التذكيرات
  Future<List<Reminder>> getAllReminders() async {
    try {
      final remindersData = await _databaseHelper.getAllReminders();
      return remindersData.map((data) => Reminder.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات: $e');
    }
  }

  // الحصول على تذكيرات مهمة محددة
  Future<List<Reminder>> getRemindersByTaskId(int taskId) async {
    try {
      final remindersData = await _databaseHelper.getRemindersByTaskId(taskId);
      return remindersData.map((data) => Reminder.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب تذكيرات المهمة: $e');
    }
  }

  // الحصول على التذكيرات المفعلة
  Future<List<Reminder>> getEnabledReminders() async {
    try {
      final remindersData = await _databaseHelper.getEnabledReminders();
      return remindersData.map((data) => Reminder.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات المفعلة: $e');
    }
  }

  // تحديث تذكير
  Future<Reminder> updateReminder(Reminder reminder) async {
    try {
      if (reminder.id == null) {
        throw Exception('معرف التذكير مطلوب للتحديث');
      }

      final updatedReminder = reminder.copyWith(updatedAt: DateTime.now());
      final rowsAffected = await _databaseHelper.updateReminder(
        reminder.id!,
        updatedReminder.toDatabase(),
      );

      if (rowsAffected == 0) {
        throw Exception('التذكير غير موجود');
      }

      // إعادة جدولة التنبيه
      await _rescheduleNotification(updatedReminder);

      return updatedReminder;
    } catch (e) {
      throw Exception('فشل في تحديث التذكير: $e');
    }
  }

  // تبديل حالة تفعيل التذكير
  Future<Reminder> toggleReminderEnabled(Reminder reminder) async {
    try {
      if (reminder.id == null) {
        throw Exception('معرف التذكير مطلوب');
      }

      final updatedReminder = reminder.toggleEnabled();
      final rowsAffected = await _databaseHelper.updateReminder(
        reminder.id!,
        updatedReminder.toDatabase(),
      );

      if (rowsAffected == 0) {
        throw Exception('التذكير غير موجود');
      }

      // إدارة التنبيه حسب الحالة
      if (updatedReminder.isEnabled) {
        await _scheduleNotification(updatedReminder);
      } else {
        await _cancelNotification(updatedReminder);
      }

      return updatedReminder;
    } catch (e) {
      throw Exception('فشل في تحديث حالة التذكير: $e');
    }
  }

  // حذف تذكير
  Future<bool> deleteReminder(int id) async {
    try {
      // الحصول على التذكير أولاً لإلغاء التنبيه
      final remindersData = await _databaseHelper.getAllReminders();
      final reminder = remindersData
          .where((data) => data['id'] == id)
          .map((data) => Reminder.fromDatabase(data))
          .firstOrNull;

      if (reminder != null) {
        await _cancelNotification(reminder);
      }

      final rowsAffected = await _databaseHelper.deleteReminder(id);
      return rowsAffected > 0;
    } catch (e) {
      throw Exception('فشل في حذف التذكير: $e');
    }
  }

  // حذف جميع تذكيرات مهمة
  Future<int> deleteRemindersByTaskId(int taskId) async {
    try {
      // إلغاء جميع التنبيهات للمهمة
      final reminders = await getRemindersByTaskId(taskId);
      for (final reminder in reminders) {
        await _cancelNotification(reminder);
      }

      return await _databaseHelper.deleteRemindersByTaskId(taskId);
    } catch (e) {
      throw Exception('فشل في حذف تذكيرات المهمة: $e');
    }
  }

  // الحصول على التذكيرات القادمة
  Future<List<Reminder>> getUpcomingReminders() async {
    try {
      final remindersData = await _databaseHelper.getUpcomingReminders();
      return remindersData.map((data) => Reminder.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات القادمة: $e');
    }
  }

  // إضافة تذكير سريع لمهمة
  Future<Reminder> addQuickReminder({
    required Task task,
    required Duration duration,
    ReminderType type = ReminderType.notification,
  }) async {
    final reminderDateTime = DateTime.now().add(duration);
    return await addReminder(
      taskId: task.id!,
      reminderDateTime: reminderDateTime,
      type: type,
      customMessage: 'تذكير سريع: ${task.title}',
    );
  }

  // جدولة التنبيه
  Future<void> _scheduleNotification(Reminder reminder) async {
    try {
      // الحصول على معلومات المهمة
      final taskData = await _databaseHelper.getTaskById(reminder.taskId);
      if (taskData == null) {
        throw Exception('المهمة غير موجودة');
      }

      final task = Task.fromDatabase(taskData);
      
      await _notificationService.scheduleTaskReminder(
        task: task,
        reminderDateTime: reminder.reminderDateTime,
        type: reminder.type,
        frequency: reminder.frequency,
        customMessage: reminder.customMessage,
      );
    } catch (e) {
      print('خطأ في جدولة التنبيه: $e');
    }
  }

  // إعادة جدولة التنبيه
  Future<void> _rescheduleNotification(Reminder reminder) async {
    await _cancelNotification(reminder);
    if (reminder.isEnabled) {
      await _scheduleNotification(reminder);
    }
  }

  // إلغاء التنبيه
  Future<void> _cancelNotification(Reminder reminder) async {
    try {
      await _notificationService.cancelReminder(
        reminder.taskId,
        reminder.reminderDateTime,
      );
    } catch (e) {
      print('خطأ في إلغاء التنبيه: $e');
    }
  }

  // إحصائيات التذكيرات
  Future<ReminderStatistics> getReminderStatistics() async {
    try {
      final allReminders = await getAllReminders();
      final enabledReminders = allReminders.where((r) => r.isEnabled).toList();
      final upcomingReminders = await getUpcomingReminders();

      return ReminderStatistics(
        totalReminders: allReminders.length,
        enabledReminders: enabledReminders.length,
        upcomingReminders: upcomingReminders.length,
        remindersByType: _groupRemindersByType(allReminders),
        remindersByFrequency: _groupRemindersByFrequency(allReminders),
      );
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات التذكيرات: $e');
    }
  }

  // تجميع التذكيرات حسب النوع
  Map<ReminderType, int> _groupRemindersByType(List<Reminder> reminders) {
    final Map<ReminderType, int> result = {};
    for (final type in ReminderType.values) {
      result[type] = reminders.where((r) => r.type == type).length;
    }
    return result;
  }

  // تجميع التذكيرات حسب التكرار
  Map<ReminderFrequency, int> _groupRemindersByFrequency(List<Reminder> reminders) {
    final Map<ReminderFrequency, int> result = {};
    for (final frequency in ReminderFrequency.values) {
      result[frequency] = reminders.where((r) => r.frequency == frequency).length;
    }
    return result;
  }
}

// فئة إحصائيات التذكيرات
class ReminderStatistics {
  final int totalReminders;
  final int enabledReminders;
  final int upcomingReminders;
  final Map<ReminderType, int> remindersByType;
  final Map<ReminderFrequency, int> remindersByFrequency;

  const ReminderStatistics({
    required this.totalReminders,
    required this.enabledReminders,
    required this.upcomingReminders,
    required this.remindersByType,
    required this.remindersByFrequency,
  });

  // نسبة التذكيرات المفعلة
  double get enabledPercentage {
    if (totalReminders == 0) return 0.0;
    return enabledReminders / totalReminders;
  }
}
