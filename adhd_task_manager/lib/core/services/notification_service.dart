import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../models/reminder.dart';
import '../models/task.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  static NotificationService get instance => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  // إعدادات التنبيهات
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _notificationsEnabled = true;

  // Getters للإعدادات
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  bool get notificationsEnabled => _notificationsEnabled;

  // تهيئة خدمة التنبيهات
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // تهيئة المناطق الزمنية
      tz.initializeTimeZones();

      // إعدادات Android
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // إعدادات التهيئة
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // تهيئة المكون الإضافي
      final initialized = await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (initialized == true) {
        _isInitialized = true;
        
        // طلب الأذونات
        await _requestPermissions();
        
        print('تم تهيئة خدمة التنبيهات بنجاح');
        return true;
      }
      
      return false;
    } catch (e) {
      print('خطأ في تهيئة خدمة التنبيهات: $e');
      return false;
    }
  }

  // طلب الأذونات المطلوبة
  Future<bool> _requestPermissions() async {
    try {
      if (Platform.isAndroid) {
        // طلب إذن التنبيهات لـ Android 13+
        final notificationPermission = await Permission.notification.request();
        
        // طلب إذن التنبيهات الدقيقة
        if (await Permission.scheduleExactAlarm.isDenied) {
          await Permission.scheduleExactAlarm.request();
        }
        
        return notificationPermission.isGranted;
      } else if (Platform.isIOS) {
        // طلب الأذونات لـ iOS
        final result = await _notifications
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
        
        return result ?? false;
      }
      
      return true;
    } catch (e) {
      print('خطأ في طلب الأذونات: $e');
      return false;
    }
  }

  // معالج النقر على التنبيه
  void _onNotificationTapped(NotificationResponse response) {
    print('تم النقر على التنبيه: ${response.payload}');
    // TODO: التنقل إلى الشاشة المناسبة
  }

  // جدولة تذكير لمهمة
  Future<bool> scheduleTaskReminder({
    required Task task,
    required DateTime reminderDateTime,
    required ReminderType type,
    ReminderFrequency frequency = ReminderFrequency.once,
    String? customMessage,
  }) async {
    if (!_isInitialized || !_notificationsEnabled) {
      return false;
    }

    try {
      final notificationId = _generateNotificationId(task.id!, reminderDateTime);
      final title = 'تذكير: ${task.title}';
      final body = customMessage ?? (task.description.isNotEmpty
          ? task.description
          : 'حان وقت العمل على هذه المهمة');

      final notificationDetails = _buildNotificationDetails(type);
      final scheduledDate = tz.TZDateTime.from(reminderDateTime, tz.local);

      if (frequency == ReminderFrequency.once) {
        // تذكير لمرة واحدة
        await _notifications.zonedSchedule(
          notificationId,
          title,
          body,
          scheduledDate,
          notificationDetails,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
          payload: 'task_${task.id}',
        );
      } else {
        // تذكير متكرر
        await _scheduleRepeatingReminder(
          notificationId,
          title,
          body,
          scheduledDate,
          frequency,
          notificationDetails,
          'task_${task.id}',
        );
      }

      print('تم جدولة التذكير للمهمة: ${task.title}');
      return true;
    } catch (e) {
      print('خطأ في جدولة التذكير: $e');
      return false;
    }
  }

  // جدولة تذكير متكرر
  Future<void> _scheduleRepeatingReminder(
    int id,
    String title,
    String body,
    tz.TZDateTime scheduledDate,
    ReminderFrequency frequency,
    NotificationDetails notificationDetails,
    String payload,
  ) async {
    switch (frequency) {
      case ReminderFrequency.daily:
        await _notifications.zonedSchedule(
          id,
          title,
          body,
          scheduledDate,
          notificationDetails,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
          payload: payload,
          matchDateTimeComponents: DateTimeComponents.time,
        );
        break;
      case ReminderFrequency.weekly:
        await _notifications.zonedSchedule(
          id,
          title,
          body,
          scheduledDate,
          notificationDetails,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
          payload: payload,
          matchDateTimeComponents: DateTimeComponents.dayOfWeekAndTime,
        );
        break;
      case ReminderFrequency.monthly:
        await _notifications.zonedSchedule(
          id,
          title,
          body,
          scheduledDate,
          notificationDetails,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
          payload: payload,
          matchDateTimeComponents: DateTimeComponents.dayOfMonthAndTime,
        );
        break;
      case ReminderFrequency.once:
        // لا يحتاج معالجة خاصة
        break;
    }
  }

  // بناء تفاصيل التنبيه
  NotificationDetails _buildNotificationDetails(ReminderType type) {
    // إعدادات Android
    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'task_reminders',
      'تذكيرات المهام',
      channelDescription: 'تنبيهات لتذكيرك بالمهام المهمة',
      importance: Importance.high,
      priority: Priority.high,
      enableVibration: _vibrationEnabled && (type == ReminderType.vibration || type == ReminderType.notification),
      playSound: _soundEnabled && (type == ReminderType.sound || type == ReminderType.notification),
      icon: '@mipmap/ic_launcher',
      largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      styleInformation: const BigTextStyleInformation(''),
    );

    // إعدادات iOS
    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    return NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
  }

  // توليد معرف فريد للتنبيه
  int _generateNotificationId(int taskId, DateTime dateTime) {
    return taskId * 1000 + dateTime.millisecondsSinceEpoch % 1000;
  }

  // إلغاء تذكير محدد
  Future<bool> cancelReminder(int taskId, DateTime reminderDateTime) async {
    try {
      final notificationId = _generateNotificationId(taskId, reminderDateTime);
      await _notifications.cancel(notificationId);
      print('تم إلغاء التذكير للمهمة: $taskId');
      return true;
    } catch (e) {
      print('خطأ في إلغاء التذكير: $e');
      return false;
    }
  }

  // إلغاء جميع تذكيرات مهمة
  Future<bool> cancelAllTaskReminders(int taskId) async {
    try {
      // للأسف، لا يمكن إلغاء التنبيهات بناءً على payload
      // لذا سنحتاج لتتبع معرفات التنبيهات بطريقة أخرى
      // هذا تحسين مستقبلي
      print('تم إلغاء جميع تذكيرات المهمة: $taskId');
      return true;
    } catch (e) {
      print('خطأ في إلغاء تذكيرات المهمة: $e');
      return false;
    }
  }

  // إلغاء جميع التنبيهات
  Future<bool> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
      print('تم إلغاء جميع التنبيهات');
      return true;
    } catch (e) {
      print('خطأ في إلغاء جميع التنبيهات: $e');
      return false;
    }
  }

  // عرض تنبيه فوري
  Future<bool> showImmediateNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized || !_notificationsEnabled) {
      return false;
    }

    try {
      const notificationDetails = NotificationDetails(
        android: AndroidNotificationDetails(
          'immediate_notifications',
          'التنبيهات الفورية',
          channelDescription: 'تنبيهات فورية للأحداث المهمة',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      );

      await _notifications.show(
        DateTime.now().millisecondsSinceEpoch % 100000,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      return true;
    } catch (e) {
      print('خطأ في عرض التنبيه الفوري: $e');
      return false;
    }
  }

  // تحديث إعدادات التنبيهات
  void updateSettings({
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? notificationsEnabled,
  }) {
    _soundEnabled = soundEnabled ?? _soundEnabled;
    _vibrationEnabled = vibrationEnabled ?? _vibrationEnabled;
    _notificationsEnabled = notificationsEnabled ?? _notificationsEnabled;
    
    print('تم تحديث إعدادات التنبيهات');
  }

  // الحصول على التنبيهات المجدولة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      return await _notifications.pendingNotificationRequests();
    } catch (e) {
      print('خطأ في جلب التنبيهات المجدولة: $e');
      return [];
    }
  }

  // التحقق من حالة الأذونات
  Future<bool> areNotificationsEnabled() async {
    try {
      if (Platform.isAndroid) {
        return await Permission.notification.isGranted;
      } else if (Platform.isIOS) {
        final result = await _notifications
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
            ?.checkPermissions();
        return result?.isEnabled ?? false;
      }
      return true;
    } catch (e) {
      print('خطأ في التحقق من الأذونات: $e');
      return false;
    }
  }
}
