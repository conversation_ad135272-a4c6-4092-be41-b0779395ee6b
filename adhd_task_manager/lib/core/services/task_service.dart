import '../database/database_helper.dart';
import '../models/task.dart';

class TaskService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة مهمة جديدة
  Future<Task> addTask({
    required String title,
    required String description,
    required TaskPriority priority,
    DateTime? dueDate,
  }) async {
    try {
      if (title.trim().isEmpty) {
        throw Exception('عنوان المهمة مطلوب');
      }

      final task = Task.create(
        title: title.trim(),
        description: description.trim(),
        priority: priority,
        dueDate: dueDate,
      );

      final id = await _databaseHelper.insertTask(task.toDatabase());
      return task.copyWith(id: id);
    } catch (e) {
      throw Exception('فشل في إضافة المهمة: $e');
    }
  }

  // الحصول على جميع المهام
  Future<List<Task>> getAllTasks() async {
    try {
      final tasksData = await _databaseHelper.getAllTasks();
      return tasksData.map((data) => Task.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المهام: $e');
    }
  }

  // الحصول على مهمة بالمعرف
  Future<Task?> getTaskById(int id) async {
    try {
      final taskData = await _databaseHelper.getTaskById(id);
      return taskData != null ? Task.fromDatabase(taskData) : null;
    } catch (e) {
      throw Exception('فشل في جلب المهمة: $e');
    }
  }

  // الحصول على المهام حسب الأولوية
  Future<List<Task>> getTasksByPriority(TaskPriority priority) async {
    try {
      final tasksData = await _databaseHelper.getTasksByPriority(priority.value);
      return tasksData.map((data) => Task.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المهام حسب الأولوية: $e');
    }
  }

  // الحصول على المهام المكتملة
  Future<List<Task>> getCompletedTasks() async {
    try {
      final tasksData = await _databaseHelper.getTasksByCompletion(true);
      return tasksData.map((data) => Task.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المهام المكتملة: $e');
    }
  }

  // الحصول على المهام غير المكتملة
  Future<List<Task>> getPendingTasks() async {
    try {
      final tasksData = await _databaseHelper.getTasksByCompletion(false);
      return tasksData.map((data) => Task.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المهام المعلقة: $e');
    }
  }

  // تحديث مهمة
  Future<Task> updateTask(Task task) async {
    try {
      if (task.id == null) {
        throw Exception('معرف المهمة مطلوب للتحديث');
      }

      if (task.title.trim().isEmpty) {
        throw Exception('عنوان المهمة مطلوب');
      }

      final updatedTask = task.copyWith(updatedAt: DateTime.now());
      final rowsAffected = await _databaseHelper.updateTask(
        task.id!,
        updatedTask.toDatabase(),
      );

      if (rowsAffected == 0) {
        throw Exception('المهمة غير موجودة');
      }

      return updatedTask;
    } catch (e) {
      throw Exception('فشل في تحديث المهمة: $e');
    }
  }

  // تبديل حالة إكمال المهمة
  Future<Task> toggleTaskCompletion(Task task) async {
    try {
      if (task.id == null) {
        throw Exception('معرف المهمة مطلوب');
      }

      final updatedTask = task.toggleCompleted();
      final rowsAffected = await _databaseHelper.updateTask(
        task.id!,
        updatedTask.toDatabase(),
      );

      if (rowsAffected == 0) {
        throw Exception('المهمة غير موجودة');
      }

      return updatedTask;
    } catch (e) {
      throw Exception('فشل في تحديث حالة المهمة: $e');
    }
  }

  // حذف مهمة
  Future<bool> deleteTask(int id) async {
    try {
      final rowsAffected = await _databaseHelper.deleteTask(id);
      return rowsAffected > 0;
    } catch (e) {
      throw Exception('فشل في حذف المهمة: $e');
    }
  }

  // حذف جميع المهام
  Future<int> deleteAllTasks() async {
    try {
      return await _databaseHelper.deleteAllTasks();
    } catch (e) {
      throw Exception('فشل في حذف جميع المهام: $e');
    }
  }

  // البحث في المهام
  Future<List<Task>> searchTasks(String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getAllTasks();
      }

      final tasksData = await _databaseHelper.searchTasks(query.trim());
      return tasksData.map((data) => Task.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في البحث في المهام: $e');
    }
  }

  // الحصول على إحصائيات المهام
  Future<TaskStatistics> getTaskStatistics() async {
    try {
      final totalTasks = await _databaseHelper.getTasksCount();
      final completedTasks = await _databaseHelper.getCompletedTasksCount();
      final pendingTasks = totalTasks - completedTasks;

      // حساب المهام حسب الأولوية
      final urgentTasks = await getTasksByPriority(TaskPriority.urgent);
      final importantTasks = await getTasksByPriority(TaskPriority.important);
      final normalTasks = await getTasksByPriority(TaskPriority.normal);

      return TaskStatistics(
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        pendingTasks: pendingTasks,
        urgentTasks: urgentTasks.length,
        importantTasks: importantTasks.length,
        normalTasks: normalTasks.length,
      );
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات المهام: $e');
    }
  }

  // الحصول على المهام مرتبة حسب الأولوية
  Future<List<Task>> getTasksSortedByPriority() async {
    try {
      final tasks = await getAllTasks();
      tasks.sort((a, b) {
        // ترتيب حسب الأولوية أولاً، ثم حسب تاريخ الإنشاء
        final priorityComparison = b.priority.order.compareTo(a.priority.order);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        return b.createdAt.compareTo(a.createdAt);
      });
      return tasks;
    } catch (e) {
      throw Exception('فشل في ترتيب المهام: $e');
    }
  }

  // الحصول على المهام المستحقة اليوم
  Future<List<Task>> getTasksDueToday() async {
    try {
      final allTasks = await getAllTasks();
      final today = DateTime.now();
      
      return allTasks.where((task) {
        if (task.dueDate == null) return false;
        return task.dueDate!.year == today.year &&
               task.dueDate!.month == today.month &&
               task.dueDate!.day == today.day;
      }).toList();
    } catch (e) {
      throw Exception('فشل في جلب المهام المستحقة اليوم: $e');
    }
  }

  // الحصول على المهام المتأخرة
  Future<List<Task>> getOverdueTasks() async {
    try {
      final allTasks = await getAllTasks();
      final now = DateTime.now();
      
      return allTasks.where((task) {
        if (task.dueDate == null || task.isCompleted) return false;
        return task.dueDate!.isBefore(now);
      }).toList();
    } catch (e) {
      throw Exception('فشل في جلب المهام المتأخرة: $e');
    }
  }
}

// فئة إحصائيات المهام
class TaskStatistics {
  final int totalTasks;
  final int completedTasks;
  final int pendingTasks;
  final int urgentTasks;
  final int importantTasks;
  final int normalTasks;

  const TaskStatistics({
    required this.totalTasks,
    required this.completedTasks,
    required this.pendingTasks,
    required this.urgentTasks,
    required this.importantTasks,
    required this.normalTasks,
  });

  // نسبة الإكمال
  double get completionRate {
    if (totalTasks == 0) return 0.0;
    return completedTasks / totalTasks;
  }

  // نسبة الإكمال كنسبة مئوية
  int get completionPercentage {
    return (completionRate * 100).round();
  }
}
