import 'dart:async';
import '../database/database_helper.dart';
import '../models/focus_session.dart';
import 'notification_service.dart';

class PomodoroService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final NotificationService _notificationService = NotificationService.instance;

  Timer? _timer;
  FocusSession? _currentSession;
  PomodoroSettings _settings = const PomodoroSettings();

  // Stream controllers للحالة
  final StreamController<FocusSession?> _sessionController =
      StreamController<FocusSession?>.broadcast();
  final StreamController<Duration> _timerController =
      StreamController<Duration>.broadcast();
  final StreamController<PomodoroSettings> _settingsController =
      StreamController<PomodoroSettings>.broadcast();

  // Getters للـ streams
  Stream<FocusSession?> get sessionStream => _sessionController.stream;
  Stream<Duration> get timerStream => _timerController.stream;
  Stream<PomodoroSettings> get settingsStream => _settingsController.stream;

  // Getters للحالة الحالية
  FocusSession? get currentSession => _currentSession;
  PomodoroSettings get settings => _settings;
  bool get isRunning => _timer != null && _timer!.isActive;

  // تهيئة الخدمة
  Future<void> initialize() async {
    try {
      // تحميل الإعدادات المحفوظة
      await _loadSettings();

      // البحث عن جلسة نشطة
      await _loadActiveSession();

      // إرسال الحالة الأولية للـ streams
      _sessionController.add(_currentSession);
      _timerController.add(Duration.zero);
      _settingsController.add(_settings);

      print('تم تهيئة خدمة Pomodoro بنجاح');
    } catch (e) {
      print('خطأ في تهيئة خدمة Pomodoro: $e');

      // إرسال الحالة الافتراضية في حالة الخطأ
      _sessionController.add(null);
      _timerController.add(Duration.zero);
      _settingsController.add(_settings);
    }
  }

  // تحميل الإعدادات
  Future<void> _loadSettings() async {
    // TODO: تحميل الإعدادات من SharedPreferences
    // للآن نستخدم الإعدادات الافتراضية
    _settingsController.add(_settings);
  }

  // تحميل الجلسة النشطة
  Future<void> _loadActiveSession() async {
    try {
      final sessionData = await _databaseHelper.getActiveFocusSession();
      if (sessionData != null) {
        _currentSession = FocusSession.fromDatabase(sessionData);

        // التحقق من انتهاء الجلسة
        if (_currentSession!.isExpired) {
          await _completeSession();
        } else {
          _startTimer();
        }
      }
    } catch (e) {
      print('خطأ في تحميل الجلسة النشطة: $e');
      // في حالة عدم وجود الجدول، نتجاهل الخطأ ونستمر
      if (e.toString().contains('no such table')) {
        print('جدول جلسات التركيز غير موجود بعد، سيتم إنشاؤه لاحقاً');
      }
      // تأكد من أن _currentSession هو null
      _currentSession = null;
    }
  }

  // بدء جلسة جديدة
  Future<FocusSession> startSession({
    required FocusSessionType type,
    int? customDuration,
    int? taskId,
    String? notes,
  }) async {
    try {
      // إنهاء الجلسة الحالية إن وجدت
      if (_currentSession != null) {
        await _completeSession();
      }

      // تحديد المدة حسب النوع
      int duration;
      switch (type) {
        case FocusSessionType.work:
          duration = customDuration ?? _settings.workDuration;
          break;
        case FocusSessionType.shortBreak:
          duration = customDuration ?? _settings.shortBreakDuration;
          break;
        case FocusSessionType.longBreak:
          duration = customDuration ?? _settings.longBreakDuration;
          break;
      }

      // إنشاء جلسة جديدة
      _currentSession = FocusSession.create(
        type: type,
        plannedDuration: duration,
        taskId: taskId,
        notes: notes,
      );

      // حفظ في قاعدة البيانات
      final id = await _databaseHelper.insertFocusSession(
        _currentSession!.toDatabase(),
      );
      _currentSession = _currentSession!.copyWith(id: id);

      // بدء المؤقت
      _startTimer();

      // إرسال التحديث
      _sessionController.add(_currentSession);

      // إرسال تنبيه بدء الجلسة
      if (_settings.enableNotifications) {
        await _notificationService.showImmediateNotification(
          title: 'بدء جلسة ${type.value}',
          body: 'مدة الجلسة: $duration دقيقة',
        );
      }

      return _currentSession!;
    } catch (e) {
      throw Exception('فشل في بدء الجلسة: $e');
    }
  }

  // إيقاف الجلسة مؤقتاً
  Future<void> pauseSession() async {
    if (_currentSession == null || !_currentSession!.status.isActive) {
      return;
    }

    try {
      _timer?.cancel();
      _currentSession = _currentSession!.pause();

      // تحديث في قاعدة البيانات
      await _databaseHelper.updateFocusSession(
        _currentSession!.id!,
        _currentSession!.toDatabase(),
      );

      _sessionController.add(_currentSession);
    } catch (e) {
      throw Exception('فشل في إيقاف الجلسة: $e');
    }
  }

  // استئناف الجلسة
  Future<void> resumeSession() async {
    if (_currentSession == null || !_currentSession!.status.isPaused) {
      return;
    }

    try {
      _currentSession = _currentSession!.resume();

      // تحديث في قاعدة البيانات
      await _databaseHelper.updateFocusSession(
        _currentSession!.id!,
        _currentSession!.toDatabase(),
      );

      _startTimer();
      _sessionController.add(_currentSession);
    } catch (e) {
      throw Exception('فشل في استئناف الجلسة: $e');
    }
  }

  // إلغاء الجلسة
  Future<void> cancelSession() async {
    if (_currentSession == null) return;

    try {
      _timer?.cancel();
      _currentSession = _currentSession!.cancel();

      // تحديث في قاعدة البيانات
      await _databaseHelper.updateFocusSession(
        _currentSession!.id!,
        _currentSession!.toDatabase(),
      );

      _sessionController.add(_currentSession);
      _currentSession = null;

      // إرسال تنبيه إلغاء الجلسة
      if (_settings.enableNotifications) {
        await _notificationService.showImmediateNotification(
          title: 'تم إلغاء الجلسة',
          body: 'يمكنك بدء جلسة جديدة في أي وقت',
        );
      }
    } catch (e) {
      throw Exception('فشل في إلغاء الجلسة: $e');
    }
  }

  // إكمال الجلسة
  Future<void> _completeSession() async {
    if (_currentSession == null) return;

    try {
      _timer?.cancel();
      _currentSession = _currentSession!.complete();

      // تحديث في قاعدة البيانات
      await _databaseHelper.updateFocusSession(
        _currentSession!.id!,
        _currentSession!.toDatabase(),
      );

      _sessionController.add(_currentSession);

      // إرسال تنبيه إكمال الجلسة
      if (_settings.enableNotifications) {
        final isSuccessful = _currentSession!.isSuccessful;
        await _notificationService.showImmediateNotification(
          title: isSuccessful ? 'تم إكمال الجلسة بنجاح!' : 'انتهت الجلسة',
          body:
              isSuccessful
                  ? 'أحسنت! لقد أكملت جلسة ${_currentSession!.type.value}'
                  : 'جلسة ${_currentSession!.type.value} انتهت',
        );
      }

      // اقتراح الجلسة التالية
      await _suggestNextSession();

      _currentSession = null;
    } catch (e) {
      throw Exception('فشل في إكمال الجلسة: $e');
    }
  }

  // بدء المؤقت
  void _startTimer() {
    _timer?.cancel();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_currentSession == null) {
        timer.cancel();
        return;
      }

      final remaining = _currentSession!.remainingDuration;
      _timerController.add(remaining);

      // التحقق من انتهاء الوقت
      if (remaining == Duration.zero) {
        timer.cancel();
        _completeSession();
      }
    });

    // إرسال الوقت المتبقي فوراً
    if (_currentSession != null) {
      _timerController.add(_currentSession!.remainingDuration);
    }
  }

  // اقتراح الجلسة التالية
  Future<void> _suggestNextSession() async {
    if (_currentSession == null) return;

    try {
      FocusSessionType nextType;

      if (_currentSession!.type == FocusSessionType.work) {
        // بعد جلسة العمل، تحديد نوع الراحة
        final workSessionsToday = await _getWorkSessionsToday();
        final shouldTakeLongBreak =
            workSessionsToday % _settings.sessionsUntilLongBreak == 0;

        nextType =
            shouldTakeLongBreak
                ? FocusSessionType.longBreak
                : FocusSessionType.shortBreak;
      } else {
        // بعد الراحة، العودة للعمل
        nextType = FocusSessionType.work;
      }

      // بدء تلقائي إذا كان مفعلاً
      if ((nextType == FocusSessionType.work && _settings.autoStartWork) ||
          (nextType != FocusSessionType.work && _settings.autoStartBreaks)) {
        await Future.delayed(const Duration(seconds: 3));
        await startSession(type: nextType);
      }
    } catch (e) {
      print('خطأ في اقتراح الجلسة التالية: $e');
    }
  }

  // الحصول على عدد جلسات العمل اليوم
  Future<int> _getWorkSessionsToday() async {
    try {
      final today = DateTime.now();
      final sessions = await _databaseHelper.getFocusSessionsByDate(today);

      return sessions
          .where(
            (session) =>
                session['type'] == 'عمل' && session['status'] == 'مكتملة',
          )
          .length;
    } catch (e) {
      return 0;
    }
  }

  // تحديث الإعدادات
  Future<void> updateSettings(PomodoroSettings newSettings) async {
    try {
      _settings = newSettings;
      _settingsController.add(_settings);

      // TODO: حفظ الإعدادات في SharedPreferences

      print('تم تحديث إعدادات Pomodoro');
    } catch (e) {
      throw Exception('فشل في تحديث الإعدادات: $e');
    }
  }

  // الحصول على جلسات اليوم
  Future<List<FocusSession>> getTodaySessions() async {
    try {
      final today = DateTime.now();
      final sessionsData = await _databaseHelper.getFocusSessionsByDate(today);

      return sessionsData
          .map((data) => FocusSession.fromDatabase(data))
          .toList();
    } catch (e) {
      // في حالة عدم وجود الجدول، نعيد قائمة فارغة
      if (e.toString().contains('no such table')) {
        return [];
      }
      throw Exception('فشل في جلب جلسات اليوم: $e');
    }
  }

  // تنظيف الموارد
  void dispose() {
    _timer?.cancel();
    _sessionController.close();
    _timerController.close();
    _settingsController.close();
  }
}
