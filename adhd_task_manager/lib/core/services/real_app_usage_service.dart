import 'dart:io';
import 'package:installed_apps/installed_apps.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:usage_stats/usage_stats.dart';
import '../models/app_usage_data.dart';

class RealAppUsageService {
  static final RealAppUsageService _instance = RealAppUsageService._internal();
  factory RealAppUsageService() => _instance;
  RealAppUsageService._internal();

  /// طلب الأذونات المطلوبة
  Future<bool> requestPermissions() async {
    if (Platform.isAndroid) {
      try {
        print('🔐 فحص أذونات إحصائيات الاستخدام...');
        // التحقق من إذن إحصائيات الاستخدام
        bool hasPermission = await UsageStats.checkUsagePermission() ?? false;
        print('📊 حالة الإذن الحالية: $hasPermission');

        if (!hasPermission) {
          print('🚀 طلب الإذن من المستخدم...');
          // طلب الإذن من المستخدم
          await UsageStats.grantUsagePermission();

          // التحقق مرة أخرى
          hasPermission = await UsageStats.checkUsagePermission() ?? false;
          print('✅ حالة الإذن بعد الطلب: $hasPermission');
        }

        return hasPermission;
      } catch (e) {
        print('❌ خطأ في طلب الأذونات: $e');
        return false;
      }
    } else if (Platform.isIOS) {
      // في iOS، لا نحتاج أذونات خاصة للتطبيقات المثبتة
      return true;
    }

    return false;
  }

  /// الحصول على قائمة التطبيقات المثبتة
  Future<List<AppUsageData>> getInstalledApps() async {
    try {
      final apps = await InstalledApps.getInstalledApps(
        true, // includeSystemApps
        true, // includeAppIcons
      );

      final List<AppUsageData> appUsageList = [];

      for (final app in apps) {
        // تجاهل تطبيقات النظام غير المهمة
        if (_shouldIncludeApp(app.packageName)) {
          appUsageList.add(
            AppUsageData(
              appName: app.name,
              packageName: app.packageName,
              usageTime: Duration.zero, // سيتم تحديثه لاحقاً
              category: _getAppCategory(app.packageName),
              icon: app.icon,
            ),
          );
        }
      }

      return appUsageList;
    } catch (e) {
      return [];
    }
  }

  /// الحصول على بيانات الاستخدام للتطبيقات
  Future<List<AppUsageData>> getAppUsageStats({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (!Platform.isAndroid) {
        // في iOS، نعيد التطبيقات المثبتة فقط
        return await getInstalledApps();
      }

      // التحقق من الأذونات أولاً
      final hasPermission = await UsageStats.checkUsagePermission() ?? false;
      if (!hasPermission) {
        // إذا لم تكن الأذونات متاحة، نعيد التطبيقات المثبتة فقط
        return await getInstalledApps();
      }

      final installedApps = await getInstalledApps();
      final appsWithUsageData = <AppUsageData>[];

      // الحصول على بيانات الاستخدام الحقيقية
      print(
        '📊 استعلام بيانات الاستخدام من ${startDate.toString()} إلى ${endDate.toString()}',
      );
      final usageStats = await UsageStats.queryUsageStats(startDate, endDate);
      print('📈 تم العثور على ${usageStats.length} إحصائية استخدام');

      // إنشاء خريطة للبيانات الحقيقية
      final usageMap = <String, UsageInfo>{};
      var appsWithUsageCount = 0;
      for (final stat in usageStats) {
        if (stat.packageName != null && stat.packageName!.isNotEmpty) {
          usageMap[stat.packageName!] = stat;
          final timeMs = (stat.totalTimeInForeground as int?) ?? 0;
          if (timeMs > 0) {
            appsWithUsageCount++;
            final minutes = Duration(milliseconds: timeMs).inMinutes;
            final seconds = Duration(milliseconds: timeMs).inSeconds % 60;
            print(
              '⏱️ ${stat.packageName}: ${minutes}:${seconds.toString().padLeft(2, '0')} دقيقة:ثانية (${timeMs}ms)',
            );
          }
        }
      }
      print(
        '📊 عدد التطبيقات التي لها وقت استخدام: $appsWithUsageCount من ${usageStats.length}',
      );

      for (final app in installedApps) {
        final packageName = app.packageName;
        final usageData = usageMap[packageName];

        Duration usageTime = Duration.zero;
        int launchCount = 0;
        DateTime? lastUsed;

        if (usageData != null) {
          // استخراج البيانات الحقيقية
          final totalTimeInForeground =
              (usageData.totalTimeInForeground as int?) ?? 0;
          usageTime = Duration(milliseconds: totalTimeInForeground);

          final lastTimeUsedMs = (usageData.lastTimeUsed as int?) ?? 0;
          if (lastTimeUsedMs > 0) {
            lastUsed = DateTime.fromMillisecondsSinceEpoch(lastTimeUsedMs);
          }

          // تقدير عدد مرات الفتح بناءً على وقت الاستخدام
          if (usageTime.inMinutes > 0) {
            launchCount = (usageTime.inMinutes / 10).ceil().clamp(1, 50);
          }
        }

        appsWithUsageData.add(
          app.copyWith(
            usageTime: usageTime,
            launchCount: launchCount,
            lastTimeUsed: lastUsed,
          ),
        );
      }

      // ترتيب التطبيقات حسب وقت الاستخدام
      appsWithUsageData.sort((a, b) => b.usageTime.compareTo(a.usageTime));

      return appsWithUsageData;
    } catch (e) {
      // في حالة الخطأ، نعيد التطبيقات المثبتة
      return await getInstalledApps();
    }
  }

  /// تحديد ما إذا كان يجب تضمين التطبيق
  bool _shouldIncludeApp(String packageName) {
    // تجاهل تطبيقات النظام غير المهمة
    final systemApps = [
      'com.android.systemui',
      'com.android.settings',
      'com.android.launcher',
      'com.google.android.gms',
      'com.android.vending',
      'android',
    ];

    for (final systemApp in systemApps) {
      if (packageName.startsWith(systemApp)) {
        return false;
      }
    }

    return packageName.isNotEmpty;
  }

  /// تحديد فئة التطبيق بناءً على اسم الحزمة والبيانات الوصفية
  String _getAppCategory(String packageName) {
    // فئات التواصل الاجتماعي
    final socialApps = [
      'com.facebook',
      'com.instagram',
      'com.twitter',
      'com.snapchat',
      'com.whatsapp',
      'org.telegram',
      'com.linkedin',
      'com.tiktok',
      'com.discord',
      'com.skype',
      'com.viber',
      'com.pinterest',
      'com.reddit',
      'com.tumblr',
      'com.facebook.orca',
    ];

    // فئات الألعاب
    final gameApps = [
      'com.king',
      'com.supercell',
      'com.rovio',
      'com.ea',
      'com.gameloft',
      'com.ubisoft',
      'com.activision',
      'com.miHoYo',
      'com.tencent',
      'com.pubg',
      'com.roblox',
    ];

    // فئات الترفيه
    final entertainmentApps = [
      'com.google.android.youtube',
      'com.netflix',
      'com.spotify',
      'com.amazon.avod',
      'com.disney',
      'com.hulu',
      'com.twitch',
      'com.soundcloud',
      'com.apple.android.music',
      'com.deezer',
    ];

    // فئات الإنتاجية
    final productivityApps = [
      'com.microsoft',
      'com.google.android.apps.docs',
      'com.adobe',
      'com.dropbox',
      'com.evernote',
      'com.todoist',
      'com.trello',
      'com.slack',
      'com.zoom',
      'notion.id',
    ];

    // التحقق من الفئات
    for (final socialApp in socialApps) {
      if (packageName.startsWith(socialApp)) {
        return 'التواصل الاجتماعي';
      }
    }

    for (final gameApp in gameApps) {
      if (packageName.contains(gameApp) || packageName.contains('game')) {
        return 'الألعاب';
      }
    }

    for (final entertainmentApp in entertainmentApps) {
      if (packageName.startsWith(entertainmentApp)) {
        return 'الترفيه';
      }
    }

    for (final productivityApp in productivityApps) {
      if (packageName.startsWith(productivityApp)) {
        return 'الإنتاجية';
      }
    }

    // فئات إضافية بناءً على الكلمات المفتاحية
    if (packageName.contains('bank') ||
        packageName.contains('pay') ||
        packageName.contains('wallet') ||
        packageName.contains('finance')) {
      return 'المالية';
    }

    if (packageName.contains('health') ||
        packageName.contains('fitness') ||
        packageName.contains('medical') ||
        packageName.contains('workout')) {
      return 'الصحة واللياقة';
    }

    if (packageName.contains('news') || packageName.contains('weather')) {
      return 'الأخبار والطقس';
    }

    return 'أخرى';
  }
}
