import 'dart:io';
import 'dart:math';
import 'package:installed_apps/installed_apps.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/app_usage_data.dart';

class RealAppUsageService {
  static final RealAppUsageService _instance = RealAppUsageService._internal();
  factory RealAppUsageService() => _instance;
  RealAppUsageService._internal();

  /// طلب الأذونات المطلوبة
  Future<bool> requestPermissions() async {
    if (Platform.isAndroid) {
      // طلب إذن إحصائيات الاستخدام
      final usagePermission = await Permission.systemAlertWindow.request();

      // فتح إعدادات إحصائيات الاستخدام
      if (usagePermission.isDenied) {
        // يمكن للمستخدم فتح الإعدادات يدوياً
        return false;
      }

      return true;
    } else if (Platform.isIOS) {
      // في iOS، لا نحتاج أذونات خاصة للتطبيقات المثبتة
      return true;
    }

    return false;
  }

  /// الحصول على قائمة التطبيقات المثبتة
  Future<List<AppUsageData>> getInstalledApps() async {
    try {
      final apps = await InstalledApps.getInstalledApps(
        true, // includeSystemApps
        true, // includeAppIcons
      );

      final List<AppUsageData> appUsageList = [];

      for (final app in apps) {
        // تجاهل تطبيقات النظام غير المهمة
        if (_shouldIncludeApp(app.packageName)) {
          appUsageList.add(
            AppUsageData(
              appName: app.name,
              packageName: app.packageName,
              usageTime: Duration.zero, // سيتم تحديثه لاحقاً
              category: _getAppCategory(app.packageName),
              icon: app.icon,
            ),
          );
        }
      }

      return appUsageList;
    } catch (e) {
      return [];
    }
  }

  /// الحصول على بيانات الاستخدام للتطبيقات
  Future<List<AppUsageData>> getAppUsageStats({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (!Platform.isAndroid) {
        // في iOS، نعيد التطبيقات المثبتة فقط
        return await getInstalledApps();
      }

      final installedApps = await getInstalledApps();

      // محاكاة بيانات الاستخدام
      final random = Random();
      final appsWithUsage = <AppUsageData>[];

      for (final app in installedApps) {
        // محاكاة وقت استخدام عشوائي (0-180 دقيقة)
        final usageMinutes = random.nextInt(180);
        final launchCount = usageMinutes > 0 ? random.nextInt(20) + 1 : 0;

        // محاكاة آخر وقت استخدام
        DateTime? lastUsed;
        if (usageMinutes > 0) {
          final hoursAgo = random.nextInt(24);
          lastUsed = DateTime.now().subtract(Duration(hours: hoursAgo));
        }

        appsWithUsage.add(
          app.copyWith(
            usageTime: Duration(minutes: usageMinutes),
            launchCount: launchCount,
            lastTimeUsed: lastUsed,
          ),
        );
      }

      // ترتيب التطبيقات حسب وقت الاستخدام
      appsWithUsage.sort((a, b) => b.usageTime.compareTo(a.usageTime));

      return appsWithUsage;
    } catch (e) {
      // في حالة الخطأ، نعيد التطبيقات المثبتة
      return await getInstalledApps();
    }
  }

  /// تحديد ما إذا كان يجب تضمين التطبيق
  bool _shouldIncludeApp(String packageName) {
    // تجاهل تطبيقات النظام غير المهمة
    final systemApps = [
      'com.android.systemui',
      'com.android.settings',
      'com.android.launcher',
      'com.google.android.gms',
      'com.android.vending',
      'android',
    ];

    for (final systemApp in systemApps) {
      if (packageName.startsWith(systemApp)) {
        return false;
      }
    }

    return packageName.isNotEmpty;
  }

  /// تحديد فئة التطبيق
  String _getAppCategory(String packageName) {
    if (packageName.contains('social') ||
        packageName.contains('facebook') ||
        packageName.contains('instagram') ||
        packageName.contains('twitter') ||
        packageName.contains('whatsapp') ||
        packageName.contains('telegram')) {
      return 'التواصل الاجتماعي';
    } else if (packageName.contains('game')) {
      return 'الألعاب';
    } else if (packageName.contains('music') ||
        packageName.contains('spotify') ||
        packageName.contains('youtube')) {
      return 'الترفيه';
    } else if (packageName.contains('work') ||
        packageName.contains('office') ||
        packageName.contains('docs') ||
        packageName.contains('productivity')) {
      return 'الإنتاجية';
    } else {
      return 'أخرى';
    }
  }
}
