import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

/// خدمة إدارة الأذونات
class PermissionsService {
  static final PermissionsService _instance = PermissionsService._internal();
  factory PermissionsService() => _instance;
  PermissionsService._internal();

  /// طلب أذونات تتبع الاستخدام
  Future<bool> requestUsageTrackingPermissions(BuildContext context) async {
    try {
      if (Platform.isAndroid) {
        // التحقق من إذن إحصائيات الاستخدام
        bool hasUsagePermission = await _checkUsageStatsPermission();
        
        if (!hasUsagePermission) {
          // عرض dialog توضيحي للمستخدم
          final userConsent = await _showUsagePermissionDialog(context);
          if (!userConsent) return false;
          
          // توجيه المستخدم لإعدادات النظام
          await _openUsageAccessSettings();
          
          // انتظار قصير ثم التحقق مرة أخرى
          await Future.delayed(const Duration(seconds: 2));
          hasUsagePermission = await _checkUsageStatsPermission();
          
          // إذا لم يتم منح الإذن، عرض تعليمات إضافية
          if (!hasUsagePermission) {
            await _showUsagePermissionInstructions(context);
          }
        }

        return hasUsagePermission;
      } else if (Platform.isIOS) {
        // iOS لا يدعم تتبع استخدام التطبيقات بنفس الطريقة
        await _showIOSLimitationDialog(context);
        return true; // نفترض الموافقة مؤقتاً
      }

      return false;
    } catch (e) {
      await _showErrorDialog(context, 'فشل في طلب الأذونات: $e');
      return false;
    }
  }

  /// التحقق من إذن إحصائيات الاستخدام
  Future<bool> _checkUsageStatsPermission() async {
    try {
      // في التطبيق الحقيقي، سنستخدم app_usage package للتحقق
      // هنا نحاكي التحقق بمحاولة الوصول للبيانات
      
      // محاولة قراءة إحصائيات الاستخدام
      // إذا فشلت، فالإذن غير موجود
      
      // مؤقتاً نعيد false لإجبار المستخدم على منح الإذن
      return false;
    } catch (e) {
      return false;
    }
  }

  /// فتح إعدادات الوصول للاستخدام
  Future<void> _openUsageAccessSettings() async {
    try {
      if (Platform.isAndroid) {
        // فتح إعدادات الوصول للاستخدام في Android
        const platform = MethodChannel('com.example.adhd_task_manager/permissions');
        await platform.invokeMethod('openUsageAccessSettings');
      }
    } catch (e) {
      // في حالة فشل فتح الإعدادات، نعرض تعليمات يدوية
      print('فشل في فتح إعدادات الاستخدام: $e');
    }
  }

  /// عرض dialog طلب الإذن
  Future<bool> _showUsagePermissionDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.security, color: Colors.blue),
              SizedBox(width: 8),
              Text('إذن تتبع الاستخدام'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'لتفعيل ميزة تتبع الاستخدام، نحتاج للوصول إلى إحصائيات استخدام التطبيقات.',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 16),
              Text(
                'هذا الإذن يساعدنا في:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• تتبع وقت استخدام التطبيقات'),
              Text('• تحليل أنماط الاستخدام'),
              Text('• تقديم توصيات مخصصة'),
              Text('• مساعدتك في تحسين التركيز'),
              SizedBox(height: 16),
              Text(
                'ملاحظة: جميع البيانات تبقى محلية على جهازك ولا يتم مشاركتها.',
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('منح الإذن'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// عرض تعليمات منح الإذن
  Future<void> _showUsagePermissionInstructions(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.info, color: Colors.orange),
              SizedBox(width: 8),
              Text('تعليمات منح الإذن'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'لمنح إذن تتبع الاستخدام، يرجى اتباع الخطوات التالية:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('1. اذهب إلى إعدادات الهاتف'),
              Text('2. ابحث عن "الوصول للاستخدام" أو "Usage Access"'),
              Text('3. ابحث عن تطبيق "إنجاز" في القائمة'),
              Text('4. فعّل الإذن للتطبيق'),
              Text('5. ارجع للتطبيق وحاول مرة أخرى'),
              SizedBox(height: 16),
              Text(
                'أو يمكنك الذهاب إلى:\nالإعدادات > التطبيقات > إعدادات خاصة > الوصول لاستخدام الجهاز',
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('فهمت'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _openUsageAccessSettings();
              },
              child: const Text('فتح الإعدادات'),
            ),
          ],
        );
      },
    );
  }

  /// عرض dialog قيود iOS
  Future<void> _showIOSLimitationDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.info, color: Colors.blue),
              SizedBox(width: 8),
              Text('ملاحظة iOS'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'في نظام iOS، تتبع استخدام التطبيقات محدود بسبب قيود النظام.',
              ),
              SizedBox(height: 16),
              Text(
                'سيتم استخدام بيانات محاكاة لعرض الميزات.',
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('فهمت'),
            ),
          ],
        );
      },
    );
  }

  /// عرض dialog خطأ
  Future<void> _showErrorDialog(BuildContext context, String message) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              SizedBox(width: 8),
              Text('خطأ'),
            ],
          ),
          content: Text(message),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }

  /// التحقق من جميع الأذونات المطلوبة
  Future<Map<String, bool>> checkAllPermissions() async {
    final permissions = <String, bool>{};

    try {
      // إذن التنبيهات
      permissions['notifications'] = await Permission.notification.isGranted;
      
      // إذن تتبع الاستخدام
      permissions['usage_stats'] = await _checkUsageStatsPermission();
      
      // أذونات أخرى حسب الحاجة
      if (Platform.isAndroid) {
        permissions['system_alert_window'] = await Permission.systemAlertWindow.isGranted;
      }
      
    } catch (e) {
      print('خطأ في التحقق من الأذونات: $e');
    }

    return permissions;
  }

  /// طلب جميع الأذونات المطلوبة
  Future<bool> requestAllPermissions(BuildContext context) async {
    try {
      // طلب إذن التنبيهات
      final notificationPermission = await Permission.notification.request();
      
      // طلب إذن تتبع الاستخدام
      final usagePermission = await requestUsageTrackingPermissions(context);
      
      return notificationPermission.isGranted && usagePermission;
    } catch (e) {
      await _showErrorDialog(context, 'فشل في طلب الأذونات: $e');
      return false;
    }
  }
}
