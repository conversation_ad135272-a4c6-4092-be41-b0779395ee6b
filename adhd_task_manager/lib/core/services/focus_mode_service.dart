import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import '../database/database_helper.dart';
import '../models/focus_mode.dart';
import '../models/smart_notification.dart';
import 'smart_notification_service.dart';
import 'usage_tracking_service.dart';

/// خدمة إدارة وضع التركيز المتقدم
class FocusModeService {
  static final FocusModeService _instance = FocusModeService._internal();
  factory FocusModeService() => _instance;
  FocusModeService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final SmartNotificationService _notificationService = SmartNotificationService();
  final UsageTrackingService _usageService = UsageTrackingService();
  
  Timer? _focusModeTimer;
  FocusMode? _activeFocusMode;
  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // إنشاء أوضاع التركيز الافتراضية إذا لم تكن موجودة
      await _createDefaultFocusModes();
      
      // استعادة وضع التركيز النشط إن وجد
      await _restoreActiveFocusMode();
      
      // بدء مراقبة أوضاع التركيز
      _startFocusModeMonitoring();
      
      _isInitialized = true;
    } catch (e) {
      throw Exception('فشل في تهيئة خدمة وضع التركيز: $e');
    }
  }

  /// إنشاء أوضاع التركيز الافتراضية
  Future<void> _createDefaultFocusModes() async {
    try {
      final existingModes = await getAllFocusModes();
      
      // إذا لم توجد أوضاع، أنشئ الافتراضية
      if (existingModes.isEmpty) {
        await createFocusMode(FocusMode.workMode);
        await createFocusMode(FocusMode.studyMode);
        await createFocusMode(FocusMode.sleepMode);
      }
    } catch (e) {
      // تسجيل الخطأ ولكن عدم إيقاف التهيئة
      print('تعذر إنشاء أوضاع التركيز الافتراضية: $e');
    }
  }

  /// استعادة وضع التركيز النشط
  Future<void> _restoreActiveFocusMode() async {
    try {
      final activeModeData = await _databaseHelper.getActiveFocusMode();
      if (activeModeData != null) {
        _activeFocusMode = FocusMode.fromDatabase(activeModeData);
        
        // التحقق من انتهاء صلاحية الوضع
        if (_activeFocusMode!.isExpired) {
          await deactivateFocusMode();
        } else {
          // بدء مراقبة الوضع النشط
          _startFocusModeTimer();
        }
      }
    } catch (e) {
      print('تعذر استعادة وضع التركيز النشط: $e');
    }
  }

  /// بدء مراقبة أوضاع التركيز
  void _startFocusModeMonitoring() {
    // مراقبة دورية كل دقيقة
    Timer.periodic(const Duration(minutes: 1), (_) {
      _checkFocusModeStatus();
    });
  }

  /// فحص حالة وضع التركيز
  Future<void> _checkFocusModeStatus() async {
    if (_activeFocusMode != null && _activeFocusMode!.isExpired) {
      await deactivateFocusMode();
      
      // إرسال تنبيه انتهاء وضع التركيز
      await _notificationService.scheduleNotification(
        SmartNotification.create(
          title: 'انتهى وضع التركيز',
          message: 'انتهت جلسة "${_activeFocusMode!.name}". أحسنت!',
          type: SmartNotificationType.focusSuggestion,
          scheduledTime: DateTime.now(),
        ),
      );
    }
  }

  /// إنشاء وضع تركيز جديد
  Future<FocusMode> createFocusMode(FocusMode focusMode) async {
    try {
      final id = await _databaseHelper.insertFocusMode(focusMode.toDatabase());
      return focusMode.copyWith(id: id);
    } catch (e) {
      throw Exception('فشل في إنشاء وضع التركيز: $e');
    }
  }

  /// تحديث وضع التركيز
  Future<FocusMode> updateFocusMode(FocusMode focusMode) async {
    try {
      if (focusMode.id == null) {
        throw Exception('معرف وضع التركيز مطلوب للتحديث');
      }

      final updatedMode = focusMode.copyWith(updatedAt: DateTime.now());
      await _databaseHelper.updateFocusMode(focusMode.id!, updatedMode.toDatabase());
      
      // إذا كان الوضع نشطاً، حدث الوضع النشط
      if (_activeFocusMode?.id == focusMode.id) {
        _activeFocusMode = updatedMode;
      }
      
      return updatedMode;
    } catch (e) {
      throw Exception('فشل في تحديث وضع التركيز: $e');
    }
  }

  /// حذف وضع التركيز
  Future<void> deleteFocusMode(int id) async {
    try {
      // إذا كان الوضع نشطاً، ألغ تفعيله أولاً
      if (_activeFocusMode?.id == id) {
        await deactivateFocusMode();
      }
      
      await _databaseHelper.deleteFocusMode(id);
    } catch (e) {
      throw Exception('فشل في حذف وضع التركيز: $e');
    }
  }

  /// الحصول على جميع أوضاع التركيز
  Future<List<FocusMode>> getAllFocusModes() async {
    try {
      final modesData = await _databaseHelper.getAllFocusModes();
      return modesData.map((data) => FocusMode.fromDatabase(data)).toList();
    } catch (e) {
      throw Exception('فشل في الحصول على أوضاع التركيز: $e');
    }
  }

  /// تفعيل وضع التركيز
  Future<bool> activateFocusMode(FocusMode focusMode, {int? duration}) async {
    try {
      // إلغاء تفعيل أي وضع نشط
      await deactivateFocusMode();
      
      // تفعيل الوضع الجديد
      final activatedMode = focusMode.activate(duration: duration);
      await _databaseHelper.updateFocusMode(focusMode.id!, activatedMode.toDatabase());
      
      _activeFocusMode = activatedMode;
      
      // بدء مؤقت الوضع
      _startFocusModeTimer();
      
      // ربط مع خدمة تتبع الاستخدام
      if (_usageService.isTracking) {
        // يمكن ربط جلسة وقت الشاشة بوضع التركيز هنا
      }
      
      // إرسال تنبيه بدء وضع التركيز
      await _notificationService.scheduleNotification(
        SmartNotification.create(
          title: 'تم تفعيل وضع التركيز',
          message: 'بدأت جلسة "${activatedMode.name}". ركز على أهدافك!',
          type: SmartNotificationType.focusSuggestion,
          scheduledTime: DateTime.now(),
        ),
      );
      
      return true;
    } catch (e) {
      throw Exception('فشل في تفعيل وضع التركيز: $e');
    }
  }

  /// إلغاء تفعيل وضع التركيز
  Future<void> deactivateFocusMode() async {
    try {
      if (_activeFocusMode != null) {
        final deactivatedMode = _activeFocusMode!.deactivate();
        await _databaseHelper.updateFocusMode(_activeFocusMode!.id!, deactivatedMode.toDatabase());
        
        _activeFocusMode = null;
        _stopFocusModeTimer();
      }
    } catch (e) {
      throw Exception('فشل في إلغاء تفعيل وضع التركيز: $e');
    }
  }

  /// بدء مؤقت وضع التركيز
  void _startFocusModeTimer() {
    _stopFocusModeTimer();
    
    if (_activeFocusMode?.endTime != null) {
      final duration = _activeFocusMode!.endTime!.difference(DateTime.now());
      
      if (duration.isNegative) {
        // الوضع منتهي الصلاحية
        deactivateFocusMode();
        return;
      }
      
      _focusModeTimer = Timer(duration, () async {
        await deactivateFocusMode();
        
        // إرسال تنبيه انتهاء الوضع
        await _notificationService.scheduleNotification(
          SmartNotification.create(
            title: 'انتهى وضع التركيز',
            message: 'انتهت جلسة "${_activeFocusMode?.name}". أحسنت!',
            type: SmartNotificationType.focusSuggestion,
            scheduledTime: DateTime.now(),
          ),
        );
      });
    }
  }

  /// إيقاف مؤقت وضع التركيز
  void _stopFocusModeTimer() {
    _focusModeTimer?.cancel();
    _focusModeTimer = null;
  }

  /// التحقق من حجب تطبيق معين
  bool isAppBlocked(String packageName) {
    return _activeFocusMode?.isAppBlocked(packageName) ?? false;
  }

  /// الحصول على رسالة الحجب المخصصة
  String? getBlockMessage() {
    return _activeFocusMode?.customMessage;
  }

  /// اقتراح وضع تركيز بناءً على السياق
  Future<FocusMode?> suggestFocusMode({
    required DateTime currentTime,
    String? currentActivity,
    List<String>? recentApps,
  }) async {
    try {
      final allModes = await getAllFocusModes();
      
      // منطق اقتراح ذكي بناءً على الوقت والنشاط
      final hour = currentTime.hour;
      
      // وقت العمل (9 صباحاً - 5 مساءً)
      if (hour >= 9 && hour <= 17) {
        return allModes.firstWhere(
          (mode) => mode.type == FocusModeType.work,
          orElse: () => allModes.first,
        );
      }
      
      // وقت المساء للدراسة (6 مساءً - 10 مساءً)
      if (hour >= 18 && hour <= 22) {
        return allModes.firstWhere(
          (mode) => mode.type == FocusModeType.study,
          orElse: () => allModes.first,
        );
      }
      
      // وقت النوم (10 مساءً - 7 صباحاً)
      if (hour >= 22 || hour <= 7) {
        return allModes.firstWhere(
          (mode) => mode.type == FocusModeType.sleep,
          orElse: () => allModes.first,
        );
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على وضع التركيز النشط
  FocusMode? get activeFocusMode => _activeFocusMode;

  /// التحقق من وجود وضع تركيز نشط
  bool get isActive => _activeFocusMode?.isActive ?? false;

  /// الحصول على الوقت المتبقي لوضع التركيز النشط
  Duration? get remainingTime => _activeFocusMode?.remainingTime;

  /// تنظيف الموارد
  void dispose() {
    _stopFocusModeTimer();
  }
}
