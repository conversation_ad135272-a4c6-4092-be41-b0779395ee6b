import 'dart:async';
import 'dart:math';
import '../database/database_helper.dart';
import '../models/smart_recommendation.dart';
import '../models/user_behavior_pattern.dart';
import '../models/adaptive_goal.dart';
import '../models/usage_statistics.dart';
import 'analytics_engine.dart';

/// خدمة التوصيات الذكية المتقدمة
class SmartRecommendationService {
  static final SmartRecommendationService _instance = SmartRecommendationService._internal();
  factory SmartRecommendationService() => _instance;
  SmartRecommendationService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final AnalyticsEngine _analyticsEngine = AnalyticsEngine();
  final String _defaultUserId = 'default_user';

  bool _isInitialized = false;
  Timer? _recommendationTimer;

  /// تهيئة خدمة التوصيات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _analyticsEngine.initialize();
      _startRecommendationEngine();
      _isInitialized = true;
      print('تم تهيئة خدمة التوصيات الذكية بنجاح');
    } catch (e) {
      throw Exception('فشل في تهيئة خدمة التوصيات الذكية: $e');
    }
  }

  /// بدء محرك التوصيات
  void _startRecommendationEngine() {
    // توليد توصيات كل 30 دقيقة
    _recommendationTimer = Timer.periodic(
      const Duration(minutes: 30),
      (_) => _generatePeriodicRecommendations(),
    );

    // توليد توصيات فورية
    _generatePeriodicRecommendations();
  }

  /// توليد التوصيات الدورية
  Future<void> _generatePeriodicRecommendations() async {
    try {
      await generateContextualRecommendations();
      await generateGoalBasedRecommendations();
      await generateTimeBasedRecommendations();
      await cleanupOldRecommendations();
    } catch (e) {
      print('خطأ في توليد التوصيات الدورية: $e');
    }
  }

  /// توليد التوصيات السياقية
  Future<List<SmartRecommendation>> generateContextualRecommendations() async {
    try {
      final recommendations = <SmartRecommendation>[];
      final now = DateTime.now();

      // التوصيات بناءً على الوقت الحالي
      recommendations.addAll(await _generateTimeContextRecommendations(now));

      // التوصيات بناءً على أنماط السلوك
      recommendations.addAll(await _generateBehaviorBasedRecommendations());

      // التوصيات بناءً على الاستخدام الحالي
      recommendations.addAll(await _generateUsageBasedRecommendations());

      // حفظ التوصيات الجديدة
      for (final recommendation in recommendations) {
        if (await _isRecommendationRelevant(recommendation)) {
          await _databaseHelper.insertSmartRecommendation(recommendation.toDatabase());
        }
      }

      return recommendations;
    } catch (e) {
      print('خطأ في توليد التوصيات السياقية: $e');
      return [];
    }
  }

  /// توليد التوصيات بناءً على الوقت
  Future<List<SmartRecommendation>> _generateTimeContextRecommendations(DateTime now) async {
    final recommendations = <SmartRecommendation>[];

    try {
      final hour = now.hour;

      // توصيات الصباح (6-10 صباحاً)
      if (hour >= 6 && hour <= 10) {
        recommendations.add(
          SmartRecommendation.create(
            userId: _defaultUserId,
            type: RecommendationType.productivityTip,
            title: 'بداية يوم منتجة',
            description: 'ابدأ يومك بمراجعة مهامك وتحديد أولوياتك',
            actionText: 'عرض المهام',
            actionData: 'view_tasks',
            priority: RecommendationPriority.medium,
            relevanceScore: 0.8,
            tags: ['صباح', 'إنتاجية'],
          ),
        );
      }

      // توصيات وقت العمل (9-17)
      if (hour >= 9 && hour <= 17) {
        recommendations.add(
          SmartRecommendation.focusModeRecommendation(
            userId: _defaultUserId,
            focusModeName: 'وضع العمل',
            reason: 'وقت العمل المثالي للتركيز',
            relevanceScore: 0.9,
          ),
        );
      }

      // توصيات المساء (18-22)
      if (hour >= 18 && hour <= 22) {
        recommendations.add(
          SmartRecommendation.create(
            userId: _defaultUserId,
            type: RecommendationType.improveHabits,
            title: 'مراجعة اليوم',
            description: 'راجع إنجازاتك اليوم وخطط للغد',
            actionText: 'عرض الإحصائيات',
            actionData: 'view_statistics',
            priority: RecommendationPriority.low,
            relevanceScore: 0.6,
            tags: ['مساء', 'مراجعة'],
          ),
        );
      }

      // توصيات الليل (22-6)
      if (hour >= 22 || hour <= 6) {
        recommendations.add(
          SmartRecommendation.sleepImprovementRecommendation(
            userId: _defaultUserId,
            nightUsageMinutes: 0, // سيتم تحديثه بناءً على الاستخدام الفعلي
            relevanceScore: 0.8,
          ),
        );
      }
    } catch (e) {
      print('خطأ في توليد التوصيات الزمنية: $e');
    }

    return recommendations;
  }

  /// توليد التوصيات بناءً على السلوك
  Future<List<SmartRecommendation>> _generateBehaviorBasedRecommendations() async {
    final recommendations = <SmartRecommendation>[];

    try {
      final patterns = await _databaseHelper.getUserBehaviorPatterns(_defaultUserId);

      for (final patternData in patterns) {
        final pattern = UserBehaviorPattern.fromDatabase(patternData);
        
        // تجاهل الأنماط ضعيفة الثقة
        if (pattern.confidence < 0.5) continue;

        final recommendation = await _createRecommendationFromPattern(pattern);
        if (recommendation != null) {
          recommendations.add(recommendation);
        }
      }
    } catch (e) {
      print('خطأ في توليد التوصيات السلوكية: $e');
    }

    return recommendations;
  }

  /// إنشاء توصية من نمط سلوك
  Future<SmartRecommendation?> _createRecommendationFromPattern(
    UserBehaviorPattern pattern,
  ) async {
    switch (pattern.type) {
      case BehaviorPatternType.excessiveUsage:
        final appName = pattern.data['appName'] as String;
        final dailyMinutes = pattern.data['dailyMinutes'] as int;
        return SmartRecommendation.reduceAppUsage(
          userId: _defaultUserId,
          appName: appName,
          dailyMinutes: dailyMinutes,
          recommendedLimit: (dailyMinutes * 0.8).round(),
          relevanceScore: pattern.confidence,
        );

      case BehaviorPatternType.frequentDistraction:
        return SmartRecommendation.focusModeRecommendation(
          userId: _defaultUserId,
          focusModeName: 'وضع التركيز',
          reason: 'لتقليل التشتت المتكرر',
          relevanceScore: pattern.confidence,
        );

      case BehaviorPatternType.nightUsage:
        final nightMinutes = pattern.data['nightMinutes'] as int;
        return SmartRecommendation.sleepImprovementRecommendation(
          userId: _defaultUserId,
          nightUsageMinutes: nightMinutes,
          relevanceScore: pattern.confidence,
        );

      case BehaviorPatternType.highProductivity:
        return SmartRecommendation.create(
          userId: _defaultUserId,
          type: RecommendationType.productivityTip,
          title: 'إنتاجية ممتازة!',
          description: 'أنت في حالة إنتاجية عالية. استمر في هذا الأداء الرائع!',
          actionText: 'مشاركة الإنجاز',
          actionData: 'share_achievement',
          priority: RecommendationPriority.low,
          relevanceScore: pattern.confidence,
          tags: ['إنجاز', 'تحفيز'],
        );

      default:
        return null;
    }
  }

  /// توليد التوصيات بناءً على الاستخدام
  Future<List<SmartRecommendation>> _generateUsageBasedRecommendations() async {
    final recommendations = <SmartRecommendation>[];

    try {
      // محاكاة تحليل الاستخدام الحالي
      final random = Random();
      final currentScreenTime = random.nextInt(300) + 60; // 60-360 دقيقة

      // إذا كان وقت الشاشة مرتفع
      if (currentScreenTime > 240) { // أكثر من 4 ساعات
        recommendations.add(
          SmartRecommendation.breakRecommendation(
            userId: _defaultUserId,
            workMinutes: currentScreenTime,
            breakType: 'استراحة طويلة',
            relevanceScore: 0.9,
          ),
        );
      }

      // إذا كان الاستخدام متوسط
      if (currentScreenTime > 120 && currentScreenTime <= 240) {
        recommendations.add(
          SmartRecommendation.create(
            userId: _defaultUserId,
            type: RecommendationType.healthReminder,
            title: 'تذكير صحي',
            description: 'تذكر أن تأخذ استراحة كل ساعة وتحرك قليلاً',
            actionText: 'تعيين تذكير',
            actionData: 'set_break_reminder',
            priority: RecommendationPriority.medium,
            relevanceScore: 0.7,
            tags: ['صحة', 'استراحة'],
          ),
        );
      }
    } catch (e) {
      print('خطأ في توليد التوصيات الاستخدامية: $e');
    }

    return recommendations;
  }

  /// توليد التوصيات بناءً على الأهداف
  Future<List<SmartRecommendation>> generateGoalBasedRecommendations() async {
    final recommendations = <SmartRecommendation>[];

    try {
      final activeGoals = await _databaseHelper.getActiveAdaptiveGoals(_defaultUserId);

      for (final goalData in activeGoals) {
        final goal = AdaptiveGoal.fromDatabase(goalData);
        final recommendation = await _createRecommendationFromGoal(goal);
        if (recommendation != null) {
          recommendations.add(recommendation);
          await _databaseHelper.insertSmartRecommendation(recommendation.toDatabase());
        }
      }
    } catch (e) {
      print('خطأ في توليد التوصيات الهدفية: $e');
    }

    return recommendations;
  }

  /// إنشاء توصية من هدف
  Future<SmartRecommendation?> _createRecommendationFromGoal(AdaptiveGoal goal) async {
    final progressPercentage = goal.progressPercentage;

    // إذا كان التقدم بطيء (أقل من 30%)
    if (progressPercentage < 30 && goal.daysRemaining > 0) {
      return SmartRecommendation.create(
        userId: _defaultUserId,
        type: RecommendationType.productivityTip,
        title: 'تحفيز للهدف: ${goal.title}',
        description: 'تقدمك ${progressPercentage.toStringAsFixed(1)}%. تحتاج لزيادة الجهد لتحقيق هدفك.',
        actionText: 'عرض الهدف',
        actionData: 'view_goal_${goal.id}',
        priority: RecommendationPriority.high,
        relevanceScore: 0.8,
        tags: ['هدف', 'تحفيز'],
      );
    }

    // إذا كان التقدم جيد (70-90%)
    if (progressPercentage >= 70 && progressPercentage < 90) {
      return SmartRecommendation.create(
        userId: _defaultUserId,
        type: RecommendationType.productivityTip,
        title: 'أنت قريب من الهدف!',
        description: 'تقدمك ${progressPercentage.toStringAsFixed(1)}% في "${goal.title}". دفعة أخيرة!',
        actionText: 'عرض الهدف',
        actionData: 'view_goal_${goal.id}',
        priority: RecommendationPriority.medium,
        relevanceScore: 0.9,
        tags: ['هدف', 'تشجيع'],
      );
    }

    // إذا تم تحقيق الهدف
    if (progressPercentage >= 100) {
      return SmartRecommendation.create(
        userId: _defaultUserId,
        type: RecommendationType.productivityTip,
        title: 'تهانينا! 🎉',
        description: 'لقد حققت هدف "${goal.title}" بنجاح!',
        actionText: 'مشاركة الإنجاز',
        actionData: 'share_goal_achievement_${goal.id}',
        priority: RecommendationPriority.low,
        relevanceScore: 1.0,
        tags: ['إنجاز', 'تهنئة'],
      );
    }

    return null;
  }

  /// توليد التوصيات الزمنية
  Future<List<SmartRecommendation>> generateTimeBasedRecommendations() async {
    final recommendations = <SmartRecommendation>[];

    try {
      final now = DateTime.now();

      // توصيات يوم الاثنين
      if (now.weekday == DateTime.monday) {
        recommendations.add(
          SmartRecommendation.personalChallengeRecommendation(
            userId: _defaultUserId,
            challengeTitle: 'تحدي الأسبوع',
            challengeDescription: 'حدد 3 أهداف لهذا الأسبوع واعمل على تحقيقها',
            durationDays: 7,
            relevanceScore: 0.8,
          ),
        );
      }

      // توصيات نهاية الأسبوع
      if (now.weekday == DateTime.friday) {
        recommendations.add(
          SmartRecommendation.create(
            userId: _defaultUserId,
            type: RecommendationType.improveHabits,
            title: 'مراجعة الأسبوع',
            description: 'راجع إنجازاتك هذا الأسبوع وخطط للأسبوع القادم',
            actionText: 'عرض التقرير الأسبوعي',
            actionData: 'view_weekly_report',
            priority: RecommendationPriority.medium,
            relevanceScore: 0.7,
            tags: ['مراجعة', 'تخطيط'],
          ),
        );
      }

      // حفظ التوصيات
      for (final recommendation in recommendations) {
        await _databaseHelper.insertSmartRecommendation(recommendation.toDatabase());
      }
    } catch (e) {
      print('خطأ في توليد التوصيات الزمنية: $e');
    }

    return recommendations;
  }

  /// الحصول على التوصيات النشطة
  Future<List<SmartRecommendation>> getActiveRecommendations() async {
    try {
      final recommendationsData = await _databaseHelper.getActiveRecommendations(_defaultUserId);
      return recommendationsData
          .map((data) => SmartRecommendation.fromDatabase(data))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على التوصيات النشطة: $e');
    }
  }

  /// تحديد التوصية كمقروءة
  Future<void> markRecommendationAsRead(int recommendationId) async {
    try {
      final recommendationsData = await _databaseHelper.getActiveRecommendations(_defaultUserId);
      final recommendationData = recommendationsData.firstWhere(
        (data) => data['id'] == recommendationId,
      );
      
      final recommendation = SmartRecommendation.fromDatabase(recommendationData);
      final readRecommendation = recommendation.markAsRead();
      
      await _databaseHelper.updateSmartRecommendation(
        recommendationId,
        readRecommendation.toDatabase(),
      );
    } catch (e) {
      throw Exception('فشل في تحديد التوصية كمقروءة: $e');
    }
  }

  /// تنفيذ التوصية
  Future<void> executeRecommendation(int recommendationId) async {
    try {
      final recommendationsData = await _databaseHelper.getActiveRecommendations(_defaultUserId);
      final recommendationData = recommendationsData.firstWhere(
        (data) => data['id'] == recommendationId,
      );
      
      final recommendation = SmartRecommendation.fromDatabase(recommendationData);
      final actionedRecommendation = recommendation.markAsActioned();
      
      await _databaseHelper.updateSmartRecommendation(
        recommendationId,
        actionedRecommendation.toDatabase(),
      );
    } catch (e) {
      throw Exception('فشل في تنفيذ التوصية: $e');
    }
  }

  /// التحقق من صلة التوصية
  Future<bool> _isRecommendationRelevant(SmartRecommendation recommendation) async {
    try {
      // تجنب التوصيات المكررة
      final existingRecommendations = await _databaseHelper.getRecommendationsByType(
        recommendation.userId,
        recommendation.type.value,
      );

      // إذا كان هناك توصية مشابهة في آخر 24 ساعة
      final oneDayAgo = DateTime.now().subtract(const Duration(days: 1));
      final recentSimilar = existingRecommendations.where((data) {
        final existing = SmartRecommendation.fromDatabase(data);
        return existing.createdAt.isAfter(oneDayAgo) && 
               existing.title == recommendation.title;
      });

      return recentSimilar.isEmpty;
    } catch (e) {
      return true; // في حالة الخطأ، اعتبر التوصية ذات صلة
    }
  }

  /// تنظيف التوصيات القديمة
  Future<void> cleanupOldRecommendations() async {
    try {
      await _databaseHelper.deleteExpiredRecommendations();
    } catch (e) {
      print('خطأ في تنظيف التوصيات القديمة: $e');
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _recommendationTimer?.cancel();
  }
}
