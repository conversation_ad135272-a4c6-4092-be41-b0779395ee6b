import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';
import '../database/database_helper.dart';
import '../models/app_usage.dart' as models;
import '../models/screen_time_session.dart';
import '../models/usage_statistics.dart';

/// خدمة تتبع استخدام الهاتف والتطبيقات
class UsageTrackingService {
  static final UsageTrackingService _instance =
      UsageTrackingService._internal();
  factory UsageTrackingService() => _instance;
  UsageTrackingService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  Timer? _trackingTimer;
  ScreenTimeSession? _currentSession;
  bool _isTracking = false;

  /// بدء تتبع الاستخدام
  Future<bool> startTracking() async {
    try {
      // طلب الأذونات المطلوبة
      final hasPermissions = await _requestPermissions();
      if (!hasPermissions) {
        throw Exception('لا توجد أذونات كافية لتتبع الاستخدام');
      }

      _isTracking = true;

      // بدء جلسة وقت الشاشة
      await _startScreenTimeSession();

      // بدء تتبع دوري كل دقيقة
      _trackingTimer = Timer.periodic(
        const Duration(minutes: 1),
        (_) => _collectUsageData(),
      );

      return true;
    } catch (e) {
      _isTracking = false;
      throw Exception('فشل في بدء تتبع الاستخدام: $e');
    }
  }

  /// إيقاف تتبع الاستخدام
  Future<void> stopTracking() async {
    try {
      _isTracking = false;
      _trackingTimer?.cancel();
      _trackingTimer = null;

      // إنهاء جلسة وقت الشاشة الحالية
      if (_currentSession != null) {
        await _endScreenTimeSession();
      }
    } catch (e) {
      throw Exception('فشل في إيقاف تتبع الاستخدام: $e');
    }
  }

  /// طلب الأذونات المطلوبة
  Future<bool> _requestPermissions() async {
    try {
      if (Platform.isAndroid) {
        // التحقق من إذن الوصول لإحصائيات الاستخدام
        bool hasUsagePermission = await _checkUsageStatsPermission();

        // إذا كان الإذن موجود، لا نحتاج لطلبه مرة أخرى
        if (hasUsagePermission) {
          return true;
        }

        // إذا لم يكن الإذن موجود، نرمي استثناء ليتم التعامل معه في المستوى الأعلى
        throw Exception('إذن تتبع الاستخدام غير موجود');
      } else if (Platform.isIOS) {
        // iOS لا يدعم تتبع استخدام التطبيقات بنفس الطريقة
        // يمكن استخدام Screen Time API ولكنه محدود
        return true; // نفترض الموافقة مؤقتاً
      }

      return false;
    } catch (e) {
      throw Exception('فشل في طلب الأذونات: $e');
    }
  }

  /// التحقق من إذن إحصائيات الاستخدام
  Future<bool> _checkUsageStatsPermission() async {
    try {
      if (Platform.isAndroid) {
        // استخدام MethodChannel للتحقق من الإذن
        const platform = MethodChannel(
          'com.example.adhd_task_manager/permissions',
        );
        final hasPermission = await platform.invokeMethod(
          'checkUsageStatsPermission',
        );
        return hasPermission as bool;
      }
      return true; // iOS
    } catch (e) {
      return false;
    }
  }

  /// طلب إذن إحصائيات الاستخدام
  Future<void> _requestUsageStatsPermission() async {
    try {
      if (Platform.isAndroid) {
        // استخدام MethodChannel لفتح إعدادات الاستخدام
        const platform = MethodChannel(
          'com.example.adhd_task_manager/permissions',
        );
        await platform.invokeMethod('openUsageAccessSettings');
      }
    } catch (e) {
      throw Exception('فشل في طلب إذن إحصائيات الاستخدام: $e');
    }
  }

  /// جمع بيانات الاستخدام
  Future<void> _collectUsageData() async {
    try {
      if (!_isTracking) return;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      if (Platform.isAndroid) {
        // جمع بيانات استخدام التطبيقات
        await _collectAppUsageData(today);
      }

      // تحديث جلسة وقت الشاشة
      await _updateScreenTimeSession();
    } catch (e) {
      // تسجيل الخطأ ولكن عدم إيقاف التتبع
      print('خطأ في جمع بيانات الاستخدام: $e');
    }
  }

  /// جمع بيانات استخدام التطبيقات (Android فقط)
  Future<void> _collectAppUsageData(DateTime date) async {
    try {
      final endTime = DateTime.now();
      final startTime = DateTime(date.year, date.month, date.day);

      try {
        // محاكاة بيانات الاستخدام للاختبار
        final random = Random();

        // قائمة التطبيقات الشائعة للمحاكاة
        final commonApps = [
          {'package': 'com.facebook.katana', 'name': 'Facebook'},
          {'package': 'com.instagram.android', 'name': 'Instagram'},
          {'package': 'com.google.android.youtube', 'name': 'YouTube'},
          {'package': 'com.whatsapp', 'name': 'WhatsApp'},
          {
            'package': 'com.google.android.apps.docs.editors.docs',
            'name': 'Google Docs',
          },
          {'package': 'com.microsoft.office.word', 'name': 'Microsoft Word'},
          {'package': 'com.netflix.mediaclient', 'name': 'Netflix'},
          {'package': 'com.spotify.music', 'name': 'Spotify'},
        ];

        for (final app in commonApps) {
          // محاكاة وقت استخدام عشوائي (0-120 دقيقة)
          final usageMinutes = random.nextInt(120);
          if (usageMinutes == 0) continue; // تخطي التطبيقات غير المستخدمة

          final usageMilliseconds = usageMinutes * 60 * 1000;
          final appStartTime = startTime.add(
            Duration(minutes: random.nextInt(60)),
          );

          final appUsage = models.AppUsage.create(
            packageName: app['package']!,
            appName: app['name']!,
            date: date,
            totalTimeInForeground: usageMilliseconds,
            launchCount: random.nextInt(10) + 1,
            firstTimeStamp: appStartTime,
            lastTimeStamp: endTime,
            category: _categorizeApp(app['package']!),
          );

          // حفظ في قاعدة البيانات
          await _databaseHelper.insertOrUpdateAppUsage(appUsage.toDatabase());
        }

        print('تم جمع بيانات ${commonApps.length} تطبيق (محاكاة)');
      } catch (e) {
        // في حالة عدم توفر البيانات أو عدم وجود أذونات
        print('تعذر الحصول على بيانات استخدام التطبيقات: $e');
      }
    } catch (e) {
      throw Exception('فشل في جمع بيانات استخدام التطبيقات: $e');
    }
  }

  /// تصنيف التطبيق حسب اسم الحزمة
  String _categorizeApp(String packageName) {
    // قاموس تصنيف التطبيقات الشائعة
    final categories = {
      // تطبيقات التواصل الاجتماعي
      'com.facebook.katana': 'social',
      'com.instagram.android': 'social',
      'com.twitter.android': 'social',
      'com.snapchat.android': 'social',
      'com.linkedin.android': 'social',
      'com.whatsapp': 'social',
      'com.telegram.messenger': 'social',

      // تطبيقات الترفيه
      'com.google.android.youtube': 'entertainment',
      'com.netflix.mediaclient': 'entertainment',
      'com.spotify.music': 'music',
      'com.amazon.avod.thirdpartyclient': 'entertainment',

      // الألعاب
      'com.king.candycrushsaga': 'games',
      'com.supercell.clashofclans': 'games',
      'com.mojang.minecraftpe': 'games',

      // الإنتاجية
      'com.microsoft.office.word': 'productivity',
      'com.google.android.apps.docs.editors.docs': 'productivity',
      'com.microsoft.office.excel': 'productivity',
      'com.google.android.apps.docs.editors.sheets': 'productivity',
      'com.evernote': 'productivity',
      'com.todoist': 'productivity',

      // التعليم
      'com.duolingo': 'education',
      'com.khanacademy.android': 'education',
      'com.coursera.android': 'education',

      // الصحة
      'com.fitbit.FitbitMobile': 'health',
      'com.myfitnesspal.android': 'health',
      'com.headspace.android': 'health',
    };

    return categories[packageName] ?? 'other';
  }

  /// بدء جلسة وقت الشاشة
  Future<void> _startScreenTimeSession() async {
    try {
      _currentSession = ScreenTimeSession.create(
        startTime: DateTime.now(),
        sessionType: 'active',
      );

      final sessionId = await _databaseHelper.insertScreenTimeSession(
        _currentSession!.toDatabase(),
      );

      _currentSession = _currentSession!.copyWith(id: sessionId);
    } catch (e) {
      throw Exception('فشل في بدء جلسة وقت الشاشة: $e');
    }
  }

  /// تحديث جلسة وقت الشاشة
  Future<void> _updateScreenTimeSession() async {
    try {
      if (_currentSession == null) return;

      // تحديث الجلسة الحالية
      final updatedSession = _currentSession!.copyWith(
        updatedAt: DateTime.now(),
      );

      await _databaseHelper.updateScreenTimeSession(
        _currentSession!.id!,
        updatedSession.toDatabase(),
      );

      _currentSession = updatedSession;
    } catch (e) {
      throw Exception('فشل في تحديث جلسة وقت الشاشة: $e');
    }
  }

  /// إنهاء جلسة وقت الشاشة
  Future<void> _endScreenTimeSession() async {
    try {
      if (_currentSession == null) return;

      final endedSession = _currentSession!.endSession();

      await _databaseHelper.updateScreenTimeSession(
        _currentSession!.id!,
        endedSession.toDatabase(),
      );

      _currentSession = null;
    } catch (e) {
      throw Exception('فشل في إنهاء جلسة وقت الشاشة: $e');
    }
  }

  /// الحصول على إحصائيات الاستخدام لتاريخ محدد
  Future<UsageStatistics> getUsageStatistics(DateTime date) async {
    try {
      // الحصول على بيانات استخدام التطبيقات
      final appUsageData = await _databaseHelper.getAppUsageByDate(date);
      final appUsages =
          appUsageData
              .map((data) => models.AppUsage.fromDatabase(data))
              .toList();

      // الحصول على بيانات جلسات وقت الشاشة
      final screenTimeData = await _databaseHelper.getScreenTimeSessionsByDate(
        date,
      );
      final screenTimeSessions =
          screenTimeData
              .map((data) => ScreenTimeSession.fromDatabase(data))
              .toList();

      // إنشاء إحصائيات وقت الشاشة اليومية
      final screenTimeStats = DailyScreenTimeStats.fromSessions(
        date,
        screenTimeSessions,
      );

      // إنشاء إحصائيات الاستخدام الشاملة
      return UsageStatistics.fromData(
        date: date,
        appUsages: appUsages,
        screenTimeStats: screenTimeStats,
      );
    } catch (e) {
      throw Exception('فشل في الحصول على إحصائيات الاستخدام: $e');
    }
  }

  /// الحصول على إحصائيات أسبوعية
  Future<WeeklyUsageStatistics> getWeeklyStatistics(DateTime weekStart) async {
    try {
      final weekEnd = weekStart.add(const Duration(days: 6));
      final dailyStats = <UsageStatistics>[];

      // جمع الإحصائيات اليومية للأسبوع
      for (int i = 0; i < 7; i++) {
        final date = weekStart.add(Duration(days: i));
        final dayStats = await getUsageStatistics(date);
        dailyStats.add(dayStats);
      }

      return WeeklyUsageStatistics.fromDailyStats(dailyStats);
    } catch (e) {
      throw Exception('فشل في الحصول على الإحصائيات الأسبوعية: $e');
    }
  }

  /// ربط جلسة وقت الشاشة بجلسة تركيز
  Future<void> linkToFocusSession(int focusSessionId) async {
    try {
      if (_currentSession != null) {
        final linkedSession = _currentSession!.update(
          sessionType: 'focus',
          focusSessionId: focusSessionId,
        );

        await _databaseHelper.updateScreenTimeSession(
          _currentSession!.id!,
          linkedSession.toDatabase(),
        );

        _currentSession = linkedSession;
      }
    } catch (e) {
      throw Exception('فشل في ربط جلسة وقت الشاشة بجلسة التركيز: $e');
    }
  }

  /// ربط جلسة وقت الشاشة بمهمة
  Future<void> linkToTask(int taskId) async {
    try {
      if (_currentSession != null) {
        final linkedSession = _currentSession!.update(taskId: taskId);

        await _databaseHelper.updateScreenTimeSession(
          _currentSession!.id!,
          linkedSession.toDatabase(),
        );

        _currentSession = linkedSession;
      }
    } catch (e) {
      throw Exception('فشل في ربط جلسة وقت الشاشة بالمهمة: $e');
    }
  }

  /// التحقق من حالة التتبع
  bool get isTracking => _isTracking;

  /// الحصول على الجلسة الحالية
  ScreenTimeSession? get currentSession => _currentSession;

  /// التحقق من أذونات تتبع الاستخدام (طريقة عامة)
  Future<bool> checkUsagePermissions() async {
    return await _checkUsageStatsPermission();
  }

  /// تنظيف البيانات القديمة
  Future<void> cleanupOldData({int daysToKeep = 30}) async {
    try {
      await _databaseHelper.deleteOldAppUsage(daysToKeep);
      // يمكن إضافة تنظيف جلسات وقت الشاشة القديمة هنا
    } catch (e) {
      throw Exception('فشل في تنظيف البيانات القديمة: $e');
    }
  }
}
