import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/focus_session.dart';
import '../services/pomodoro_service.dart';
import '../database/database_helper.dart';

// Provider لخدمة Pomodoro
final pomodoroServiceProvider = Provider<PomodoroService>((ref) {
  final service = PomodoroService();
  service.initialize();
  
  // تنظيف الموارد عند التخلص من الـ provider
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

// Provider للجلسة الحالية
final currentSessionProvider = StreamProvider<FocusSession?>((ref) {
  final service = ref.read(pomodoroServiceProvider);
  return service.sessionStream;
});

// Provider للمؤقت
final timerProvider = StreamProvider<Duration>((ref) {
  final service = ref.read(pomodoroServiceProvider);
  return service.timerStream;
});

// Provider لإعدادات Pomodoro
final pomodoroSettingsProvider = StreamProvider<PomodoroSettings>((ref) {
  final service = ref.read(pomodoroServiceProvider);
  return service.settingsStream;
});

// Provider لجلسات اليوم
final todaySessionsProvider = FutureProvider<List<FocusSession>>((ref) async {
  final service = ref.read(pomodoroServiceProvider);
  return await service.getTodaySessions();
});

// Provider لجميع جلسات التركيز
final allFocusSessionsProvider = FutureProvider<List<FocusSession>>((ref) async {
  final databaseHelper = DatabaseHelper();
  final sessionsData = await databaseHelper.getAllFocusSessions();
  
  return sessionsData
      .map((data) => FocusSession.fromDatabase(data))
      .toList();
});

// Provider لجلسات التركيز حسب التاريخ
final focusSessionsByDateProvider = FutureProvider.family<List<FocusSession>, DateTime>((ref, date) async {
  final databaseHelper = DatabaseHelper();
  final sessionsData = await databaseHelper.getFocusSessionsByDate(date);
  
  return sessionsData
      .map((data) => FocusSession.fromDatabase(data))
      .toList();
});

// Provider لإحصائيات التركيز
final focusStatisticsProvider = FutureProvider<FocusStatistics>((ref) async {
  final databaseHelper = DatabaseHelper();
  final allSessionsData = await databaseHelper.getAllFocusSessions();
  final allSessions = allSessionsData
      .map((data) => FocusSession.fromDatabase(data))
      .toList();
  
  return FocusStatistics.fromSessions(allSessions);
});

// Provider لإحصائيات اليوم
final todayStatisticsProvider = FutureProvider<DailyFocusStatistics>((ref) async {
  final today = DateTime.now();
  final sessions = await ref.read(focusSessionsByDateProvider(today).future);
  
  return DailyFocusStatistics.fromSessions(sessions, today);
});

// Provider لإحصائيات الأسبوع
final weeklyStatisticsProvider = FutureProvider<WeeklyFocusStatistics>((ref) async {
  final now = DateTime.now();
  final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
  
  final weekSessions = <FocusSession>[];
  for (int i = 0; i < 7; i++) {
    final date = startOfWeek.add(Duration(days: i));
    final daySessions = await ref.read(focusSessionsByDateProvider(date).future);
    weekSessions.addAll(daySessions);
  }
  
  return WeeklyFocusStatistics.fromSessions(weekSessions, startOfWeek);
});

// Notifier لإدارة جلسات التركيز
class FocusSessionsNotifier extends StateNotifier<AsyncValue<List<FocusSession>>> {
  FocusSessionsNotifier(this._databaseHelper) : super(const AsyncValue.loading()) {
    loadSessions();
  }

  final DatabaseHelper _databaseHelper;

  // تحميل الجلسات
  Future<void> loadSessions() async {
    state = const AsyncValue.loading();
    try {
      final sessionsData = await _databaseHelper.getAllFocusSessions();
      final sessions = sessionsData
          .map((data) => FocusSession.fromDatabase(data))
          .toList();
      state = AsyncValue.data(sessions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // حذف جلسة
  Future<void> deleteSession(int id) async {
    try {
      await _databaseHelper.deleteFocusSession(id);
      await loadSessions(); // إعادة تحميل الجلسات
    } catch (error) {
      rethrow;
    }
  }
}

// Provider للـ FocusSessionsNotifier
final focusSessionsNotifierProvider = StateNotifierProvider<FocusSessionsNotifier, AsyncValue<List<FocusSession>>>((ref) {
  return FocusSessionsNotifier(DatabaseHelper());
});

// فئة إحصائيات التركيز العامة
class FocusStatistics {
  final int totalSessions;
  final int completedSessions;
  final int cancelledSessions;
  final int totalFocusMinutes;
  final int totalBreakMinutes;
  final double averageSessionDuration;
  final double completionRate;
  final Map<FocusSessionType, int> sessionsByType;

  const FocusStatistics({
    required this.totalSessions,
    required this.completedSessions,
    required this.cancelledSessions,
    required this.totalFocusMinutes,
    required this.totalBreakMinutes,
    required this.averageSessionDuration,
    required this.completionRate,
    required this.sessionsByType,
  });

  factory FocusStatistics.fromSessions(List<FocusSession> sessions) {
    final totalSessions = sessions.length;
    final completedSessions = sessions.where((s) => s.status == FocusSessionStatus.completed).length;
    final cancelledSessions = sessions.where((s) => s.status == FocusSessionStatus.cancelled).length;
    
    int totalFocusMinutes = 0;
    int totalBreakMinutes = 0;
    int totalDuration = 0;
    
    final sessionsByType = <FocusSessionType, int>{};
    for (final type in FocusSessionType.values) {
      sessionsByType[type] = 0;
    }
    
    for (final session in sessions) {
      sessionsByType[session.type] = (sessionsByType[session.type] ?? 0) + 1;
      
      if (session.actualDuration != null) {
        totalDuration += session.actualDuration!;
        
        if (session.type == FocusSessionType.work) {
          totalFocusMinutes += session.actualDuration!;
        } else {
          totalBreakMinutes += session.actualDuration!;
        }
      }
    }
    
    final averageSessionDuration = totalSessions > 0 ? totalDuration / totalSessions : 0.0;
    final completionRate = totalSessions > 0 ? completedSessions / totalSessions : 0.0;
    
    return FocusStatistics(
      totalSessions: totalSessions,
      completedSessions: completedSessions,
      cancelledSessions: cancelledSessions,
      totalFocusMinutes: totalFocusMinutes,
      totalBreakMinutes: totalBreakMinutes,
      averageSessionDuration: averageSessionDuration,
      completionRate: completionRate,
      sessionsByType: sessionsByType,
    );
  }
}

// فئة إحصائيات التركيز اليومية
class DailyFocusStatistics {
  final DateTime date;
  final int workSessions;
  final int breakSessions;
  final int totalFocusMinutes;
  final int completedSessions;
  final double productivityScore;

  const DailyFocusStatistics({
    required this.date,
    required this.workSessions,
    required this.breakSessions,
    required this.totalFocusMinutes,
    required this.completedSessions,
    required this.productivityScore,
  });

  factory DailyFocusStatistics.fromSessions(List<FocusSession> sessions, DateTime date) {
    final workSessions = sessions.where((s) => s.type == FocusSessionType.work).length;
    final breakSessions = sessions.where((s) => s.type != FocusSessionType.work).length;
    final completedSessions = sessions.where((s) => s.status == FocusSessionStatus.completed).length;
    
    int totalFocusMinutes = 0;
    for (final session in sessions) {
      if (session.type == FocusSessionType.work && session.actualDuration != null) {
        totalFocusMinutes += session.actualDuration!;
      }
    }
    
    // حساب نقاط الإنتاجية (من 0 إلى 100)
    final targetSessions = 8; // هدف 8 جلسات عمل يومياً
    final targetMinutes = 200; // هدف 200 دقيقة تركيز يومياً
    
    final sessionScore = (workSessions / targetSessions * 50).clamp(0, 50);
    final minutesScore = (totalFocusMinutes / targetMinutes * 50).clamp(0, 50);
    final productivityScore = sessionScore + minutesScore;
    
    return DailyFocusStatistics(
      date: date,
      workSessions: workSessions,
      breakSessions: breakSessions,
      totalFocusMinutes: totalFocusMinutes,
      completedSessions: completedSessions,
      productivityScore: productivityScore.toDouble(),
    );
  }
}

// فئة إحصائيات التركيز الأسبوعية
class WeeklyFocusStatistics {
  final DateTime weekStart;
  final List<DailyFocusStatistics> dailyStats;
  final int totalWorkSessions;
  final int totalFocusMinutes;
  final double averageDailyProductivity;

  const WeeklyFocusStatistics({
    required this.weekStart,
    required this.dailyStats,
    required this.totalWorkSessions,
    required this.totalFocusMinutes,
    required this.averageDailyProductivity,
  });

  factory WeeklyFocusStatistics.fromSessions(List<FocusSession> sessions, DateTime weekStart) {
    final dailyStats = <DailyFocusStatistics>[];
    int totalWorkSessions = 0;
    int totalFocusMinutes = 0;
    
    for (int i = 0; i < 7; i++) {
      final date = weekStart.add(Duration(days: i));
      final daySessions = sessions.where((s) {
        final sessionDate = DateTime(s.startTime.year, s.startTime.month, s.startTime.day);
        final targetDate = DateTime(date.year, date.month, date.day);
        return sessionDate.isAtSameMomentAs(targetDate);
      }).toList();
      
      final dayStats = DailyFocusStatistics.fromSessions(daySessions, date);
      dailyStats.add(dayStats);
      
      totalWorkSessions += dayStats.workSessions;
      totalFocusMinutes += dayStats.totalFocusMinutes;
    }
    
    final averageDailyProductivity = dailyStats.isNotEmpty 
        ? dailyStats.map((s) => s.productivityScore).reduce((a, b) => a + b) / dailyStats.length
        : 0.0;
    
    return WeeklyFocusStatistics(
      weekStart: weekStart,
      dailyStats: dailyStats,
      totalWorkSessions: totalWorkSessions,
      totalFocusMinutes: totalFocusMinutes,
      averageDailyProductivity: averageDailyProductivity,
    );
  }
}
