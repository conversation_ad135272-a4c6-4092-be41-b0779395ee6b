import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/usage_tracking_service.dart';
import '../services/permissions_service.dart';
import '../services/real_app_usage_service.dart';
import '../models/app_usage.dart';
import '../models/app_usage_data.dart';
import '../models/screen_time_session.dart';
import '../models/usage_statistics.dart';

// Provider لخدمة تتبع الاستخدام
final usageTrackingServiceProvider = Provider<UsageTrackingService>((ref) {
  return UsageTrackingService();
});

// Provider للخدمة الحقيقية لتتبع الاستخدام
final realAppUsageServiceProvider = Provider<RealAppUsageService>((ref) {
  return RealAppUsageService();
});

// Provider للتطبيقات الحقيقية مع إمكانية التحديث
final realAppsProvider = FutureProvider.autoDispose<List<AppUsageData>>((
  ref,
) async {
  final service = ref.read(realAppUsageServiceProvider);

  // مراقبة مزود التحديث لإعادة تحميل البيانات عند الحاجة
  ref.watch(refreshTriggerProvider);

  try {
    // طلب الأذونات أولاً
    final hasPermissions = await service.requestPermissions();
    if (!hasPermissions) {
      return [];
    }

    // الحصول على بيانات الاستخدام لليوم الحالي
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final apps = await service.getAppUsageStats(
      startDate: startOfDay,
      endDate: endOfDay,
    );

    // ترتيب التطبيقات من الأعلى استخداماً إلى الأقل
    apps.sort((a, b) => b.usageTime.compareTo(a.usageTime));

    return apps;
  } catch (e) {
    print('خطأ في تحميل التطبيقات الحقيقية: $e');
    return [];
  }
});

// Provider لتحديث البيانات
final refreshTriggerProvider = StateProvider<int>((ref) => 0);

// Provider لحالة التتبع
final usageTrackingStateProvider = StateProvider<bool>((ref) => false);

// Notifier لإدارة إحصائيات الاستخدام
class UsageStatisticsNotifier extends AsyncNotifier<UsageStatistics> {
  UsageStatisticsNotifier();

  late final UsageTrackingService _usageService;
  DateTime _currentDate = DateTime.now();

  @override
  Future<UsageStatistics> build() async {
    _usageService = ref.read(usageTrackingServiceProvider);
    return await _loadUsageStatistics(_currentDate);
  }

  /// تحميل إحصائيات الاستخدام لتاريخ محدد
  Future<UsageStatistics> _loadUsageStatistics(DateTime date) async {
    try {
      return await _usageService.getUsageStatistics(date);
    } catch (e) {
      // في حالة عدم وجود بيانات، نعيد إحصائيات فارغة
      return UsageStatistics.fromData(
        date: date,
        appUsages: [],
        screenTimeStats: null,
      );
    }
  }

  /// تحديث التاريخ المحدد
  Future<void> updateDate(DateTime date) async {
    _currentDate = date;
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadUsageStatistics(date));
  }

  /// إعادة تحميل الإحصائيات
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadUsageStatistics(_currentDate));
  }

  /// الحصول على التاريخ الحالي
  DateTime get currentDate => _currentDate;
}

// Provider لإحصائيات الاستخدام
final usageStatisticsProvider =
    AsyncNotifierProvider<UsageStatisticsNotifier, UsageStatistics>(() {
      return UsageStatisticsNotifier();
    });

// Notifier لإدارة الإحصائيات الأسبوعية
class WeeklyUsageStatisticsNotifier
    extends AsyncNotifier<WeeklyUsageStatistics> {
  WeeklyUsageStatisticsNotifier();

  late final UsageTrackingService _usageService;
  DateTime _currentWeekStart = _getWeekStart(DateTime.now());

  @override
  Future<WeeklyUsageStatistics> build() async {
    _usageService = ref.read(usageTrackingServiceProvider);
    return await _loadWeeklyStatistics(_currentWeekStart);
  }

  /// تحميل الإحصائيات الأسبوعية
  Future<WeeklyUsageStatistics> _loadWeeklyStatistics(
    DateTime weekStart,
  ) async {
    try {
      return await _usageService.getWeeklyStatistics(weekStart);
    } catch (e) {
      // في حالة عدم وجود بيانات، نعيد إحصائيات فارغة
      final emptyDailyStats = <UsageStatistics>[];
      for (int i = 0; i < 7; i++) {
        final date = weekStart.add(Duration(days: i));
        emptyDailyStats.add(
          UsageStatistics.fromData(
            date: date,
            appUsages: [],
            screenTimeStats: null,
          ),
        );
      }
      return WeeklyUsageStatistics.fromDailyStats(emptyDailyStats);
    }
  }

  /// تحديث الأسبوع المحدد
  Future<void> updateWeek(DateTime weekStart) async {
    _currentWeekStart = weekStart;
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadWeeklyStatistics(weekStart));
  }

  /// الانتقال للأسبوع السابق
  Future<void> previousWeek() async {
    final previousWeek = _currentWeekStart.subtract(const Duration(days: 7));
    await updateWeek(previousWeek);
  }

  /// الانتقال للأسبوع التالي
  Future<void> nextWeek() async {
    final nextWeek = _currentWeekStart.add(const Duration(days: 7));
    await updateWeek(nextWeek);
  }

  /// إعادة تحميل الإحصائيات
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => _loadWeeklyStatistics(_currentWeekStart),
    );
  }

  /// الحصول على بداية الأسبوع الحالي
  DateTime get currentWeekStart => _currentWeekStart;

  /// الحصول على نهاية الأسبوع الحالي
  DateTime get currentWeekEnd => _currentWeekStart.add(const Duration(days: 6));

  /// حساب بداية الأسبوع لتاريخ معين
  static DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }
}

// Provider للإحصائيات الأسبوعية
final weeklyUsageStatisticsProvider =
    AsyncNotifierProvider<WeeklyUsageStatisticsNotifier, WeeklyUsageStatistics>(
      () {
        return WeeklyUsageStatisticsNotifier();
      },
    );

// Provider لأهم التطبيقات المستخدمة اليوم (البيانات الحقيقية)
final topAppsProvider = FutureProvider<List<AppUsageData>>((ref) async {
  final realApps = await ref.watch(realAppsProvider.future);

  // ترتيب التطبيقات حسب وقت الاستخدام وأخذ أول 10
  final topApps =
      realApps.where((app) => app.usageTime.inMinutes > 0).toList()
        ..sort((a, b) => b.usageTime.compareTo(a.usageTime));

  return topApps.take(10).toList();
});

// Provider للتطبيقات المشتتة (البيانات الحقيقية)
final distractingAppsProvider = FutureProvider<List<AppUsageData>>((ref) async {
  final realApps = await ref.watch(realAppsProvider.future);

  // فلترة التطبيقات المشتتة وترتيبها
  final distractingApps =
      realApps
          .where((app) => app.isDistractingApp && app.usageTime.inMinutes > 0)
          .toList()
        ..sort((a, b) => b.usageTime.compareTo(a.usageTime));

  return distractingApps;
});

// Provider للتطبيقات المنتجة (البيانات الحقيقية)
final productiveAppsProvider = FutureProvider<List<AppUsageData>>((ref) async {
  final realApps = await ref.watch(realAppsProvider.future);

  // فلترة التطبيقات المنتجة وترتيبها
  final productiveApps =
      realApps
          .where((app) => app.isProductiveApp && app.usageTime.inMinutes > 0)
          .toList()
        ..sort((a, b) => b.usageTime.compareTo(a.usageTime));

  return productiveApps;
});

// Provider للإحصائيات الحقيقية مع إمكانية التحديث
final realUsageStatisticsProvider = AutoDisposeFutureProvider<UsageStatistics>((
  ref,
) async {
  final realApps = await ref.watch(realAppsProvider.future);

  // تحويل AppUsageData إلى AppUsage للتوافق مع UsageStatistics
  final appUsages = <AppUsage>[];
  final now = DateTime.now();

  for (final app in realApps) {
    if (app.usageTime.inMinutes > 0) {
      final appUsage = AppUsage.create(
        packageName: app.packageName,
        appName: app.appName,
        date: now,
        totalTimeInForeground: app.usageTime.inMilliseconds,
        launchCount: app.launchCount,
        firstTimeStamp: app.lastTimeUsed ?? now,
        lastTimeStamp: app.lastTimeUsed ?? now,
        category: _mapCategoryToEnglish(app.category),
      );
      appUsages.add(appUsage);
    }
  }

  return UsageStatistics.fromData(date: now, appUsages: appUsages);
});

// دالة مساعدة لتحويل الفئات من العربية إلى الإنجليزية
String _mapCategoryToEnglish(String arabicCategory) {
  switch (arabicCategory) {
    case 'التواصل الاجتماعي':
      return 'social';
    case 'الترفيه':
      return 'entertainment';
    case 'الألعاب':
      return 'games';
    case 'الإنتاجية':
      return 'productivity';
    default:
      return 'other';
  }
}

// Provider للجلسة الحالية لوقت الشاشة
final currentScreenTimeSessionProvider = StateProvider<ScreenTimeSession?>(
  (ref) => null,
);

// Provider لحالة الأذونات
final usagePermissionsProvider = StateProvider<bool>((ref) => false);

// Provider للتاريخ المحدد لعرض الإحصائيات
final selectedDateProvider = StateProvider<DateTime>((ref) => DateTime.now());

// Provider لنوع العرض (يومي، أسبوعي، شهري)
final usageViewTypeProvider = StateProvider<UsageViewType>(
  (ref) => UsageViewType.daily,
);

// Provider لفلتر التطبيقات
final appCategoryFilterProvider = StateProvider<AppCategory?>((ref) => null);

// Provider لترتيب التطبيقات
final appSortModeProvider = StateProvider<AppSortMode>(
  (ref) => AppSortMode.timeDescending,
);

/// أنواع عرض الإحصائيات
enum UsageViewType {
  daily('daily', 'يومي'),
  weekly('weekly', 'أسبوعي'),
  monthly('monthly', 'شهري');

  const UsageViewType(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// أنماط ترتيب التطبيقات
enum AppSortMode {
  timeDescending('time_desc', 'الوقت (تنازلي)'),
  timeAscending('time_asc', 'الوقت (تصاعدي)'),
  nameAscending('name_asc', 'الاسم (أ-ي)'),
  nameDescending('name_desc', 'الاسم (ي-أ)'),
  launchesDescending('launches_desc', 'مرات الفتح (تنازلي)'),
  launchesAscending('launches_asc', 'مرات الفتح (تصاعدي)');

  const AppSortMode(this.value, this.displayName);

  final String value;
  final String displayName;
}

// Provider لبدء/إيقاف التتبع
final usageTrackingControllerProvider = Provider<UsageTrackingController>((
  ref,
) {
  return UsageTrackingController(ref);
});

/// تحكم في تتبع الاستخدام
class UsageTrackingController {
  final Ref _ref;

  UsageTrackingController(this._ref);

  /// التحقق من الأذونات
  Future<bool> checkPermissions() async {
    try {
      final service = _ref.read(usageTrackingServiceProvider);
      // محاولة التحقق من الأذونات دون بدء التتبع
      return await service.checkUsagePermissions();
    } catch (e) {
      return false;
    }
  }

  /// بدء التتبع مع طلب الأذونات
  Future<bool> startTracking({BuildContext? context}) async {
    try {
      final service = _ref.read(usageTrackingServiceProvider);

      // التحقق من الأذونات أولاً
      bool hasPermissions = await checkPermissions();

      // إذا لم تكن الأذونات موجودة، نطلبها من المستخدم
      if (!hasPermissions && context != null && context.mounted) {
        final permissionsService = PermissionsService();
        hasPermissions = await permissionsService
            .requestUsageTrackingPermissions(context);
      }

      // إذا كانت الأذونات موجودة، نبدأ التتبع
      if (hasPermissions) {
        final success = await service.startTracking();
        if (success) {
          _ref.read(usageTrackingStateProvider.notifier).state = true;
          _ref.read(currentScreenTimeSessionProvider.notifier).state =
              service.currentSession;
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// إيقاف التتبع
  Future<void> stopTracking() async {
    try {
      final service = _ref.read(usageTrackingServiceProvider);
      await service.stopTracking();

      _ref.read(usageTrackingStateProvider.notifier).state = false;
      _ref.read(currentScreenTimeSessionProvider.notifier).state = null;
    } catch (e) {
      // تسجيل الخطأ
    }
  }

  /// ربط بجلسة تركيز
  Future<void> linkToFocusSession(int focusSessionId) async {
    try {
      final service = _ref.read(usageTrackingServiceProvider);
      await service.linkToFocusSession(focusSessionId);

      _ref.read(currentScreenTimeSessionProvider.notifier).state =
          service.currentSession;
    } catch (e) {
      // تسجيل الخطأ
    }
  }

  /// ربط بمهمة
  Future<void> linkToTask(int taskId) async {
    try {
      final service = _ref.read(usageTrackingServiceProvider);
      await service.linkToTask(taskId);

      _ref.read(currentScreenTimeSessionProvider.notifier).state =
          service.currentSession;
    } catch (e) {
      // تسجيل الخطأ
    }
  }

  /// تحديث الإحصائيات
  Future<void> refreshStatistics() async {
    _ref.invalidate(usageStatisticsProvider);
    _ref.invalidate(weeklyUsageStatisticsProvider);
    _ref.invalidate(topAppsProvider);
    _ref.invalidate(distractingAppsProvider);
    _ref.invalidate(productiveAppsProvider);
  }
}
