import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/analytics_engine.dart';
import '../services/smart_recommendation_service.dart';
import '../models/user_behavior_pattern.dart';
import '../models/smart_recommendation.dart';
import '../models/adaptive_goal.dart';

// Provider لمحرك التحليلات
final analyticsEngineProvider = Provider<AnalyticsEngine>((ref) {
  return AnalyticsEngine();
});

// Provider لخدمة التوصيات الذكية
final smartRecommendationServiceProvider = Provider<SmartRecommendationService>((ref) {
  return SmartRecommendationService();
});

// Notifier لإدارة أنماط سلوك المستخدم
class UserBehaviorPatternsNotifier extends AsyncNotifier<List<UserBehaviorPattern>> {
  late final AnalyticsEngine _analyticsEngine;

  @override
  Future<List<UserBehaviorPattern>> build() async {
    _analyticsEngine = ref.read(analyticsEngineProvider);
    await _analyticsEngine.initialize();
    return await _loadBehaviorPatterns();
  }

  /// تحميل أنماط السلوك
  Future<List<UserBehaviorPattern>> _loadBehaviorPatterns() async {
    try {
      return await _analyticsEngine.analyzeUserBehavior();
    } catch (e) {
      throw Exception('فشل في تحميل أنماط السلوك: $e');
    }
  }

  /// تحليل السلوك الجديد
  Future<void> analyzeBehavior() async {
    try {
      state = const AsyncValue.loading();
      final patterns = await _analyticsEngine.analyzeUserBehavior();
      state = AsyncValue.data(patterns);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// إعادة تحميل أنماط السلوك
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadBehaviorPatterns());
  }
}

// Provider لأنماط سلوك المستخدم
final userBehaviorPatternsProvider = AsyncNotifierProvider<UserBehaviorPatternsNotifier, List<UserBehaviorPattern>>(() {
  return UserBehaviorPatternsNotifier();
});

// Notifier لإدارة التوصيات الذكية
class SmartRecommendationsNotifier extends AsyncNotifier<List<SmartRecommendation>> {
  late final SmartRecommendationService _recommendationService;

  @override
  Future<List<SmartRecommendation>> build() async {
    _recommendationService = ref.read(smartRecommendationServiceProvider);
    await _recommendationService.initialize();
    return await _loadRecommendations();
  }

  /// تحميل التوصيات النشطة
  Future<List<SmartRecommendation>> _loadRecommendations() async {
    try {
      return await _recommendationService.getActiveRecommendations();
    } catch (e) {
      throw Exception('فشل في تحميل التوصيات: $e');
    }
  }

  /// توليد توصيات جديدة
  Future<void> generateRecommendations() async {
    try {
      await _recommendationService.generateContextualRecommendations();
      await refresh();
    } catch (e) {
      throw Exception('فشل في توليد التوصيات: $e');
    }
  }

  /// تحديد التوصية كمقروءة
  Future<void> markAsRead(int recommendationId) async {
    try {
      await _recommendationService.markRecommendationAsRead(recommendationId);
      await refresh();
    } catch (e) {
      throw Exception('فشل في تحديد التوصية كمقروءة: $e');
    }
  }

  /// تنفيذ التوصية
  Future<void> executeRecommendation(int recommendationId) async {
    try {
      await _recommendationService.executeRecommendation(recommendationId);
      await refresh();
    } catch (e) {
      throw Exception('فشل في تنفيذ التوصية: $e');
    }
  }

  /// إعادة تحميل التوصيات
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadRecommendations());
  }
}

// Provider للتوصيات الذكية
final smartRecommendationsProvider = AsyncNotifierProvider<SmartRecommendationsNotifier, List<SmartRecommendation>>(() {
  return SmartRecommendationsNotifier();
});

// Notifier لإدارة الأهداف التكيفية
class AdaptiveGoalsNotifier extends AsyncNotifier<List<AdaptiveGoal>> {
  late final AnalyticsEngine _analyticsEngine;

  @override
  Future<List<AdaptiveGoal>> build() async {
    _analyticsEngine = ref.read(analyticsEngineProvider);
    return await _loadAdaptiveGoals();
  }

  /// تحميل الأهداف التكيفية
  Future<List<AdaptiveGoal>> _loadAdaptiveGoals() async {
    try {
      // محاكاة تحميل الأهداف - في التطبيق الحقيقي سيتم من قاعدة البيانات
      return [
        AdaptiveGoal.reduceScreenTime(
          userId: 'default_user',
          currentDailyMinutes: 300,
          reductionPercentage: 0.2,
        ),
        AdaptiveGoal.increaseFocusSessions(
          userId: 'default_user',
          currentWeeklySessions: 10,
          increasePercentage: 0.3,
        ),
        AdaptiveGoal.completeDailyTasks(
          userId: 'default_user',
          averageCompletedTasks: 5,
        ),
      ];
    } catch (e) {
      throw Exception('فشل في تحميل الأهداف التكيفية: $e');
    }
  }

  /// إنشاء هدف تكيفي جديد
  Future<void> createAdaptiveGoal(AdaptiveGoal goal) async {
    try {
      // في التطبيق الحقيقي سيتم حفظ الهدف في قاعدة البيانات
      await refresh();
    } catch (e) {
      throw Exception('فشل في إنشاء الهدف التكيفي: $e');
    }
  }

  /// تحديث تقدم الهدف
  Future<void> updateGoalProgress(int goalId, double newValue) async {
    try {
      // في التطبيق الحقيقي سيتم تحديث الهدف في قاعدة البيانات
      await refresh();
    } catch (e) {
      throw Exception('فشل في تحديث تقدم الهدف: $e');
    }
  }

  /// تكييف الأهداف
  Future<void> adaptGoals() async {
    try {
      await _analyticsEngine.updateAdaptiveGoals();
      await refresh();
    } catch (e) {
      throw Exception('فشل في تكييف الأهداف: $e');
    }
  }

  /// إعادة تحميل الأهداف
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadAdaptiveGoals());
  }
}

// Provider للأهداف التكيفية
final adaptiveGoalsProvider = AsyncNotifierProvider<AdaptiveGoalsNotifier, List<AdaptiveGoal>>(() {
  return AdaptiveGoalsNotifier();
});

// Provider للأهداف النشطة فقط
final activeAdaptiveGoalsProvider = Provider<AsyncValue<List<AdaptiveGoal>>>((ref) {
  final goalsAsync = ref.watch(adaptiveGoalsProvider);
  return goalsAsync.when(
    data: (goals) => AsyncValue.data(
      goals.where((goal) => goal.status == AdaptiveGoalStatus.active && !goal.isExpired).toList(),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Provider للتوصيات عالية الأولوية
final highPriorityRecommendationsProvider = Provider<AsyncValue<List<SmartRecommendation>>>((ref) {
  final recommendationsAsync = ref.watch(smartRecommendationsProvider);
  return recommendationsAsync.when(
    data: (recommendations) => AsyncValue.data(
      recommendations
          .where((rec) => rec.priority == RecommendationPriority.high || rec.priority == RecommendationPriority.urgent)
          .toList(),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Provider لأنماط السلوك القوية
final strongBehaviorPatternsProvider = Provider<AsyncValue<List<UserBehaviorPattern>>>((ref) {
  final patternsAsync = ref.watch(userBehaviorPatternsProvider);
  return patternsAsync.when(
    data: (patterns) => AsyncValue.data(
      patterns.where((pattern) => pattern.strength == PatternStrength.strong).toList(),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Provider لإحصائيات الذكاء الاصطناعي
final aiInsightsProvider = Provider<AIInsights>((ref) {
  final patternsAsync = ref.watch(userBehaviorPatternsProvider);
  final recommendationsAsync = ref.watch(smartRecommendationsProvider);
  final goalsAsync = ref.watch(adaptiveGoalsProvider);

  return patternsAsync.when(
    data: (patterns) => recommendationsAsync.when(
      data: (recommendations) => goalsAsync.when(
        data: (goals) => AIInsights(
          totalPatterns: patterns.length,
          strongPatterns: patterns.where((p) => p.strength == PatternStrength.strong).length,
          activeRecommendations: recommendations.length,
          highPriorityRecommendations: recommendations
              .where((r) => r.priority == RecommendationPriority.high || r.priority == RecommendationPriority.urgent)
              .length,
          activeGoals: goals.where((g) => g.status == AdaptiveGoalStatus.active).length,
          completedGoals: goals.where((g) => g.status == AdaptiveGoalStatus.completed).length,
          averageGoalProgress: goals.isNotEmpty 
              ? goals.map((g) => g.progressPercentage).reduce((a, b) => a + b) / goals.length
              : 0.0,
        ),
        loading: () => const AIInsights(),
        error: (_, __) => const AIInsights(),
      ),
      loading: () => const AIInsights(),
      error: (_, __) => const AIInsights(),
    ),
    loading: () => const AIInsights(),
    error: (_, __) => const AIInsights(),
  );
});

// Provider للتحكم في النظام الذكي
final aiControllerProvider = Provider<AIController>((ref) {
  return AIController(ref);
});

/// تحكم في النظام الذكي
class AIController {
  final Ref _ref;
  
  AIController(this._ref);

  /// تحليل شامل للمستخدم
  Future<void> performFullAnalysis() async {
    try {
      // تحليل أنماط السلوك
      await _ref.read(userBehaviorPatternsProvider.notifier).analyzeBehavior();
      
      // توليد توصيات جديدة
      await _ref.read(smartRecommendationsProvider.notifier).generateRecommendations();
      
      // تكييف الأهداف
      await _ref.read(adaptiveGoalsProvider.notifier).adaptGoals();
    } catch (e) {
      throw Exception('فشل في التحليل الشامل: $e');
    }
  }

  /// تنفيذ توصية مع تحديث النظام
  Future<void> executeRecommendationWithUpdate(int recommendationId) async {
    try {
      await _ref.read(smartRecommendationsProvider.notifier).executeRecommendation(recommendationId);
      
      // إعادة تحليل بعد تنفيذ التوصية
      await performFullAnalysis();
    } catch (e) {
      throw Exception('فشل في تنفيذ التوصية: $e');
    }
  }

  /// تحديث جميع البيانات
  Future<void> refreshAll() async {
    await _ref.read(userBehaviorPatternsProvider.notifier).refresh();
    await _ref.read(smartRecommendationsProvider.notifier).refresh();
    await _ref.read(adaptiveGoalsProvider.notifier).refresh();
  }
}

/// إحصائيات الذكاء الاصطناعي
class AIInsights {
  final int totalPatterns;
  final int strongPatterns;
  final int activeRecommendations;
  final int highPriorityRecommendations;
  final int activeGoals;
  final int completedGoals;
  final double averageGoalProgress;

  const AIInsights({
    this.totalPatterns = 0,
    this.strongPatterns = 0,
    this.activeRecommendations = 0,
    this.highPriorityRecommendations = 0,
    this.activeGoals = 0,
    this.completedGoals = 0,
    this.averageGoalProgress = 0.0,
  });

  /// معدل قوة الأنماط
  double get patternStrengthRatio {
    if (totalPatterns == 0) return 0.0;
    return strongPatterns / totalPatterns;
  }

  /// معدل التوصيات عالية الأولوية
  double get highPriorityRatio {
    if (activeRecommendations == 0) return 0.0;
    return highPriorityRecommendations / activeRecommendations;
  }

  /// معدل إكمال الأهداف
  double get goalCompletionRatio {
    final totalGoals = activeGoals + completedGoals;
    if (totalGoals == 0) return 0.0;
    return completedGoals / totalGoals;
  }

  /// تقييم عام للنظام الذكي
  String get overallAssessment {
    final score = (patternStrengthRatio * 0.3 + 
                   (1 - highPriorityRatio) * 0.3 + 
                   goalCompletionRatio * 0.4) * 100;
    
    if (score >= 80) return 'ممتاز';
    if (score >= 60) return 'جيد';
    if (score >= 40) return 'متوسط';
    return 'يحتاج تحسين';
  }
}
