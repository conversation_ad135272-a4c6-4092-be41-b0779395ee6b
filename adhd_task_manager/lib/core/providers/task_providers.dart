import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/task.dart';
import '../services/task_service.dart';

// Provider لخدمة المهام
final taskServiceProvider = Provider<TaskService>((ref) {
  return TaskService();
});

// Provider لجميع المهام
final tasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getAllTasks();
});

// Provider للمهام المعلقة
final pendingTasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getPendingTasks();
});

// Provider للمهام المكتملة
final completedTasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getCompletedTasks();
});

// Provider للمهام مرتبة حسب الأولوية
final tasksSortedByPriorityProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getTasksSortedByPriority();
});

// Provider للمهام العاجلة
final urgentTasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getTasksByPriority(TaskPriority.urgent);
});

// Provider للمهام المهمة
final importantTasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getTasksByPriority(TaskPriority.important);
});

// Provider للمهام العادية
final normalTasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getTasksByPriority(TaskPriority.normal);
});

// Provider لإحصائيات المهام
final taskStatisticsProvider = FutureProvider<TaskStatistics>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getTaskStatistics();
});

// Provider للمهام المستحقة اليوم
final tasksDueTodayProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getTasksDueToday();
});

// Provider للمهام المتأخرة
final overdueTasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  return await taskService.getOverdueTasks();
});

// Provider للبحث في المهام
final searchQueryProvider = StateProvider<String>((ref) => '');

final searchResultsProvider = FutureProvider<List<Task>>((ref) async {
  final query = ref.watch(searchQueryProvider);
  final taskService = ref.read(taskServiceProvider);
  return await taskService.searchTasks(query);
});

// Provider لفلتر الأولوية المحدد
final selectedPriorityFilterProvider = StateProvider<TaskPriority?>((ref) => null);

// Provider للمهام المفلترة حسب الأولوية
final filteredTasksProvider = FutureProvider<List<Task>>((ref) async {
  final selectedPriority = ref.watch(selectedPriorityFilterProvider);
  final taskService = ref.read(taskServiceProvider);
  
  if (selectedPriority == null) {
    return await taskService.getAllTasks();
  } else {
    return await taskService.getTasksByPriority(selectedPriority);
  }
});

// Provider لحالة التحميل
final isLoadingProvider = StateProvider<bool>((ref) => false);

// Provider لرسائل الخطأ
final errorMessageProvider = StateProvider<String?>((ref) => null);

// Provider لحالة النجاح
final successMessageProvider = StateProvider<String?>((ref) => null);

// Notifier للمهام مع إمكانية التحديث
class TasksNotifier extends StateNotifier<AsyncValue<List<Task>>> {
  TasksNotifier(this._taskService) : super(const AsyncValue.loading()) {
    loadTasks();
  }

  final TaskService _taskService;

  // تحميل المهام
  Future<void> loadTasks() async {
    state = const AsyncValue.loading();
    try {
      final tasks = await _taskService.getAllTasks();
      state = AsyncValue.data(tasks);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // إضافة مهمة جديدة
  Future<void> addTask({
    required String title,
    required String description,
    required TaskPriority priority,
    DateTime? dueDate,
  }) async {
    try {
      await _taskService.addTask(
        title: title,
        description: description,
        priority: priority,
        dueDate: dueDate,
      );
      await loadTasks(); // إعادة تحميل المهام
    } catch (error) {
      rethrow;
    }
  }

  // تحديث مهمة
  Future<void> updateTask(Task task) async {
    try {
      await _taskService.updateTask(task);
      await loadTasks(); // إعادة تحميل المهام
    } catch (error) {
      rethrow;
    }
  }

  // تبديل حالة إكمال المهمة
  Future<void> toggleTaskCompletion(Task task) async {
    try {
      await _taskService.toggleTaskCompletion(task);
      await loadTasks(); // إعادة تحميل المهام
    } catch (error) {
      rethrow;
    }
  }

  // حذف مهمة
  Future<void> deleteTask(int id) async {
    try {
      await _taskService.deleteTask(id);
      await loadTasks(); // إعادة تحميل المهام
    } catch (error) {
      rethrow;
    }
  }

  // حذف جميع المهام
  Future<void> deleteAllTasks() async {
    try {
      await _taskService.deleteAllTasks();
      await loadTasks(); // إعادة تحميل المهام
    } catch (error) {
      rethrow;
    }
  }
}

// Provider للـ TasksNotifier
final tasksNotifierProvider = StateNotifierProvider<TasksNotifier, AsyncValue<List<Task>>>((ref) {
  final taskService = ref.read(taskServiceProvider);
  return TasksNotifier(taskService);
});

// Provider لعدد المهام حسب النوع
final taskCountsProvider = FutureProvider<Map<String, int>>((ref) async {
  final taskService = ref.read(taskServiceProvider);
  final statistics = await taskService.getTaskStatistics();
  
  return {
    'total': statistics.totalTasks,
    'completed': statistics.completedTasks,
    'pending': statistics.pendingTasks,
    'urgent': statistics.urgentTasks,
    'important': statistics.importantTasks,
    'normal': statistics.normalTasks,
  };
});

// Provider لحالة عرض المهام (قائمة أم شبكة)
final taskViewModeProvider = StateProvider<TaskViewMode>((ref) => TaskViewMode.list);

enum TaskViewMode { list, grid }

// Provider لترتيب المهام
final taskSortModeProvider = StateProvider<TaskSortMode>((ref) => TaskSortMode.createdDate);

enum TaskSortMode {
  createdDate,
  priority,
  dueDate,
  alphabetical,
}

// Provider للمهام مرتبة حسب الوضع المحدد
final sortedTasksProvider = FutureProvider<List<Task>>((ref) async {
  final sortMode = ref.watch(taskSortModeProvider);
  final taskService = ref.read(taskServiceProvider);
  final tasks = await taskService.getAllTasks();
  
  switch (sortMode) {
    case TaskSortMode.priority:
      tasks.sort((a, b) => b.priority.order.compareTo(a.priority.order));
      break;
    case TaskSortMode.dueDate:
      tasks.sort((a, b) {
        if (a.dueDate == null && b.dueDate == null) return 0;
        if (a.dueDate == null) return 1;
        if (b.dueDate == null) return -1;
        return a.dueDate!.compareTo(b.dueDate!);
      });
      break;
    case TaskSortMode.alphabetical:
      tasks.sort((a, b) => a.title.compareTo(b.title));
      break;
    case TaskSortMode.createdDate:
    default:
      tasks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      break;
  }
  
  return tasks;
});
