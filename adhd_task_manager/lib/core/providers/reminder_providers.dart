import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/reminder.dart';
import '../services/reminder_service.dart';
import '../services/notification_service.dart';

// Provider لخدمة التذكيرات
final reminderServiceProvider = Provider<ReminderService>((ref) {
  return ReminderService();
});

// Provider لخدمة التنبيهات
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService.instance;
});

// Provider لجميع التذكيرات
final remindersProvider = FutureProvider<List<Reminder>>((ref) async {
  final reminderService = ref.read(reminderServiceProvider);
  return await reminderService.getAllReminders();
});

// Provider للتذكيرات المفعلة
final enabledRemindersProvider = FutureProvider<List<Reminder>>((ref) async {
  final reminderService = ref.read(reminderServiceProvider);
  return await reminderService.getEnabledReminders();
});

// Provider للتذكيرات القادمة
final upcomingRemindersProvider = FutureProvider<List<Reminder>>((ref) async {
  final reminderService = ref.read(reminderServiceProvider);
  return await reminderService.getUpcomingReminders();
});

// Provider لتذكيرات مهمة محددة
final taskRemindersProvider = FutureProvider.family<List<Reminder>, int>((ref, taskId) async {
  final reminderService = ref.read(reminderServiceProvider);
  return await reminderService.getRemindersByTaskId(taskId);
});

// Provider لإحصائيات التذكيرات
final reminderStatisticsProvider = FutureProvider<ReminderStatistics>((ref) async {
  final reminderService = ref.read(reminderServiceProvider);
  return await reminderService.getReminderStatistics();
});

// Provider لإعدادات التنبيهات
final notificationSettingsProvider = StateNotifierProvider<NotificationSettingsNotifier, NotificationSettings>((ref) {
  return NotificationSettingsNotifier();
});

// Notifier للتذكيرات مع إمكانية التحديث
class RemindersNotifier extends StateNotifier<AsyncValue<List<Reminder>>> {
  RemindersNotifier(this._reminderService) : super(const AsyncValue.loading()) {
    loadReminders();
  }

  final ReminderService _reminderService;

  // تحميل التذكيرات
  Future<void> loadReminders() async {
    state = const AsyncValue.loading();
    try {
      final reminders = await _reminderService.getAllReminders();
      state = AsyncValue.data(reminders);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // إضافة تذكير جديد
  Future<void> addReminder({
    required int taskId,
    required DateTime reminderDateTime,
    required ReminderType type,
    ReminderFrequency frequency = ReminderFrequency.once,
    String? customMessage,
  }) async {
    try {
      await _reminderService.addReminder(
        taskId: taskId,
        reminderDateTime: reminderDateTime,
        type: type,
        frequency: frequency,
        customMessage: customMessage,
      );
      await loadReminders(); // إعادة تحميل التذكيرات
    } catch (error) {
      rethrow;
    }
  }

  // تحديث تذكير
  Future<void> updateReminder(Reminder reminder) async {
    try {
      await _reminderService.updateReminder(reminder);
      await loadReminders(); // إعادة تحميل التذكيرات
    } catch (error) {
      rethrow;
    }
  }

  // تبديل حالة تفعيل التذكير
  Future<void> toggleReminderEnabled(Reminder reminder) async {
    try {
      await _reminderService.toggleReminderEnabled(reminder);
      await loadReminders(); // إعادة تحميل التذكيرات
    } catch (error) {
      rethrow;
    }
  }

  // حذف تذكير
  Future<void> deleteReminder(int id) async {
    try {
      await _reminderService.deleteReminder(id);
      await loadReminders(); // إعادة تحميل التذكيرات
    } catch (error) {
      rethrow;
    }
  }

  // حذف جميع تذكيرات مهمة
  Future<void> deleteRemindersByTaskId(int taskId) async {
    try {
      await _reminderService.deleteRemindersByTaskId(taskId);
      await loadReminders(); // إعادة تحميل التذكيرات
    } catch (error) {
      rethrow;
    }
  }
}

// Provider للـ RemindersNotifier
final remindersNotifierProvider = StateNotifierProvider<RemindersNotifier, AsyncValue<List<Reminder>>>((ref) {
  final reminderService = ref.read(reminderServiceProvider);
  return RemindersNotifier(reminderService);
});

// Notifier لإعدادات التنبيهات
class NotificationSettingsNotifier extends StateNotifier<NotificationSettings> {
  NotificationSettingsNotifier() : super(const NotificationSettings()) {
    _loadSettings();
  }

  // تحميل الإعدادات (من SharedPreferences في المستقبل)
  void _loadSettings() {
    // TODO: تحميل الإعدادات من التخزين المحلي
  }

  // تحديث إعدادات الصوت
  void updateSoundEnabled(bool enabled) {
    state = state.copyWith(soundEnabled: enabled);
    _saveSettings();
    _updateNotificationService();
  }

  // تحديث إعدادات الاهتزاز
  void updateVibrationEnabled(bool enabled) {
    state = state.copyWith(vibrationEnabled: enabled);
    _saveSettings();
    _updateNotificationService();
  }

  // تحديث إعدادات التنبيهات
  void updateNotificationsEnabled(bool enabled) {
    state = state.copyWith(notificationsEnabled: enabled);
    _saveSettings();
    _updateNotificationService();
  }

  // حفظ الإعدادات (في SharedPreferences في المستقبل)
  void _saveSettings() {
    // TODO: حفظ الإعدادات في التخزين المحلي
  }

  // تحديث خدمة التنبيهات
  void _updateNotificationService() {
    NotificationService.instance.updateSettings(
      soundEnabled: state.soundEnabled,
      vibrationEnabled: state.vibrationEnabled,
      notificationsEnabled: state.notificationsEnabled,
    );
  }
}

// فئة إعدادات التنبيهات
class NotificationSettings {
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool notificationsEnabled;

  const NotificationSettings({
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.notificationsEnabled = true,
  });

  NotificationSettings copyWith({
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? notificationsEnabled,
  }) {
    return NotificationSettings(
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationSettings &&
        other.soundEnabled == soundEnabled &&
        other.vibrationEnabled == vibrationEnabled &&
        other.notificationsEnabled == notificationsEnabled;
  }

  @override
  int get hashCode {
    return Object.hash(
      soundEnabled,
      vibrationEnabled,
      notificationsEnabled,
    );
  }
}

// Provider لحالة التحميل
final reminderLoadingProvider = StateProvider<bool>((ref) => false);

// Provider لرسائل الخطأ
final reminderErrorProvider = StateProvider<String?>((ref) => null);

// Provider لرسائل النجاح
final reminderSuccessProvider = StateProvider<String?>((ref) => null);

// Provider للتذكيرات المفلترة حسب النوع
final remindersByTypeProvider = FutureProvider.family<List<Reminder>, ReminderType>((ref, type) async {
  final reminders = await ref.watch(remindersProvider.future);
  return reminders.where((reminder) => reminder.type == type).toList();
});

// Provider للتذكيرات المفلترة حسب التكرار
final remindersByFrequencyProvider = FutureProvider.family<List<Reminder>, ReminderFrequency>((ref, frequency) async {
  final reminders = await ref.watch(remindersProvider.future);
  return reminders.where((reminder) => reminder.frequency == frequency).toList();
});

// Provider لعدد التذكيرات حسب النوع
final reminderCountByTypeProvider = FutureProvider<Map<ReminderType, int>>((ref) async {
  final statistics = await ref.watch(reminderStatisticsProvider.future);
  return statistics.remindersByType;
});

// Provider لعدد التذكيرات حسب التكرار
final reminderCountByFrequencyProvider = FutureProvider<Map<ReminderFrequency, int>>((ref) async {
  final statistics = await ref.watch(reminderStatisticsProvider.future);
  return statistics.remindersByFrequency;
});

// Provider لحالة أذونات التنبيهات
final notificationPermissionProvider = FutureProvider<bool>((ref) async {
  final notificationService = ref.read(notificationServiceProvider);
  return await notificationService.areNotificationsEnabled();
});
