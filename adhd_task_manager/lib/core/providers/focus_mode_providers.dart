import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/focus_mode_service.dart';
import '../services/smart_notification_service.dart';
import '../models/focus_mode.dart';
import '../models/smart_notification.dart';

// Provider لخدمة وضع التركيز
final focusModeServiceProvider = Provider<FocusModeService>((ref) {
  return FocusModeService();
});

// Provider لخدمة التنبيهات الذكية
final smartNotificationServiceProvider = Provider<SmartNotificationService>((ref) {
  return SmartNotificationService();
});

// Notifier لإدارة أوضاع التركيز
class FocusModesNotifier extends AsyncNotifier<List<FocusMode>> {
  late final FocusModeService _focusModeService;

  @override
  Future<List<FocusMode>> build() async {
    _focusModeService = ref.read(focusModeServiceProvider);
    await _focusModeService.initialize();
    return await _loadFocusModes();
  }

  /// تحميل جميع أوضاع التركيز
  Future<List<FocusMode>> _loadFocusModes() async {
    try {
      return await _focusModeService.getAllFocusModes();
    } catch (e) {
      throw Exception('فشل في تحميل أوضاع التركيز: $e');
    }
  }

  /// إنشاء وضع تركيز جديد
  Future<void> createFocusMode(FocusMode focusMode) async {
    try {
      await _focusModeService.createFocusMode(focusMode);
      await refresh();
    } catch (e) {
      throw Exception('فشل في إنشاء وضع التركيز: $e');
    }
  }

  /// تحديث وضع التركيز
  Future<void> updateFocusMode(FocusMode focusMode) async {
    try {
      await _focusModeService.updateFocusMode(focusMode);
      await refresh();
      
      // تحديث الوضع النشط إذا كان هو المحدث
      if (_focusModeService.activeFocusMode?.id == focusMode.id) {
        ref.invalidate(activeFocusModeProvider);
      }
    } catch (e) {
      throw Exception('فشل في تحديث وضع التركيز: $e');
    }
  }

  /// حذف وضع التركيز
  Future<void> deleteFocusMode(int id) async {
    try {
      await _focusModeService.deleteFocusMode(id);
      await refresh();
      
      // تحديث الوضع النشط إذا كان هو المحذوف
      if (_focusModeService.activeFocusMode?.id == id) {
        ref.invalidate(activeFocusModeProvider);
      }
    } catch (e) {
      throw Exception('فشل في حذف وضع التركيز: $e');
    }
  }

  /// تفعيل وضع التركيز
  Future<void> activateFocusMode(FocusMode focusMode, {int? duration}) async {
    try {
      await _focusModeService.activateFocusMode(focusMode, duration: duration);
      ref.invalidate(activeFocusModeProvider);
      ref.invalidate(focusModeStatusProvider);
    } catch (e) {
      throw Exception('فشل في تفعيل وضع التركيز: $e');
    }
  }

  /// إلغاء تفعيل وضع التركيز
  Future<void> deactivateFocusMode() async {
    try {
      await _focusModeService.deactivateFocusMode();
      ref.invalidate(activeFocusModeProvider);
      ref.invalidate(focusModeStatusProvider);
    } catch (e) {
      throw Exception('فشل في إلغاء تفعيل وضع التركيز: $e');
    }
  }

  /// إعادة تحميل أوضاع التركيز
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadFocusModes());
  }
}

// Provider لأوضاع التركيز
final focusModesProvider = AsyncNotifierProvider<FocusModesNotifier, List<FocusMode>>(() {
  return FocusModesNotifier();
});

// Provider لوضع التركيز النشط
final activeFocusModeProvider = Provider<FocusMode?>((ref) {
  final service = ref.watch(focusModeServiceProvider);
  return service.activeFocusMode;
});

// Provider لحالة وضع التركيز
final focusModeStatusProvider = Provider<FocusModeStatus>((ref) {
  final service = ref.watch(focusModeServiceProvider);
  final activeMode = service.activeFocusMode;
  
  if (activeMode == null) {
    return FocusModeStatus.inactive;
  }
  
  if (activeMode.isExpired) {
    return FocusModeStatus.expired;
  }
  
  return FocusModeStatus.active;
});

// Provider للوقت المتبقي في وضع التركيز
final focusModeRemainingTimeProvider = Provider<Duration?>((ref) {
  final service = ref.watch(focusModeServiceProvider);
  return service.remainingTime;
});

// Provider لاقتراح وضع التركيز
final focusModeSuggestionProvider = FutureProvider<FocusMode?>((ref) async {
  final service = ref.read(focusModeServiceProvider);
  return await service.suggestFocusMode(
    currentTime: DateTime.now(),
  );
});

// Notifier لإدارة التنبيهات الذكية
class SmartNotificationsNotifier extends AsyncNotifier<List<SmartNotification>> {
  late final SmartNotificationService _notificationService;

  @override
  Future<List<SmartNotification>> build() async {
    _notificationService = ref.read(smartNotificationServiceProvider);
    await _notificationService.initialize();
    return await _loadNotifications();
  }

  /// تحميل التنبيهات غير المقروءة
  Future<List<SmartNotification>> _loadNotifications() async {
    try {
      return await _notificationService.getUnreadNotifications();
    } catch (e) {
      return [];
    }
  }

  /// جدولة تنبيه جديد
  Future<void> scheduleNotification(SmartNotification notification) async {
    try {
      await _notificationService.scheduleNotification(notification);
      await refresh();
    } catch (e) {
      throw Exception('فشل في جدولة التنبيه: $e');
    }
  }

  /// تحديد التنبيه كمقروء
  Future<void> markAsRead(int notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
      await refresh();
    } catch (e) {
      throw Exception('فشل في تحديد التنبيه كمقروء: $e');
    }
  }

  /// إرسال تنبيه تحفيزي
  Future<void> sendMotivationalNotification() async {
    try {
      await _notificationService.sendMotivationalNotification();
      await refresh();
    } catch (e) {
      throw Exception('فشل في إرسال التنبيه التحفيزي: $e');
    }
  }

  /// تنظيف التنبيهات القديمة
  Future<void> cleanupOldNotifications() async {
    try {
      await _notificationService.cleanupOldNotifications();
      await refresh();
    } catch (e) {
      throw Exception('فشل في تنظيف التنبيهات القديمة: $e');
    }
  }

  /// إعادة تحميل التنبيهات
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadNotifications());
  }
}

// Provider للتنبيهات الذكية
final smartNotificationsProvider = AsyncNotifierProvider<SmartNotificationsNotifier, List<SmartNotification>>(() {
  return SmartNotificationsNotifier();
});

// Provider لعدد التنبيهات غير المقروءة
final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notificationsAsync = ref.watch(smartNotificationsProvider);
  return notificationsAsync.when(
    data: (notifications) => notifications.length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Provider لإعدادات التنبيهات الذكية
final smartNotificationSettingsProvider = StateProvider<Map<String, bool>>((ref) {
  final service = ref.read(smartNotificationServiceProvider);
  return service.currentSettings;
});

// Provider للتحكم في وضع التركيز والتنبيهات
final focusModeControllerProvider = Provider<FocusModeController>((ref) {
  return FocusModeController(ref);
});

/// تحكم في وضع التركيز والتنبيهات
class FocusModeController {
  final Ref _ref;
  
  FocusModeController(this._ref);

  /// تفعيل وضع تركيز مع تنبيه
  Future<bool> activateFocusModeWithNotification(
    FocusMode focusMode, {
    int? duration,
  }) async {
    try {
      final notifier = _ref.read(focusModesProvider.notifier);
      await notifier.activateFocusMode(focusMode, duration: duration);
      
      // إرسال تنبيه تأكيد
      final smartNotifier = _ref.read(smartNotificationsProvider.notifier);
      await smartNotifier.scheduleNotification(
        SmartNotification.create(
          title: 'تم تفعيل وضع التركيز',
          message: 'بدأت جلسة "${focusMode.name}". ركز على أهدافك!',
          type: SmartNotificationType.focusSuggestion,
          scheduledTime: DateTime.now(),
        ),
      );
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إلغاء تفعيل وضع التركيز مع تنبيه
  Future<void> deactivateFocusModeWithNotification() async {
    try {
      final activeMode = _ref.read(activeFocusModeProvider);
      if (activeMode == null) return;
      
      final notifier = _ref.read(focusModesProvider.notifier);
      await notifier.deactivateFocusMode();
      
      // إرسال تنبيه إكمال
      final smartNotifier = _ref.read(smartNotificationsProvider.notifier);
      await smartNotifier.scheduleNotification(
        SmartNotification.create(
          title: 'انتهى وضع التركيز',
          message: 'انتهت جلسة "${activeMode.name}". أحسنت!',
          type: SmartNotificationType.goalAchievement,
          scheduledTime: DateTime.now(),
        ),
      );
    } catch (e) {
      // تسجيل الخطأ
    }
  }

  /// اقتراح وضع تركيز ذكي
  Future<void> suggestSmartFocusMode() async {
    try {
      final suggestion = await _ref.read(focusModeSuggestionProvider.future);
      if (suggestion != null) {
        final smartNotifier = _ref.read(smartNotificationsProvider.notifier);
        await smartNotifier.scheduleNotification(
          SmartNotification.focusModesuggestion(
            reason: 'بناءً على الوقت الحالي ونشاطك',
            suggestedMode: suggestion.name,
          ),
        );
      }
    } catch (e) {
      // تسجيل الخطأ
    }
  }

  /// تحديث إعدادات التنبيهات
  void updateNotificationSettings(Map<String, bool> settings) {
    final service = _ref.read(smartNotificationServiceProvider);
    service.updateSettings(
      breakReminders: settings['breakReminders'],
      usageWarnings: settings['usageWarnings'],
      goalNotifications: settings['goalNotifications'],
      focusSuggestions: settings['focusSuggestions'],
    );
    
    _ref.read(smartNotificationSettingsProvider.notifier).state = settings;
  }

  /// تحديث جميع البيانات
  Future<void> refreshAll() async {
    _ref.invalidate(focusModesProvider);
    _ref.invalidate(smartNotificationsProvider);
    _ref.invalidate(activeFocusModeProvider);
    _ref.invalidate(focusModeStatusProvider);
  }
}

/// حالات وضع التركيز
enum FocusModeStatus {
  inactive('غير نشط'),
  active('نشط'),
  expired('منتهي الصلاحية');

  const FocusModeStatus(this.displayName);
  final String displayName;
}
