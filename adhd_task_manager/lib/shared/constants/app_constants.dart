class AppConstants {
  // أسماء الشاشات
  static const String appName = 'إنجاز - مدير المهام';
  static const String dashboardTitle = 'لوحة القيادة';
  static const String tasksTitle = 'قائمة المهام';
  static const String focusTimerTitle = 'مؤقت التركيز';
  static const String habitsTitle = 'تتبع العادات';
  static const String settingsTitle = 'الإعدادات';
  
  // النصوص العامة
  static const String addTask = 'إضافة مهمة';
  static const String editTask = 'تعديل مهمة';
  static const String deleteTask = 'حذف مهمة';
  static const String completeTask = 'إكمال المهمة';
  static const String taskTitle = 'عنوان المهمة';
  static const String taskDescription = 'وصف المهمة';
  static const String priority = 'الأولوية';
  static const String dueDate = 'تاريخ الاستحقاق';
  
  // مستويات الأولوية
  static const String urgentPriority = 'عاجل';
  static const String importantPriority = 'مهم';
  static const String normalPriority = 'عادي';
  
  // أيقونات التنقل
  static const String dashboardIcon = 'dashboard';
  static const String tasksIcon = 'task_alt';
  static const String timerIcon = 'timer';
  static const String habitsIcon = 'trending_up';
  static const String settingsIcon = 'settings';
  
  // رسائل نائبة
  static const String noTasksMessage = 'لا توجد مهام حالياً';
  static const String addFirstTaskMessage = 'أضف مهمتك الأولى للبدء';
  static const String noHabitsMessage = 'لا توجد عادات للتتبع';
  static const String addFirstHabitMessage = 'أضف عادة جديدة للبدء في التتبع';
  
  // إعدادات المؤقت الافتراضية
  static const int defaultWorkDuration = 25; // دقيقة
  static const int defaultBreakDuration = 5; // دقيقة
  static const int defaultLongBreakDuration = 15; // دقيقة
  
  // أبعاد التصميم
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
}
