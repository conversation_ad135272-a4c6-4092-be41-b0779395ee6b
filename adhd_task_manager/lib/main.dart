import 'package:flutter/material.dart';
import 'screens/home_screen.dart';
import 'shared/themes/app_theme.dart';
import 'shared/constants/app_constants.dart';

void main() {
  runApp(const ADHDTaskManagerApp());
}

class ADHDTaskManagerApp extends StatelessWidget {
  const ADHDTaskManagerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
      // دعم اللغة العربية
      locale: const Locale('ar', 'SA'),
    );
  }
}
