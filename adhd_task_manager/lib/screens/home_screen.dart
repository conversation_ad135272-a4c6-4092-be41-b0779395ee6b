import 'package:flutter/material.dart';
import '../features/dashboard/dashboard_screen.dart';
import '../features/tasks/tasks_screen.dart';
import '../features/pomodoro/pomodoro_screen.dart';
import '../features/habits/habits_screen.dart';
import '../features/settings/settings_screen.dart';
import '../shared/themes/app_theme.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const TasksScreen(),
    const PomodoroScreen(),
    const HabitsScreen(),
    const SettingsScreen(),
  ];

  final List<BottomNavigationBarItem> _navigationItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.dashboard),
      label: 'الرئيسية',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.task_alt),
      label: 'المهام',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.timer),
      label: 'التركيز',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.trending_up),
      label: 'العادات',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.settings),
      label: 'الإعدادات',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
        items: _navigationItems,
      ),
    );
  }
}
