import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/dashboard/dashboard_screen.dart';
import '../features/tasks/tasks_screen.dart';
import '../features/pomodoro/pomodoro_screen.dart';
import '../features/habits/habits_screen.dart';
import '../features/settings/settings_screen.dart';
import '../shared/themes/app_theme.dart';

// Provider لفهرس الصفحة الحالية
final currentIndexProvider = StateProvider<int>((ref) => 0);

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  static const List<Widget> _screens = [
    DashboardScreen(),
    TasksScreen(),
    PomodoroScreen(),
    HabitsScreen(),
    SettingsScreen(),
  ];

  static const List<BottomNavigationBarItem> _navigationItems = [
    BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'الرئيسية'),
    BottomNavigationBarItem(icon: Icon(Icons.task_alt), label: 'المهام'),
    BottomNavigationBarItem(icon: Icon(Icons.timer), label: 'التركيز'),
    BottomNavigationBarItem(icon: Icon(Icons.trending_up), label: 'العادات'),
    BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'الإعدادات'),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(currentIndexProvider);

    return Scaffold(
      body: IndexedStack(index: currentIndex, children: _screens),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: (index) {
          ref.read(currentIndexProvider.notifier).state = index;
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
        items: _navigationItems,
      ),
    );
  }
}
