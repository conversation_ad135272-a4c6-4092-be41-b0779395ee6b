{"buildFiles": ["/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/m1m296q2/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/augment-projects/Enjaz/adhd_task_manager/android/app/.cxx/Debug/m1m296q2/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}